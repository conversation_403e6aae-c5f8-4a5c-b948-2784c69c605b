feign:
  sentinel:
    enabled: false
spring:
  cloud:
    sentinel:
      enabled: false
      transport:
        dashboard: 192.168.1.*:8858
        port: 8719
        client-ip: ${spring.cloud.nacos.discovery.ip}
      eager: true
      datasource:
        flow:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            group-id: ${spring.cloud.nacos.discovery.group}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: ${spring.application.name}-sentinel-flow-rules.json
            rule-type: flow
        degrade:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            group-id: ${spring.cloud.nacos.discovery.group}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: ${spring.application.name}-sentinel-degrade-rules.json
            rule-type: degrade
        param-flow:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            group-id: ${spring.cloud.nacos.discovery.group}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: ${spring.application.name}-sentinel-param-flow-rules.json
            rule-type: param-flow
        system:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            group-id: ${spring.cloud.nacos.discovery.group}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: ${spring.application.name}-sentinel-system-rules.json
            rule-type: system
        authority:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            group-id: ${spring.cloud.nacos.discovery.group}
            namespace: ${spring.cloud.nacos.discovery.namespace}
            data-id: ${spring.application.name}-sentinel-authority-rules.json
            rule-type: authority
         # gateway 才需要以下配置
#        gw-flow:
#          nacos:
#            server-addr: ${spring.cloud.nacos.discovery.server-addr}
#            group-id: ${spring.cloud.nacos.discovery.group}
#            namespace: ${spring.cloud.nacos.discovery.namespace}
#            data-id: ${spring.application.name}-sentinel-gw-flow-rules.json
#            rule-type: gw-flow
#        gw-api-group:
#          nacos:
#            server-addr: ${spring.cloud.nacos.discovery.server-addr}
#            group-id: ${spring.cloud.nacos.discovery.group}
#            namespace: ${spring.cloud.nacos.discovery.namespace}
#            data-id: ${spring.application.name}-sentinel-gw-api-group-rules.json
#            rule-type: gw-api-group