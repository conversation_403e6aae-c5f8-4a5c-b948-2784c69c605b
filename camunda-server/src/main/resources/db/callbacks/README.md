# Flyway回调脚本

本目录用于存放Flyway的回调脚本。回调脚本允许在Flyway执行各个阶段前后添加自定义逻辑。

## 用途

回调脚本可用于许多场景，例如：
- 迁移前后的数据备份
- 在迁移过程中添加日志记录
- 在迁移完成后进行数据验证
- 向监控系统发送通知

## 命名规范

回调脚本的命名必须遵循以下规则：

```
<回调类型>__<描述>.sql
```

支持的回调类型包括：
- `beforeMigrate` - 迁移开始前执行
- `beforeEachMigrate` - 每个迁移脚本执行前执行
- `afterEachMigrate` - 每个迁移脚本执行后执行
- `afterMigrate` - 迁移完成后执行
- `beforeValidate` - 验证开始前执行
- `afterValidate` - 验证完成后执行
- `beforeBaseline` - 基线操作前执行
- `afterBaseline` - 基线操作后执行
- `beforeClean` - 清理操作前执行
- `afterClean` - 清理操作后执行
- `beforeInfo` - 信息操作前执行
- `afterInfo` - 信息操作后执行
- `beforeRepair` - 修复操作前执行
- `afterRepair` - 修复操作后执行

例如：
- `beforeMigrate__create_backup.sql`
- `afterMigrate__validate_data.sql`

## 使用注意事项

1. 回调脚本在对应的迁移阶段自动执行
2. 回调脚本应当处理可能的错误并提供适当的错误处理机制
3. 对于关键环境，确保回调脚本经过充分测试 