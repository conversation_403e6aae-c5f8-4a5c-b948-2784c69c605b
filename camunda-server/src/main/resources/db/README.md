# Flyway 数据库迁移说明

## 概述

本项目使用 Flyway 进行数据库版本控制和迁移管理。Flyway 是一个开源的数据库版本控制工具，它能够帮助我们管理数据库结构变更和初始数据导入。

## 脚本目录结构

```
db/
  |- versioned/           # 版本化的SQL脚本
      |- V20250411__init_db.sql  # 初始化数据库脚本
      |- V<版本号>__<描述>.sql    # 后续版本的脚本
  |- callbacks/           # 回调脚本（可选）
  |- repeatable/          # 可重复执行的脚本（可选）
```

## 命名规范

### 版本化迁移脚本

版本化迁移脚本必须遵循以下命名规范：

```
V<版本号>__<描述>.sql
```

- `V` 前缀（大写）表示这是一个版本化迁移
- `<版本号>` 通常使用 `年月日` 格式（例如：`20250411`）
- `__` 双下划线用于分隔版本号和描述
- `<描述>` 是对迁移内容的简短描述，使用下划线分隔单词

示例：`V20250412__add_user_table.sql`

### 可重复迁移脚本

可重复迁移脚本放在 `repeatable` 目录中，命名规范：

```
R__<描述>.sql
```

这些脚本在每次应用启动时都会检查其内容是否发生变化，如有变化则重新执行。

## 使用说明

### 添加新的迁移脚本

1. 在 `versioned` 目录中创建新的 SQL 脚本文件，遵循命名规范
2. 编写 SQL 语句，确保语法正确无误
3. 提交代码后，应用重启时会自动执行新增的脚本

### 注意事项

1. **版本号必须递增**：新脚本的版本号必须大于已执行过的所有脚本版本号
2. **不要修改已执行的脚本**：已执行的脚本不应被修改，如需修改应创建新脚本进行更正
3. **建议使用事务**：在脚本中使用事务包裹 SQL 语句，确保要么全部执行成功，要么全部失败
4. **保持幂等性**：编写脚本时尽量考虑幂等性，避免重复执行造成错误

## 配置说明

Flyway 的配置在 `application-db.yml` 文件中：

```yaml
spring:
  flyway:
    enabled: true                    # 启用 Flyway
    baseline-on-migrate: true        # 在非空数据库上执行迁移时，自动执行基线迁移
    locations: classpath:db/versioned # 脚本位置
    schemas: open_care_camunda       # 要管理的 schema
    table: flyway_schema_history     # Flyway 元数据表名
    validate-on-migrate: true        # 迁移前验证
    out-of-order: false              # 是否允许无序执行迁移
```

## 命令行工具

Flyway 也提供了命令行工具，可以在项目外部执行数据库迁移：

```bash
# 安装 Flyway CLI
brew install flyway

# 执行迁移
flyway -url=************************************ -user=username -password=password migrate
``` 