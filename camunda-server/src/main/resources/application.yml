server:
  port: 8380

spring:
  application:
    name: camunda-service
  profiles:
    active: dev
    include:
      - seata
      - sentinel
      - auth
      - db
      - open-care

  # 添加Spring Boot DevTools配置
#  devtools:
#    restart:
#      enabled: true
#      additional-paths: src/main/java
#      exclude: static/**,public/**,WEB-INF/**
#    livereload:
#      enabled: true

  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: host.docker.internal:8848
        namespace: Sunshine
        group: dev
      config:
        enabled: false
        server-addr: ${spring.cloud.nacos.discovery.server-addr}:8848
        file-extension: yaml
  #        namespace: Sunshine
  #        group: dev
  data:
    redis:
      host: host.docker.internal
      port: 6379
      password: opencare
      #    sentinel:
      #      nodes: *************:26380,*************:26381,*************:26382
      #      master: mymaster
      lettuce:
        pool:
          #最大连接数
          max-active: 200
          #最大阻塞等待时间(负数表示没限制)
          max-wait: -1ms
          #最大空闲
          max-idle: 10
          #最小空闲
          min-idle: 0
        #连接超时时间
      timeout: 2000ms
      database: 3

# Camunda配置
camunda:
  bpm:
    # 设置历史数据保留时间为30天
    enforce-history-time-to-live: false
    history-time-to-live: P90D
    admin-user:
      id: admin
      password: admin
      firstName: Demo
      lastName: User
    filter:
      create: All Tasks
    # 禁用Camunda遥测数据收集
    telemetry:
      enabled: false
    cors:
      enabled: true
      allowed-origins: "*"
    history-level: "full"
    webapp:
      application-path: /camunda

management:
  endpoints:
    web:
      exposure:
        include: [ "conditions","health", "info", "prometheus" ]
  endpoint:
    conditions:
      enabled: true
    health:
      show-details: always

# Feign客户端配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
  # 如果使用了Hystrix，设置启用它
  hystrix:
    enabled: false

# 日志配置
logging:
  level:
    com.open_care.api.client: DEBUG
    org.camunda.bpm.engine.persistence: DEBUG
    org.camunda.bpm.engine.variable: DEBUG

myconfig:
  bpmnFilePathPrefix: /file
  bpmnFilePathPostfix: /process/
  url: http://localhost:9999
gatewayServer:
  url: http://localhost:8301
messageServer:
  url: http://localhost:8306
configurationServer:
  url: http://localhost:8302
