package com.open_care.camunda;

import com.open_care.BpmConfiguration;
import org.camunda.bpm.spring.boot.starter.annotation.EnableProcessApplication;
import org.camunda.bpm.spring.boot.starter.webapp.CamundaBpmWebappAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableProcessApplication
@EnableAsync
@SpringBootApplication
@ComponentScan(basePackages = {"com.open_care"})
@EnableJpaRepositories(basePackages = {"org.camunda"})
@EntityScan(basePackages = {"org.camunda"})
@EnableConfigurationProperties({org.camunda.bpm.spring.boot.starter.property.CamundaBpmProperties.class})
@Import({CamundaBpmWebappAutoConfiguration.class})
public class CamundaServerApplication {


    public static void main(String[] args) {
        try {
            SpringApplication.run(CamundaServerApplication.class, args);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


} 