//package com.open_care.camunda.config;
//
//import org.flywaydb.core.Flyway;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import org.springframework.boot.autoconfigure.flyway.FlywayMigrationStrategy;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//
//
///**
// * Flyway配置类
// * 负责管理数据库脚本迁移
// *
// * <AUTHOR>
// * @date 2025/4/11
// */
//@Configuration
//public class FlywayConfig {
//
//    private static final Logger log = LoggerFactory.getLogger(FlywayConfig.class);
//
//    /**
//     * 自定义Flyway迁移策略
//     * 在迁移前后添加日志，方便问题排查
//     */
//    @Bean
//    public FlywayMigrationStrategy flywayMigrationStrategy() {
//        return flyway -> {
//            log.info("开始执行Flyway数据库迁移...");
//            try {
//                // 执行迁移
//                flyway.migrate();
//                log.info("Flyway数据库迁移完成！");
//            } catch (Exception e) {
//                log.error("Flyway数据库迁移失败!", e);
//                throw e;
//            }
//        };
//    }
//
//    /**
//     * 配置Flyway实例
//     * 这里可以覆盖application.yml中的配置
//     * 也可以在这里手动执行迁移
//     */
//    /*
//    @Bean
//    public Flyway flyway(DataSource dataSource) {
//        Flyway flyway = Flyway.configure()
//                .dataSource(dataSource)
//                .baselineOnMigrate(true)
//                .locations("classpath:db/versioned")
//                .schemas("open_care_camunda")
//                .defaultSchema("open_care_camunda")
//                .table("flyway_schema_history")
//                .validateOnMigrate(true)
//                .outOfOrder(false)
//                .load();
//
//        // 如果需要手动执行迁移，可以取消注释下面的代码
//        // flyway.migrate();
//
//        return flyway;
//    }
//    */
//}