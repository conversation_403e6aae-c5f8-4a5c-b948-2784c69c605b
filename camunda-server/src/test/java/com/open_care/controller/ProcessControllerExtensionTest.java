package com.open_care.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessController扩展功能集成测试
 * 扩展测试：其他重要接口
 * 测试序号：033-060
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessControllerExtensionTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据
    protected static String taskId;
    protected static String processInstanceId;
    protected static String processDefinitionKey;

    // 获取其他测试类的共享数据
    public static String getTaskId() {
        if (taskId != null) {
            return taskId;
        }
        return ProcessControllerTaskTest.getTaskId();
    }

    public static String getProcessInstanceId() {
        if (processInstanceId != null) {
            return processInstanceId;
        }
        return ProcessControllerInstanceTest.getProcessInstanceId();
    }

    public static String getProcessDefinitionKey() {
        if (processDefinitionKey != null) {
            return processDefinitionKey;
        }
        return ProcessControllerInstanceTest.getProcessDefinitionKey();
    }

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    /**
     * 测试序号 033：根据任务ID查询身份实体
     */
    @Test
    @Order(33)
    void testQueryIdentityEntityByTaskId_Success() throws Exception {
        String currentTaskId = getTaskId();
        if (currentTaskId == null) {
            log.warn("无任务ID，跳过身份实体查询测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/queryIdentityEntityByTaskId/" + currentTaskId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试033通过: 根据任务ID查询身份实体成功");
    }

    /**
     * 测试序号 034：修改任务优先级
     */
    @Test
    @Order(34)
    void testModifyTaskPriorityByTaskId_Success() throws Exception {
        String currentTaskId = getTaskId();
        if (currentTaskId == null) {
            log.warn("无任务ID，跳过修改任务优先级测试");
            return;
        }
        
        int priority = 50;
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = restTemplate.exchange(
                baseUrl + "/modifyTaskPriorityByTaskId/" + currentTaskId + "/" + priority,
                org.springframework.http.HttpMethod.PUT,
                null,
                Object.class
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试034通过: 修改任务优先级成功");
    }

    /**
     * 测试序号 035：设置任务变量
     */
    @Test
    @Order(35)
    void testSetTaskVariables_Success() throws Exception {
        String currentTaskId = getTaskId();
        if (currentTaskId == null) {
            log.warn("无任务ID，跳过设置任务变量测试");
            return;
        }
        
        // 准备数据
        Map<String, Object> variables = new HashMap<>();
        variables.put("testVar", "testValue");
        variables.put("priority", "HIGH");
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/setTaskVariables/" + currentTaskId,
                variables,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试035通过: 设置任务变量成功");
    }

    /**
     * 测试序号 036：获取用户当前任务数量
     */
    @Test
    @Order(36)
    void testGetCurrentTasksCountByUser_Success() throws Exception {
        String userId = USER_ID;
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getCurrentTasksCountByUser/" + userId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试036通过: 获取用户当前任务数量成功");
    }

    /**
     * 测试序号 037：根据流程实例获取当前任务
     */
    @Test
    @Order(37)
    void testGetCurrentTasksByProcInst_Success() throws Exception {
        String currentProcessInstanceId = getProcessInstanceId();
        if (currentProcessInstanceId == null) {
            log.warn("无流程实例ID，跳过获取当前任务测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getCurrentTasksByProcInst/" + currentProcessInstanceId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试037通过: 根据流程实例获取当前任务成功");
    }

    /**
     * 测试序号 038：获取所有流程实例
     */
    @Test
    @Order(38)
    void testGetProcessInstances_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getProcessInstances",
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试038通过: 获取所有流程实例成功");
    }

    /**
     * 测试序号 039：获取流程定义XML
     */
    @Test
    @Order(39)
    void testGetProcessDefinitionXml_Success() throws Exception {
        String processDefId = "test_process_def_001";
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getProcessDefinition/" + processDefId + "/xml",
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试039通过: 获取流程定义XML成功");
    }

    /**
     * 测试序号 040：批量完成任务
     */
    @Test
    @Order(40)
    void testBatchCompleteTasks_Success() throws Exception {
        // 准备数据
        Map<String, Object> batchData = new HashMap<>();
        batchData.put("taskIds", Arrays.asList("task1", "task2"));
        batchData.put("variables", Map.of("batchComplete", true));
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/batchCompleteTasks",
                batchData,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试040通过: 批量完成任务成功");
    }
} 