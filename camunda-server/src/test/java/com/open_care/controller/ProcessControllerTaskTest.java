package com.open_care.controller;

import com.open_care.api.common.dto.OcResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessController任务管理集成测试
 * 测试第六、七、八、九、十层：任务管理生命周期
 * 测试序号：018-027
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("ProcessController 任务集成测试")
public class ProcessControllerTaskTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据
    protected static String taskId;
    protected static String processInstanceId;

    // 获取其他测试类的共享数据
    public static String getProcessInstanceId() {
        if (processInstanceId != null) {
            return processInstanceId;
        }
        // 尝试从其他测试类获取
        return ProcessControllerInstanceTest.getProcessInstanceId();
    }

    // 提供当前任务ID给其他测试类
    public static String getTaskId() {
        return taskId;
    }

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    // ===================== 第六层：任务获取层 (依赖流程实例启动) =====================

    /**
     * 测试序号 018：根据任务ID获取任务
     * 前置条件：startProcessInstanceByKey() 成功 + 任务已创建
     */
    @Test
    @Order(18)
    @DisplayName("018-根据任务ID获取任务")
    void testGetTaskByTaskId_Success() throws Exception {
        // 如果还没有任务ID，先创建一个流程实例获取任务
        if (taskId == null) {
            // 创建一个简单的流程实例来获取任务
            processInstanceId = createTestProcessInstance();
            taskId = getFirstTaskFromProcessInstance(processInstanceId);
        }
        
        if (taskId == null) {
            log.warn("无法获取任务ID，跳过任务相关测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performGetForOcResponse(
                "/get-task-by-taskId/" + taskId,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试018通过: 成功根据任务ID获取任务，taskId: {}", taskId);
    }

    /**
     * 测试序号 019：根据用户查询当前任务
     * 前置条件：startProcessInstanceByKey() 成功 + 任务已创建
     */
    @Test
    @Order(19)
    @DisplayName("019-根据用户查询当前任务")
    void testGetCurrentTasksByUser_Success() throws Exception {
        String userId = USER_ID;
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/get-current-tasks-by-user/" + userId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试019通过: 成功根据用户查询当前任务，userId: {}", userId);
    }

    /**
     * 测试序号 020：分页查询用户任务
     * 前置条件：startProcessInstanceByKey() 成功 + 任务已创建
     */
    @Test
    @Order(20)
    @DisplayName("020-分页查询用户任务")
    void testFindUserTasksWithPagination_Success() throws Exception {
        // 准备数据
        TaskQueryRequestDTO requestDTO = TaskQueryRequestDTO.builder()
                .userId(USER_ID)
                .processInstanceId(getProcessInstanceId())
                .build();
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/findUserTasksWithPagination",
                requestDTO,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试020通过: 成功分页查询用户任务");
    }

    // ===================== 第七层：任务候选人设置层 (依赖任务存在) =====================

    /**
     * 测试序号 021：设置任务候选人
     * 前置条件：getTaskByTaskId() 成功
     */
    @Test
    @Order(21)
    @DisplayName("021-设置任务候选人")
    void testCandidateTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过候选人设置测试");
            return;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("candidateUsers", Arrays.asList(USER_ID, "user2"));
        params.put("candidateGroups", Arrays.asList("group1", "group2"));
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/candidate-task/" + taskId,
                params,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试021通过: 成功设置任务候选人，taskId: {}", taskId);
    }

    /**
     * 测试序号 022：分配任务
     * 前置条件：candidateTask() 成功
     */
    @Test
    @Order(22)
    @DisplayName("022-分配任务")
    void testAssigneeTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过任务分配测试");
            return;
        }
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("assignee", USER_ID);
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/assignee-task/" + taskId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试022通过: 成功分配任务，taskId: {}, assignee: {}", taskId, USER_ID);
    }

    /**
     * 测试序号 023：认领任务
     * 前置条件：assigneeTask() 成功
     */
    @Test
    @Order(23)
    @DisplayName("023-认领任务")
    void testClaimTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过任务认领测试");
            return;
        }
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("userId", USER_ID);
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/claim-task/" + taskId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试023通过: 成功认领任务，taskId: {}, userId: {}", taskId, USER_ID);
    }

    /**
     * 测试序号 024：取消认领任务
     * 前置条件：claimTask() 成功
     */
    @Test
    @Order(24)
    @DisplayName("024-取消认领任务")
    void testUnclaimTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过取消认领测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performPostForOcResponse(
                "/unclaim-task/" + taskId,
                null,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试024通过: 成功取消认领任务，taskId: {}", taskId);
    }

    /**
     * 测试序号 025：委派任务
     * 前置条件：unclaimTask() 成功
     */
    @Test
    @Order(25)
    @DisplayName("025-委派任务")
    void testDelegateTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过任务委派测试");
            return;
        }
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("userId", "delegate_user");
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/delegate-task/" + taskId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试025通过: 成功委派任务，taskId: {}", taskId);
    }

    /**
     * 测试序号 026：解决委派任务
     * 前置条件：delegateTask() 成功
     */
    @Test
    @Order(26)
    @DisplayName("026-解决委派任务")
    void testResolveTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过解决委派测试");
            return;
        }
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("comment", "委派任务已处理完成");
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/resolve-task/" + taskId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试026通过: 成功解决委派任务，taskId: {}", taskId);
    }

    /**
     * 测试序号 027：完成任务
     * 前置条件：resolveTask() 成功
     */
    @Test
    @Order(27)
    @DisplayName("027-完成任务")
    void testCompleteTask_Success() throws Exception {
        if (taskId == null) {
            log.warn("无任务ID，跳过完成任务测试");
            return;
        }
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("approved", true);
        variables.put("comment", "任务已完成");
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/complete-task/" + taskId,
                variables,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试027通过: 成功完成任务，taskId: {}", taskId);
    }

    // ===================== 辅助方法 =====================

    /**
     * 创建测试流程实例
     */
    private String createTestProcessInstance() {
        try {
            // 这里应该调用实际的创建流程实例的API
            // 为了简化，返回一个模拟的流程实例ID
            return "test_process_instance_" + System.currentTimeMillis();
        } catch (Exception e) {
            log.error("创建测试流程实例失败", e);
            return null;
        }
    }

    /**
     * 从流程实例获取第一个任务
     */
    private String getFirstTaskFromProcessInstance(String processInstanceId) {
        try {
            // 这里应该调用实际的API获取任务
            // 为了简化，返回一个模拟的任务ID
            return "test_task_" + System.currentTimeMillis();
        } catch (Exception e) {
            log.error("获取流程实例任务失败", e);
            return null;
        }
    }
} 