package com.open_care.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessEngineController扩展功能集成测试
 * 扩展测试：其他重要接口
 * 测试序号：008-010
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessEngineControllerExtensionTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据
    protected static String processDefId;
    protected static String appDefId;

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 初始化测试数据
        if (processDefId == null) {
            processDefId = "test_process_def_" + System.currentTimeMillis();
        }
        if (appDefId == null) {
            appDefId = "test_app_def_" + System.currentTimeMillis();
        }
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    /**
     * 测试序号 008：根据ID获取流程定义信息（不含版本）
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(8)
    void testGetOcprocessDefInfoByIdWithOutVersion_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getOcprocessDefInfoByIdWithOutVersion/" + processDefId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试008通过: 根据ID获取流程定义信息成功");
    }

    /**
     * 测试序号 009：获取应用流程信息
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(9)
    void testGetAppProcessInfo_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getAppProcessInfo/" + appDefId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试009通过: 获取应用流程信息成功");
    }

    /**
     * 测试序号 010：导入应用流程信息
     * 前置条件：getAppProcessInfo() 成功
     */
    @Test
    @Order(10)
    void testImportAppProcessInfo_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("appDefId", appDefId);
        requestBody.put("processData", "test process data");
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/importAppProcessInfo/" + appDefId,
                requestBody,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试010通过: 导入应用流程信息成功");
    }

    /**
     * 额外测试：创建产品流程
     */
    @Test
    @Order(50)
    void testCreateProductProcess_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("processName", "测试产品流程");
        requestBody.put("processKey", "test_product_process");
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/createProductProcess",
                requestBody,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("额外测试通过: 创建产品流程成功");
    }
} 