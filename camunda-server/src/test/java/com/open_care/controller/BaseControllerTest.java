package com.open_care.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.export.Page;
import lombok.Builder;
import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.core.ParameterizedTypeReference;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 基础控制器集成测试类
 * 提供HTTP上下文、Redis和H2数据库环境
 */
@SpringBootTest(
    classes = com.open_care.camunda.CamundaServerApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("test")
@Transactional
public abstract class BaseControllerTest {

    @LocalServerPort
    protected int port;

    @Autowired
    protected TestRestTemplate restTemplate;

    @Autowired
    protected ObjectMapper objectMapper;

    protected String baseUrl;

    // 测试常量
    protected static final String USER_ID = "test_user_001";
    protected static final String PROCESS_DEF_KEY = "test_process_001";

    @BeforeEach
    public void setUp() {
        baseUrl = "http://localhost:" + port;
    }

    @AfterEach
    public void tearDown() {
        // 清理测试数据
    }

    // ===================== HTTP请求辅助方法 =====================

    /**
     * 执行GET请求
     */
    protected <T> ResponseEntity<T> performGet(String url, ParameterizedTypeReference<T> responseType) {
        return restTemplate.exchange(baseUrl + url, HttpMethod.GET, null, responseType);
    }

    /**
     * 执行POST请求
     */
    protected <T> ResponseEntity<T> performPost(String url, Object requestBody, ParameterizedTypeReference<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
        return restTemplate.exchange(baseUrl + url, HttpMethod.POST, entity, responseType);
    }

    /**
     * 执行POST请求并期望OcResponse返回
     */
    protected <T> ResponseEntity<OcResponse<T>> performPostForOcResponse(String url, Object requestBody, ParameterizedTypeReference<OcResponse<T>> responseType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
        return restTemplate.exchange(baseUrl + url, HttpMethod.POST, entity, responseType);
    }

    /**
     * 执行GET请求并期望OcResponse返回
     */
    protected <T> ResponseEntity<OcResponse<T>> performGetForOcResponse(String url, ParameterizedTypeReference<OcResponse<T>> responseType) {
        return restTemplate.exchange(baseUrl + url, HttpMethod.GET, null, responseType);
    }

    // ===================== 测试数据创建辅助方法 =====================

    /**
     * 创建查询请求DTO
     */
    protected QueryRequestDTO createQueryRequestDTO(int current, int pageSize) {
        QueryRequestDTO queryRequestDTO = new QueryRequestDTO();
        Page page = new Page();
        page.setCurrent(current);
        page.setPageSize(pageSize);
        queryRequestDTO.setPagination(page);
        return queryRequestDTO;
    }

    /**
     * 创建保存流程定义的请求体
     */
    protected String createSaveProcessDefBody(String processKey, String processName, String bpmnXml) {
        Map<String, Object> body = new HashMap<>();
        body.put("data", bpmnXml);
        body.put("type", "bpmn");
        body.put("processName", processName);
        
        try {
            return objectMapper.writeValueAsString(body);
        } catch (Exception e) {
            throw new RuntimeException("创建请求体失败", e);
        }
    }

    /**
     * 创建简单的BPMN XML内容
     */
    protected String createSimpleBpmnXml(String processKey, String processName) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<bpmn:definitions xmlns:bpmn=\"http://www.omg.org/spec/BPMN/20100524/MODEL\"\n" +
                "  xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\"\n" +
                "  xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\"\n" +
                "  xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\"\n" +
                "  id=\"Definitions_1\"\n" +
                "  targetNamespace=\"http://bpmn.io/schema/bpmn\">\n" +
                "  <bpmn:process id=\"" + processKey + "\" name=\"" + processName + "\" isExecutable=\"true\">\n" +
                "    <bpmn:startEvent id=\"StartEvent_1\" name=\"开始\">\n" +
                "      <bpmn:outgoing>Flow_1</bpmn:outgoing>\n" +
                "    </bpmn:startEvent>\n" +
                "    <bpmn:userTask id=\"UserTask_1\" name=\"用户任务\">\n" +
                "      <bpmn:incoming>Flow_1</bpmn:incoming>\n" +
                "      <bpmn:outgoing>Flow_2</bpmn:outgoing>\n" +
                "    </bpmn:userTask>\n" +
                "    <bpmn:endEvent id=\"EndEvent_1\" name=\"结束\">\n" +
                "      <bpmn:incoming>Flow_2</bpmn:incoming>\n" +
                "    </bpmn:endEvent>\n" +
                "    <bpmn:sequenceFlow id=\"Flow_1\" sourceRef=\"StartEvent_1\" targetRef=\"UserTask_1\" />\n" +
                "    <bpmn:sequenceFlow id=\"Flow_2\" sourceRef=\"UserTask_1\" targetRef=\"EndEvent_1\" />\n" +
                "  </bpmn:process>\n" +
                "</bpmn:definitions>";
    }

    // ===================== 测试DTO类定义 =====================

    /**
     * 任务查询请求DTO
     */
    @Data
    @Builder
    public static class TaskQueryRequestDTO {
        private String userId;
        private String processInstanceId;
        private String processDefinitionKey;
        private Page pagination;
    }

    /**
     * 流程实例查询请求DTO
     */
    @Data
    public static class ProcessInstQueryRequestDTO {
        private List<String> processDefKeys;
        private Page pagination;
    }

    /**
     * 流程定义信息DTO
     */
    @Data
    public static class ProcessDefInfoDTO {
        private String id;
        private String name;
        private String key;
        private String version;
        private String status;
    }

    /**
     * 流程组件DTO
     */
    @Data
    public static class ProcessComponentDTO {
        private String id;
        private String name;
        private String type;
    }

    /**
     * 流程定义列表DTO
     */
    @Data
    public static class ProcessDefListDto {
        private String id;
        private String name;
        private String key;
        private String version;
        private Object data;
    }

    /**
     * 流程信息DTO
     */
    @Data
    public static class ProcessInfoDTO {
        private String id;
        private String name;
        private String description;
    }
} 