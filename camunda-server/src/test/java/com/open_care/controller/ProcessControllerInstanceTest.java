package com.open_care.controller;

import com.open_care.api.common.dto.OcResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessController流程实例集成测试
 * 测试第三、四、五层：流程部署后操作 + 流程实例启动 + 实例查询
 * 测试序号：007, 011-017
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessControllerInstanceTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据
    protected static String processInstanceId;
    protected static String processDefinitionKey;
    protected static String processDefinitionId;
    protected static String businessKey;

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
        
        // 初始化测试数据
        if (processDefinitionKey == null) {
            processDefinitionKey = "test_process_" + System.currentTimeMillis();
        }
        if (processDefinitionId == null) {
            processDefinitionId = "process_def_001";
        }
        if (businessKey == null) {
            businessKey = "business_" + System.currentTimeMillis();
        }
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    // ===================== 第三层：流程部署后操作层 (依赖流程定义部署) =====================

    /**
     * 测试序号 007：获取流程定义列表
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(7)
    void testGetProcessDefList_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/processDefinitions/1/10",
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试007通过: 成功获取流程定义列表");
    }

    // ===================== 第四层：流程实例启动层 (依赖流程定义部署) =====================

    /**
     * 测试序号 011：根据Key启动流程实例
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(11)
    void testStartProcessInstanceByKey_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("businessKey", businessKey);
        requestBody.put("variables", Map.of("initiator", USER_ID, "priority", "HIGH"));
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performPostForOcResponse(
                "/process-instance/start/" + processDefinitionKey,
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        
        // 如果成功，保存流程实例ID
        if (body.getData() instanceof Map) {
            Map<String, Object> data = (Map<String, Object>) body.getData();
            if (data.containsKey("processInstanceId")) {
                processInstanceId = (String) data.get("processInstanceId");
            }
        }

        log.info("测试011通过: 根据Key启动流程实例成功，processInstanceId: {}", processInstanceId);
    }

    /**
     * 测试序号 012：根据ID启动流程实例
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(12)
    void testStartProcessInstanceById_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("businessKey", businessKey + "_by_id");
        requestBody.put("variables", Map.of("initiator", USER_ID));
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performPostForOcResponse(
                "/process-instance/start-by-id/" + processDefinitionId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试012通过: 根据ID启动流程实例成功");
    }

    /**
     * 测试序号 013：启动流程并执行第一个任务
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(13)
    void testStartProcessInstanceByKeyAndRunFirstTask_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("businessKey", businessKey + "_with_task");
        requestBody.put("variables", Map.of("initiator", USER_ID, "autoComplete", true));
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performPostForOcResponse(
                "/process-instance/start-and-run/" + processDefinitionKey,
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试013通过: 启动流程并执行第一个任务成功");
    }

    /**
     * 测试序号 014：根据ID启动流程并执行第一个任务
     * 前置条件：deployProcessdefByOCProcessdefId() 成功
     */
    @Test
    @Order(14)
    void testStartProcessInstanceByIdAndRunFirstTask_Success() throws Exception {
        // 准备数据
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("businessKey", businessKey + "_by_id_with_task");
        requestBody.put("variables", Map.of("initiator", USER_ID, "autoComplete", true));
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performPostForOcResponse(
                "/process-instance/start-and-run-by-id/" + processDefinitionId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");

        log.info("测试014通过: 根据ID启动流程并执行第一个任务成功");
    }

    // ===================== 第五层：流程实例查询层 (依赖流程实例启动) =====================

    /**
     * 测试序号 015：分页查询流程实例
     * 前置条件：startProcessInstanceByKey() 成功
     */
    @Test
    @Order(15)
    void testFindProcessInstWithPagination_Success() throws Exception {
        // 准备数据
        ProcessInstQueryRequestDTO requestDTO = new ProcessInstQueryRequestDTO();
        requestDTO.setProcessDefKeys(Arrays.asList(processDefinitionKey));
        requestDTO.setPagination(createQueryRequestDTO(1, 10).getPagination());
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/findProcessInstWithPagination",
                requestDTO,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试015通过: 分页查询流程实例成功");
    }

    /**
     * 测试序号 016：根据流程定义Key获取当前任务
     * 前置条件：startProcessInstanceByKey() 成功
     */
    @Test
    @Order(16)
    void testGetCurrentTasksByProcDefKey_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/process-definition/" + processDefinitionKey + "/tasks",
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试016通过: 根据流程定义Key获取当前任务成功");
    }

    /**
     * 测试序号 017：获取流程定义的任务列表
     * 前置条件：startProcessInstanceByKey() 成功
     */
    @Test
    @Order(17)
    void testGetCurrentTasksByProcDef_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<String> response = restTemplate.getForEntity(
                baseUrl + "/current-tasks-by-proc-def/" + processDefinitionKey,
                String.class
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        String body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试017通过: 获取流程定义的任务列表成功");
    }

    // ===================== 静态方法，供其他测试类获取数据 =====================

    public static String getProcessInstanceId() {
        return processInstanceId;
    }

    public static String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public static String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public static String getBusinessKey() {
        return businessKey;
    }
} 