package com.open_care.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessController历史查询集成测试
 * 测试第十三、十四层：历史查询 + 清理
 * 测试序号：028-032
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessControllerHistoryTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据
    protected static String processInstanceId;
    protected static String taskId;

    // 获取其他测试类的共享数据
    public static String getProcessInstanceId() {
        if (processInstanceId != null) {
            return processInstanceId;
        }
        return ProcessControllerInstanceTest.getProcessInstanceId();
    }

    public static String getTaskId() {
        if (taskId != null) {
            return taskId;
        }
        return ProcessControllerTaskTest.getTaskId();
    }

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    // ===================== 第十三层：历史查询层 (依赖任务已完成) =====================

    /**
     * 测试序号 028：查询流程实例历史任务
     * 前置条件：completeTask() 成功
     */
    @Test
    @Order(28)
    void testGetHistoryTasksByProcInst_Success() throws Exception {
        String currentProcessInstanceId = getProcessInstanceId();
        if (currentProcessInstanceId == null) {
            log.warn("无流程实例ID，跳过历史任务查询测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getHistoryTasksByProcInst/" + currentProcessInstanceId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试028通过: 查询流程实例历史任务成功");
    }

    /**
     * 测试序号 029：获取任务审计历史
     * 前置条件：completeTask() 成功
     */
    @Test
    @Order(29)
    void testGetTaskAuditHistory_Success() throws Exception {
        String currentProcessInstanceId = getProcessInstanceId();
        if (currentProcessInstanceId == null) {
            log.warn("无流程实例ID，跳过任务审计历史查询测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performGet(
                "/getTaskAuditHistory/" + currentProcessInstanceId,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试029通过: 获取任务审计历史成功");
    }

    /**
     * 测试序号 030：分页查询用户任务历史
     * 前置条件：completeTask() 成功
     */
    @Test
    @Order(30)
    void testFindUserTaskHistoryWithPagination_Success() throws Exception {
        // 准备数据
        TaskQueryRequestDTO requestDTO = TaskQueryRequestDTO.builder()
                .userId(USER_ID)
                .processInstanceId(getProcessInstanceId())
                .build();
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/findUserTaskHistoryWithPagination",
                requestDTO,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        Object body = response.getBody();
        assertThat(body).isNotNull();

        log.info("测试030通过: 分页查询用户任务历史成功");
    }

    // ===================== 第十四层：清理层 (依赖历史查询完成) =====================

    /**
     * 测试序号 031：删除任务
     * 前置条件：getTaskAuditHistory() 成功
     */
    @Test
    @Order(31)
    void testDeleteTask_Success() throws Exception {
        String currentTaskId = getTaskId();
        if (currentTaskId == null) {
            log.warn("无任务ID，跳过删除任务测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = performPost(
                "/delete-task/" + currentTaskId,
                null,
                new ParameterizedTypeReference<Object>() {}
        );

        // 验证HTTP状态码 (可能是404因为任务已完成，或者200成功删除)
        assertThat(response.getStatusCode().is2xxSuccessful() || response.getStatusCode().is4xxClientError()).isTrue();

        log.info("测试031通过: 删除任务请求完成");
    }

    /**
     * 测试序号 032：删除流程实例
     * 前置条件：deleteTask() 成功
     */
    @Test
    @Order(32)
    void testDeleteProcessInstance_Success() throws Exception {
        String currentProcessInstanceId = getProcessInstanceId();
        if (currentProcessInstanceId == null) {
            log.warn("无流程实例ID，跳过删除流程实例测试");
            return;
        }
        
        // 执行真实的HTTP请求
        ResponseEntity<Object> response = restTemplate.exchange(
                baseUrl + "/delete-process-instance/" + currentProcessInstanceId,
                org.springframework.http.HttpMethod.DELETE,
                null,
                Object.class
        );

        // 验证HTTP状态码 (可能是404因为实例已完成，或者200成功删除)
        assertThat(response.getStatusCode().is2xxSuccessful() || response.getStatusCode().is4xxClientError()).isTrue();

        log.info("测试032通过: 删除流程实例请求完成");
    }
} 