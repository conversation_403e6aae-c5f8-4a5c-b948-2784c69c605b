package com.open_care.controller;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.bpm.dto.ProcessDefListDto;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessEngineController流程定义管理层集成测试
 * 测试第二层：流程定义管理层 (依赖基础数据) + 异常场景测试
 * 测试序号：004-010
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessEngineControllerManagementTest extends BaseControllerTest {

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    // ===================== 第二层：流程定义管理层 (依赖基础数据) =====================

    /**
     * 测试序号 004：根据ID获取流程定义信息
     * 前置条件：saveOcprocessDefInfo() 成功
     */
    @Test
    @Order(4)
    void testGetOcprocessDefInfoById_Success() throws Exception {
        // 使用BaseTest中保存的流程定义ID
        String processDefId = ProcessEngineControllerBaseTest.getSavedProcessDefId() != null ? 
                ProcessEngineControllerBaseTest.getSavedProcessDefId() : "test_process_def_001";
        String version = "1";
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Map<String, Object>>> response = performGetForOcResponse(
                "/getOcprocessDefInfoById/" + processDefId + "/" + version,
                new ParameterizedTypeReference<OcResponse<Map<String, Object>>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Map<String, Object>> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        // 根据实际API行为验证返回数据结构
        assertThat(body.getData()).isInstanceOf(Map.class);
    }

    /**
     * 测试序号 005：重命名流程定义
     * 前置条件：saveOcprocessDefInfo() 成功 + 未部署状态
     */
    @Test
    @Order(5)
    void testRenameById_Success() throws Exception {
        // 使用之前保存的流程定义ID
        String processDefId = ProcessEngineControllerBaseTest.getSavedProcessDefId() != null ? 
                ProcessEngineControllerBaseTest.getSavedProcessDefId() : "test_process_def_001";
        String newName = "重命名后的流程定义";
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("name", newName);
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<String>> response = performPostForOcResponse(
                "/renameById/" + processDefId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<String> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        // 根据实际API行为验证
    }

    /**
     * 测试序号 006：部署流程定义
     * 前置条件：saveOcprocessDefInfo() 成功
     */
    @Test
    @Order(6)
    void testDeployProcessdefByOCProcessdefId_Success() throws Exception {
        // 使用之前保存的流程定义ID
        String processDefId = ProcessEngineControllerBaseTest.getSavedProcessDefId() != null ? 
                ProcessEngineControllerBaseTest.getSavedProcessDefId() : "test_process_def_001";
        String version = "1";
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Map<String, Object>>> response = performGetForOcResponse(
                "/deployProcessdef/" + processDefId + "/" + version,
                new ParameterizedTypeReference<OcResponse<Map<String, Object>>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Map<String, Object>> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        // 部署成功后应该返回部署相关信息
        assertThat(body.getData()).isInstanceOf(Map.class);
    }

    // ===================== 异常场景测试 =====================

    /**
     * 测试序号 007：保存流程定义 - 重复的流程Key
     */
    @Test
    @Order(7)
    void testSaveOcprocessDefInfo_DuplicateKey() throws Exception {
        String processDefId = "duplicate_process_def";
        String duplicateKey = "duplicate_key_" + System.currentTimeMillis(); // 使用时间戳避免真实冲突
        String processName = "重复的流程";
        
        String bpmnXml = createSimpleBpmnXml(duplicateKey, processName);
        String requestBody = createSaveProcessDefBody(duplicateKey, processName, bpmnXml);
        
        // 第一次保存，应该成功
        ResponseEntity<OcResponse<Object>> firstResponse = performPostForOcResponse(
                "/saveOcprocessDefInfo/" + processDefId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );
        
        // 验证第一次保存成功
        assertThat(firstResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 第二次保存相同的流程Key，测试重复处理
        // 注意：实际行为可能是覆盖而不是报错，需要根据具体API实现调整期望结果
        ResponseEntity<OcResponse<Object>> secondResponse = performPostForOcResponse(
                "/saveOcprocessDefInfo/" + processDefId + "_duplicate",
                requestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );
        
        // 验证HTTP状态码（可能仍然是200，但业务状态码可能不同）
        assertThat(secondResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 根据实际API行为验证结果
        OcResponse<Object> body = secondResponse.getBody();
        assertThat(body).isNotNull();
        // 根据实际实现调整期望结果
    }

    /**
     * 测试序号 008：重命名已部署的流程定义
     */
    @Test
    @Order(8)
    void testRenameById_AlreadyDeployed() throws Exception {
        // 先创建并部署一个流程定义
        String processDefId = "deployed_process_" + System.currentTimeMillis();
        String processKey = "deployed_key_" + System.currentTimeMillis();
        String processName = "待部署的流程";
        
        String bpmnXml = createSimpleBpmnXml(processKey, processName);
        String saveRequestBody = createSaveProcessDefBody(processKey, processName, bpmnXml);
        
        // 保存流程定义
        ResponseEntity<OcResponse<Object>> saveResponse = performPostForOcResponse(
                "/saveOcprocessDefInfo/" + processDefId,
                saveRequestBody,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );
        assertThat(saveResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 部署流程定义
        ResponseEntity<OcResponse<Object>> deployResponse = performGetForOcResponse(
                "/deployProcessdef/" + processDefId + "/1",
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );
        assertThat(deployResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 尝试重命名已部署的流程定义
        Map<String, String> renameRequestBody = new HashMap<>();
        renameRequestBody.put("name", "重命名后的已部署流程");
        
        ResponseEntity<OcResponse<String>> renameResponse = performPostForOcResponse(
                "/renameById/" + processDefId,
                renameRequestBody,
                new ParameterizedTypeReference<OcResponse<String>>() {}
        );
        
        // 验证HTTP状态码
        assertThat(renameResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容 - 可能成功也可能失败，取决于具体实现
        OcResponse<String> body = renameResponse.getBody();
        assertThat(body).isNotNull();
        // 根据实际API行为验证状态码
    }

    /**
     * 测试序号 009：带过滤条件查询流程定义列表
     */
    @Test
    @Order(9)
    void testQueryProcessDefList_WithFilters() throws Exception {
        // 准备带过滤条件的查询请求
        QueryRequestDTO queryRequest = createQueryRequestDTO(1, 10);
        
        // 添加过滤条件（根据实际DTO结构调整）
        Map<String, Object> filters = new HashMap<>();
        filters.put("name", "测试");
        filters.put("status", "0"); // 未部署状态
        // 如果QueryRequestDTO支持filters字段，则设置；否则忽略此部分
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<ProcessDefListDto>> response = performPostForOcResponse(
                "/queryOcprocessDefList",
                queryRequest,
                new ParameterizedTypeReference<OcResponse<ProcessDefListDto>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<ProcessDefListDto> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        assertThat(body.getData().getData()).isNotNull();
    }

    /**
     * 测试序号 010：部署流程定义异常处理
     */
    @Test
    @Order(10)
    void testDeployProcessdefByOCProcessdefId_Exception() throws Exception {
        // 使用不存在的流程定义ID
        String nonExistentProcessDefId = "non_existent_process_def_" + System.currentTimeMillis();
        String version = "1";
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<Object>> response = performGetForOcResponse(
                "/deployProcessdef/" + nonExistentProcessDefId + "/" + version,
                new ParameterizedTypeReference<OcResponse<Object>>() {}
        );

        // 验证HTTP状态码 - 可能是200但业务状态码表示错误，或者是4xx/5xx
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<Object> body = response.getBody();
        assertThat(body).isNotNull();
        // 根据实际API实现验证错误处理方式
        // 可能是status != "0" 或者返回错误信息
    }
} 