package com.open_care.controller;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.dto.ProcessDefListDto;
import com.open_care.bpm.dto.ProcessInfoDTO;
import org.junit.jupiter.api.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProcessEngineController基础数据层集成测试
 * 测试第一层：基础数据层 (无依赖)
 * 测试序号：001-003
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ProcessEngineControllerBaseTest extends BaseControllerTest {

    // 用于存储测试过程中创建的数据，供其他测试类使用
    protected static String savedProcessDefId;
    protected static String savedProcessKey;
    protected static String savedProcessName;

    @Override
    @BeforeEach
    public void setUp() {
        super.setUp();
    }

    @Override
    @AfterEach
    public void tearDown() {
        super.tearDown();
    }

    // ===================== 第一层：基础数据层 (无依赖) =====================

    /**
     * 测试序号 001：查询流程定义列表 - 空列表或正常列表
     */
    @Test
    @Order(1)
    void testQueryProcessDefList_Success() throws Exception {
        // 准备数据
        QueryRequestDTO queryRequest = createQueryRequestDTO(1, 10);
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<ProcessDefListDto>> response = performPostForOcResponse(
                "/queryOcprocessDefList",
                queryRequest,
                new ParameterizedTypeReference<OcResponse<ProcessDefListDto>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<ProcessDefListDto> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        assertThat(body.getData().getData()).isNotNull(); // 可能为空列表，但不应该为null
    }

    /**
     * 测试序号 002：保存流程定义信息
     */
    @Test
    @Order(2)
    void testSaveOcprocessDefInfo_Success() throws Exception {
        // 准备数据
        savedProcessDefId = "test_process_def_001";
        savedProcessKey = "test_process_" + System.currentTimeMillis();
        savedProcessName = "测试流程定义";
        
        String bpmnXml = createSimpleBpmnXml(savedProcessKey, savedProcessName);
        String requestBody = createSaveProcessDefBody(savedProcessKey, savedProcessName, bpmnXml);
        
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<ProcessInfoDTO>> response = performPostForOcResponse(
                "/saveOcprocessDefInfo/" + savedProcessDefId,
                requestBody,
                new ParameterizedTypeReference<OcResponse<ProcessInfoDTO>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<ProcessInfoDTO> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        assertThat(body.getData().getId()).isNotEmpty();
        assertThat(body.getData().getName()).isEqualTo(savedProcessName);
        
        // 保存实际返回的ID供后续测试使用
        savedProcessDefId = body.getData().getId();
    }

    /**
     * 测试序号 003：获取所有流程组件
     */
    @Test
    @Order(3)
    void testGetAllProcessComponents_Success() throws Exception {
        // 执行真实的HTTP请求
        ResponseEntity<OcResponse<List<ProcessComponentDTO>>> response = performGetForOcResponse(
                "/processComponents",
                new ParameterizedTypeReference<OcResponse<List<ProcessComponentDTO>>>() {}
        );

        // 验证HTTP状态码
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        
        // 验证响应内容
        OcResponse<List<ProcessComponentDTO>> body = response.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo("0");
        assertThat(body.getData()).isNotNull();
        // 流程组件列表可能为空，但不应该为null
        assertThat(body.getData()).isInstanceOf(List.class);
    }

    // ===================== 静态方法，供其他测试类获取数据 =====================

    public static String getSavedProcessDefId() {
        return savedProcessDefId;
    }

    public static String getSavedProcessKey() {
        return savedProcessKey;
    }

    public static String getSavedProcessName() {
        return savedProcessName;
    }
} 