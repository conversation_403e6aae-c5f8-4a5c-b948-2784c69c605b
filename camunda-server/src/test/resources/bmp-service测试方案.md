# BPM Service 单元测试方案

## 测试代码位置

测试代码全部编写在 camunda-server 模块中

## 项目概述

本方案针对 `camunda-server` 模块中的 `ProcessEngineController` 和 `ProcessController` 进行渐进式单元测试。这两个控制器是 Camunda 7 功能的封装，提供了流程引擎和流程实例管理的核心功能。

## 测试目标

1. 确保所有 REST API 接口功能正确
2. 验证业务逻辑的正确性
3. 提高代码覆盖率
4. 建立可维护的测试体系

## 接口依赖关系分析

### 核心业务流程依赖链

根据代码分析，Camunda BPM 的核心业务流程遵循以下依赖关系：

```
流程定义保存 → 流程定义部署 → 流程实例启动 → 任务创建 → 任务处理 → 流程完成
     ↓              ↓              ↓           ↓         ↓         ↓
  saveOcprocessDefInfo → deployProcessdefByOCProcessdefId → startProcessInstanceByKey → 任务生命周期管理 → 流程历史查询
```

### 详细依赖关系图

#### 1. 流程定义管理依赖链
```
queryProcessDefList (查询) ← 无依赖
     ↓
saveOcprocessDefInfo (保存) ← 无依赖  
     ↓
deployProcessdefByOCProcessdefId (部署) ← 依赖：流程定义已保存
     ↓
getOcprocessDefInfoById (获取) ← 依赖：流程定义已保存
     ↓
renameById (重命名) ← 依赖：流程定义已保存，未部署时可修改名称
```

#### 2. 流程实例管理依赖链
```
startProcessInstanceByKey (启动) ← 依赖：流程定义已部署
     ↓
startProcessInstanceByKeyAndRunFirstTask ← 依赖：流程定义已部署
     ↓
findProcessInstWithPagination (查询) ← 依赖：流程实例已启动
     ↓
getHistoryTasksByProcInst (历史) ← 依赖：流程实例已启动
     ↓
deleteProcessInstance (删除) ← 依赖：流程实例已启动
```

#### 3. 任务管理依赖链
```
getTaskByTaskId (获取任务) ← 依赖：流程实例已启动，任务已创建
     ↓
candidateTask (设置候选人) ← 依赖：任务已存在
     ↓
claimTask (认领任务) ← 依赖：任务已存在，有候选人
     ↓
assigneeTask (分配任务) ← 依赖：任务已存在
     ↓
delegateTask (委派任务) ← 依赖：任务已分配
     ↓
completeTask (完成任务) ← 依赖：任务已认领或分配
     ↓
unclaimTask (取消认领) ← 依赖：任务已认领
```

#### 4. 流程实例修改依赖链
```
startBeforeActivity (活动前启动) ← 依赖：流程实例已启动
     ↓
startAfterActivity (活动后启动) ← 依赖：流程实例已启动
     ↓
cancelAllForActivity (取消活动) ← 依赖：流程实例已启动，活动已执行
```

#### 5. 任务查询依赖链
```
getCurrentTasksByProcDefKey ← 依赖：流程定义已部署，流程实例已启动
getCurrentTasksByUser ← 依赖：任务已创建并分配给用户
findUserTasksWithPagination ← 依赖：任务已创建
findUserTaskHistoryWithPagination ← 依赖：任务已完成
```

## 完整详细依赖关系图

### 总体架构依赖图

```mermaid
graph TD
    A[流程定义管理] --> B[流程实例管理]
    B --> C[任务管理]
    C --> D[历史查询]
    A --> E[应用流程管理]
    B --> F[流程修改管理]
    C --> G[任务查询]
    
    subgraph "流程定义管理"
        A1[查询流程定义列表]
        A2[保存流程定义信息]
        A3[获取流程定义信息]
        A4[部署流程定义]
        A5[重命名流程定义]
    end
    
    subgraph "流程实例管理"
        B1[启动流程实例]
        B2[查询流程实例]
        B3[删除流程实例]
        B4[流程实例迁移]
    end
    
    subgraph "任务管理"
        C1[获取任务]
        C2[设置候选人]
        C3[认领任务]
        C4[分配任务]
        C5[委派任务]
        C6[完成任务]
        C7[取消认领]
    end
```

### 详细接口依赖关系图

#### 第一层：基础数据层 (无依赖)
```
Level 0: 基础查询和保存操作
├── ProcessEngineController.queryProcessDefList()           [测试序号: 001]
├── ProcessEngineController.saveOcprocessDefInfo()          [测试序号: 002]
└── ProcessEngineController.getAllProcessComponents()       [测试序号: 003]
```

#### 第二层：流程定义管理层 (依赖基础数据)
```
Level 1: 流程定义操作 (依赖: 流程定义已保存)
├── ProcessEngineController.getOcprocessDefInfoById()       [测试序号: 004]
│   └── 前置条件: saveOcprocessDefInfo() 成功
├── ProcessEngineController.renameById()                    [测试序号: 005]
│   └── 前置条件: saveOcprocessDefInfo() 成功 + 未部署状态
└── ProcessEngineController.deployProcessdefByOCProcessdefId() [测试序号: 006]
    └── 前置条件: saveOcprocessDefInfo() 成功
```

#### 第三层：流程部署后操作层 (依赖流程定义部署)
```
Level 2: 部署后操作 (依赖: 流程定义已部署)
├── ProcessEngineController.getProcessDefList()             [测试序号: 007]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
├── ProcessEngineController.createProductProcess()          [测试序号: 008]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
├── ProcessEngineController.createPackageProcess()          [测试序号: 009]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
└── ProcessEngineController.importAppProcessInfo()          [测试序号: 010]
    └── 前置条件: deployProcessdefByOCProcessdefId() 成功
```

#### 第四层：流程实例启动层 (依赖流程定义部署)
```
Level 3: 流程实例启动 (依赖: 流程定义已部署)
├── ProcessController.startProcessInstanceByKey()           [测试序号: 011]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
├── ProcessController.startProcessInstanceById()            [测试序号: 012]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
├── ProcessController.startProcessInstanceByKeyAndRunFirstTask() [测试序号: 013]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功
└── ProcessController.startProcessInstanceByIdAndRunFirstTask() [测试序号: 014]
    └── 前置条件: deployProcessdefByOCProcessdefId() 成功
```

#### 第五层：流程实例查询层 (依赖流程实例启动)
```
Level 4: 流程实例查询 (依赖: 流程实例已启动)
├── ProcessController.findProcessInstWithPagination()       [测试序号: 015]
│   └── 前置条件: startProcessInstanceByKey() 成功
├── ProcessController.getCurrentTasksByProcDefKey()         [测试序号: 016]
│   └── 前置条件: startProcessInstanceByKey() 成功
└── ProcessController.getCurrentTasksByProcDef()            [测试序号: 017]
    └── 前置条件: startProcessInstanceByKey() 成功
```

#### 第六层：任务获取层 (依赖流程实例启动)
```
Level 5: 任务获取 (依赖: 流程实例已启动，任务已创建)
├── ProcessController.getTaskByTaskId()                     [测试序号: 018]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 任务已创建
├── ProcessController.getCurrentTasksByUser()               [测试序号: 019]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 任务已创建
└── ProcessController.findUserTasksWithPagination()         [测试序号: 020]
    └── 前置条件: startProcessInstanceByKey() 成功 + 任务已创建
```

#### 第七层：任务候选人设置层 (依赖任务存在)
```
Level 6: 任务候选人设置 (依赖: 任务已存在)
├── ProcessController.candidateTask()                       [测试序号: 021]
│   └── 前置条件: getTaskByTaskId() 成功
└── ProcessController.assigneeTask()                        [测试序号: 022]
    └── 前置条件: getTaskByTaskId() 成功
```

#### 第八层：任务认领层 (依赖候选人设置)
```
Level 7: 任务认领 (依赖: 候选人已设置 或 任务已分配)
├── ProcessController.claimTask()                           [测试序号: 023]
│   └── 前置条件: candidateTask() 成功 或 assigneeTask() 成功
└── ProcessController.unclaimTask()                         [测试序号: 024]
    └── 前置条件: claimTask() 成功
```

#### 第九层：任务委派层 (依赖任务已分配)
```
Level 8: 任务委派 (依赖: 任务已分配)
├── ProcessController.delegateTask()                        [测试序号: 025]
│   └── 前置条件: assigneeTask() 成功 或 claimTask() 成功
└── ProcessController.resolveTask()                         [测试序号: 026]
    └── 前置条件: delegateTask() 成功
```

#### 第十层：任务完成层 (依赖任务已认领或分配)
```
Level 9: 任务完成 (依赖: 任务已认领或分配)
└── ProcessController.completeTask()                        [测试序号: 027]
    └── 前置条件: claimTask() 成功 或 assigneeTask() 成功
```

#### 第十一层：流程实例修改层 (依赖流程实例运行中)
```
Level 10: 流程实例修改 (依赖: 流程实例运行中)
├── ProcessController.startBeforeActivity()                 [测试序号: 028]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 流程运行中
├── ProcessController.startAfterActivity()                  [测试序号: 029]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 流程运行中
├── ProcessController.cancelAllForActivity()                [测试序号: 030]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 活动已执行
├── ProcessController.processModification()                 [测试序号: 031]
│   └── 前置条件: startProcessInstanceByKey() 成功
└── ProcessController.taskTransfer()                        [测试序号: 032]
    └── 前置条件: getTaskByTaskId() 成功
```

#### 第十二层：高级功能层 (依赖多个前置条件)
```
Level 11: 高级功能 (依赖: 多个前置条件)
├── ProcessEngineController.addMultiInstance()              [测试序号: 033]
│   └── 前置条件: startProcessInstanceByKey() 成功 + 任务已创建
├── ProcessEngineController.saveAndDeployThenMigrateProcess() [测试序号: 034]
│   └── 前置条件: deployProcessdefByOCProcessdefId() 成功 + startProcessInstanceByKey() 成功
└── ProcessController.migrateProcessDefinitionInstances()   [测试序号: 035]
    └── 前置条件: startProcessInstanceByKey() 成功 + 新版本流程定义已部署
```

#### 第十三层：历史查询层 (依赖任务已处理)
```
Level 12: 历史查询 (依赖: 任务已处理或流程已完成)
├── ProcessController.getHistoryTasksByProcInst()           [测试序号: 036]
│   └── 前置条件: completeTask() 成功 或 流程实例已完成
├── ProcessController.getTaskAuditHistory()                 [测试序号: 037]
│   └── 前置条件: completeTask() 成功 或 任务状态已变更
└── ProcessController.findUserTaskHistoryWithPagination()   [测试序号: 038]
    └── 前置条件: completeTask() 成功
```

#### 第十四层：清理层 (依赖所有操作完成)
```
Level 13: 清理操作 (依赖: 相关数据已存在)
├── ProcessController.deleteTask()                          [测试序号: 039]
│   └── 前置条件: getTaskByTaskId() 成功 + 任务未完成
└── ProcessController.deleteProcessInstance()               [测试序号: 040]
    └── 前置条件: startProcessInstanceByKey() 成功
```

### 测试执行矩阵

| 测试序号 | 接口名称 | 依赖层级 | 前置条件 | 后置验证 | 测试优先级 |
|---------|---------|---------|----------|----------|-----------|
| 001 | queryProcessDefList | Level 0 | 无 | 返回空列表或已有数据 | 高 |
| 002 | saveOcprocessDefInfo | Level 0 | 无 | 流程定义已保存 | 高 |
| 003 | getAllProcessComponents | Level 0 | 无 | 返回组件列表 | 中 |
| 004 | getOcprocessDefInfoById | Level 1 | 002成功 | 返回正确的流程定义信息 | 高 |
| 005 | renameById | Level 1 | 002成功+未部署 | 流程定义名称已更新 | 中 |
| 006 | deployProcessdefByOCProcessdefId | Level 1 | 002成功 | 流程定义已部署 | 高 |
| 007 | getProcessDefList | Level 2 | 006成功 | 返回已部署的流程定义 | 高 |
| 008 | createProductProcess | Level 2 | 006成功 | 产品流程已创建 | 中 |
| 009 | createPackageProcess | Level 2 | 006成功 | 包流程已创建 | 中 |
| 010 | importAppProcessInfo | Level 2 | 006成功 | 应用流程信息已导入 | 低 |
| 011 | startProcessInstanceByKey | Level 3 | 006成功 | 流程实例已启动 | 高 |
| 012 | startProcessInstanceById | Level 3 | 006成功 | 流程实例已启动 | 高 |
| 013 | startProcessInstanceByKeyAndRunFirstTask | Level 3 | 006成功 | 流程实例已启动+第一个任务已执行 | 高 |
| 014 | startProcessInstanceByIdAndRunFirstTask | Level 3 | 006成功 | 流程实例已启动+第一个任务已执行 | 高 |
| 015 | findProcessInstWithPagination | Level 4 | 011成功 | 返回流程实例列表 | 高 |
| 016 | getCurrentTasksByProcDefKey | Level 4 | 011成功 | 返回当前任务列表 | 高 |
| 017 | getCurrentTasksByProcDef | Level 4 | 011成功 | 返回当前任务列表 | 高 |
| 018 | getTaskByTaskId | Level 5 | 011成功+任务已创建 | 返回任务详情 | 高 |
| 019 | getCurrentTasksByUser | Level 5 | 011成功+任务已创建 | 返回用户任务列表 | 高 |
| 020 | findUserTasksWithPagination | Level 5 | 011成功+任务已创建 | 返回分页任务列表 | 高 |
| 021 | candidateTask | Level 6 | 018成功 | 候选人已设置 | 高 |
| 022 | assigneeTask | Level 6 | 018成功 | 任务已分配 | 高 |
| 023 | claimTask | Level 7 | 021成功或022成功 | 任务已认领 | 高 |
| 024 | unclaimTask | Level 7 | 023成功 | 任务认领已取消 | 中 |
| 025 | delegateTask | Level 8 | 022成功或023成功 | 任务已委派 | 中 |
| 026 | resolveTask | Level 8 | 025成功 | 任务委派已解决 | 中 |
| 027 | completeTask | Level 9 | 023成功或022成功 | 任务已完成 | 高 |
| 028 | startBeforeActivity | Level 10 | 011成功+流程运行中 | 活动前启动成功 | 低 |
| 029 | startAfterActivity | Level 10 | 011成功+流程运行中 | 活动后启动成功 | 低 |
| 030 | cancelAllForActivity | Level 10 | 011成功+活动已执行 | 活动已取消 | 低 |
| 031 | processModification | Level 10 | 011成功 | 流程修改成功 | 中 |
| 032 | taskTransfer | Level 10 | 018成功 | 任务转移成功 | 中 |
| 033 | addMultiInstance | Level 11 | 011成功+任务已创建 | 多实例任务已添加 | 低 |
| 034 | saveAndDeployThenMigrateProcess | Level 11 | 006成功+011成功 | 流程保存部署迁移成功 | 低 |
| 035 | migrateProcessDefinitionInstances | Level 11 | 011成功+新版本已部署 | 流程实例迁移成功 | 低 |
| 036 | getHistoryTasksByProcInst | Level 12 | 027成功 | 返回历史任务列表 | 中 |
| 037 | getTaskAuditHistory | Level 12 | 027成功 | 返回审计历史 | 中 |
| 038 | findUserTaskHistoryWithPagination | Level 12 | 027成功 | 返回分页历史任务 | 中 |
| 039 | deleteTask | Level 13 | 018成功+任务未完成 | 任务已删除 | 低 |
| 040 | deleteProcessInstance | Level 13 | 011成功 | 流程实例已删除 | 中 |

### 测试数据状态管理

#### 状态转换图
```
[无数据] 
    ↓ saveOcprocessDefInfo()
[流程定义已保存] 
    ↓ deployProcessdefByOCProcessdefId()
[流程定义已部署] 
    ↓ startProcessInstanceByKey()
[流程实例已启动] 
    ↓ 自动创建任务
[任务已创建] 
    ↓ candidateTask() 或 assigneeTask()
[任务已分配] 
    ↓ claimTask()
[任务已认领] 
    ↓ completeTask()
[任务已完成] 
    ↓ 所有任务完成
[流程实例已完成]
    ↓ deleteProcessInstance()
[数据已清理]
```

#### 关键状态检查点
1. **流程定义状态检查**
   - 保存后：`processDefinitionId` 不为空
   - 部署后：`deploymentId` 不为空，状态为 `DEPLOYED`

2. **流程实例状态检查**
   - 启动后：`processInstanceId` 不为空，状态为 `ACTIVE`
   - 完成后：状态为 `COMPLETED`

3. **任务状态检查**
   - 创建后：`taskId` 不为空，`assignee` 为空
   - 分配后：`assignee` 不为空
   - 认领后：`assignee` 为当前用户
   - 完成后：`endTime` 不为空

### 测试执行策略

#### 串行执行组 (必须按顺序执行)
```java
@TestMethodOrder(OrderAnnotation.class)
class ProcessEngineControllerSerialTest {
    
    // 组1：流程定义基础操作
    @Test @Order(1) void testQueryProcessDefList()
    @Test @Order(2) void testSaveOcprocessDefInfo()
    @Test @Order(3) void testGetOcprocessDefInfoById()
    @Test @Order(4) void testDeployProcessdefByOCProcessdefId()
    
    // 组2：流程实例操作
    @Test @Order(5) void testStartProcessInstanceByKey()
    @Test @Order(6) void testFindProcessInstWithPagination()
    
    // 组3：任务生命周期
    @Test @Order(7) void testGetTaskByTaskId()
    @Test @Order(8) void testCandidateTask()
    @Test @Order(9) void testClaimTask()
    @Test @Order(10) void testCompleteTask()
    
    // 组4：历史查询
    @Test @Order(11) void testGetHistoryTasksByProcInst()
    
    // 组5：清理
    @Test @Order(12) void testDeleteProcessInstance()
}
```

#### 并行执行组 (可以并行执行)
```java
@Execution(ExecutionMode.CONCURRENT)
class ProcessEngineControllerParallelTest {
    
    // 查询类接口可以并行执行
    @Test void testQueryProcessDefList()
    @Test void testGetProcessDefList()
    @Test void testGetAllProcessComponents()
    
    // 独立的流程实例可以并行测试
    @Test void testStartProcessInstanceByKey_Scenario1()
    @Test void testStartProcessInstanceByKey_Scenario2()
}
```

### 测试失败恢复策略

#### 失败点分析
1. **Level 0-1 失败**：基础数据问题，需要检查数据库连接和基础配置
2. **Level 2-3 失败**：流程定义或部署问题，需要检查 Camunda 引擎配置
3. **Level 4-9 失败**：流程执行问题，需要检查业务逻辑和数据状态
4. **Level 10+ 失败**：高级功能问题，可能是配置或权限问题

#### 恢复机制
```java
@TestMethodOrder(OrderAnnotation.class)
class ProcessEngineControllerTest {
    
    private static ProcessDefinitionInfo processDefInfo;
    private static String deploymentId;
    private static String processInstanceId;
    private static String taskId;
    
    @BeforeEach
    void checkPrerequisites() {
        // 检查前置条件，如果不满足则跳过测试
        if (needsProcessDefinition() && processDefInfo == null) {
            Assumptions.assumeTrue(false, "流程定义未准备好");
        }
        if (needsDeployment() && deploymentId == null) {
            Assumptions.assumeTrue(false, "流程定义未部署");
        }
        // ... 其他前置条件检查
    }
    
    @AfterEach
    void handleFailure(TestInfo testInfo) {
        if (testInfo.getTags().contains("cleanup-on-failure")) {
            // 测试失败时的清理逻辑
            cleanupTestData();
        }
    }
}
```

### 测试数据模板

#### 流程定义数据模板
```java
public class TestDataTemplates {
    
    public static ProcessDefinitionInfo createBasicProcessDef() {
        return ProcessDefinitionInfo.builder()
            .name("测试流程定义")
            .key("test_process_" + System.currentTimeMillis())
            .version(1)
            .description("用于单元测试的流程定义")
            .build();
    }
    
    public static ProcessInstanceStartRequest createStartRequest(String processDefKey) {
        return ProcessInstanceStartRequest.builder()
            .processDefinitionKey(processDefKey)
            .businessKey("test_business_" + System.currentTimeMillis())
            .variables(Map.of(
                "initiator", "test_user",
                "priority", "HIGH"
            ))
            .build();
    }
    
    public static TaskRequest createTaskRequest(String taskId) {
        return TaskRequest.builder()
            .taskId(taskId)
            .assignee("test_user")
            .variables(Map.of(
                "approved", true,
                "comment", "测试完成"
            ))
            .build();
    }
}
```

这个完整的依赖关系图为后续的单元测试实现提供了清晰的路线图，确保测试按照正确的顺序执行，并且每个测试都有明确的前置条件和后置验证。

## 测试架构设计

### 测试分层策略

1. **单元测试层**：测试单个方法的逻辑
2. **集成测试层**：测试控制器与服务层的集成
3. **端到端测试层**：测试完整的业务流程

### 测试技术栈

- **测试框架**：JUnit 5
- **Mock 框架**：Mockito
- **Spring 测试**：@SpringBootTest, @WebMvcTest
- **测试容器**：Testcontainers (用于数据库集成测试)
- **断言库**：AssertJ

## 渐进式测试实施计划

### 第一阶段：基础设施搭建 (优先级：高)

#### 1.1 测试基类完善
- [ ] 完善 `BaseMockTest` 基类
- [ ] 创建 `BaseControllerTest` 基类
- [ ] 创建 `BaseIntegrationTest` 基类
- [ ] 配置测试数据库环境

#### 1.2 测试工具类
- [ ] 创建 `TestDataBuilder` 工具类
- [ ] 创建 `MockDataFactory` 工具类
- [ ] 创建 `AssertionUtils` 断言工具类

### 第二阶段：ProcessEngineController 测试 (优先级：高)

#### 2.1 流程定义管理接口测试 (按依赖顺序)

**第一批：无依赖接口**
1. `queryProcessDefList()` - 查询流程定义列表
2. `saveOcprocessDefInfo()` - 保存流程定义信息

**第二批：依赖流程定义保存的接口**
3. `getOcprocessDefInfoById()` - 根据ID获取流程定义信息
4. `renameById()` - 重命名流程定义 (测试未部署状态)

**第三批：依赖流程定义存在的接口**
5. `deployProcessdefByOCProcessdefId()` - 部署流程定义
6. `getProcessDefList()` - 获取流程定义列表 (测试已部署状态)

**测试重点**：
- 参数验证
- 业务逻辑正确性
- 异常处理
- 返回值格式
- **依赖关系验证**：确保部署前流程定义必须已保存

#### 2.2 应用流程管理接口测试

**测试顺序**：
1. `getAppProcessInfo()` - 获取应用流程信息
2. `importAppProcessInfo()` - 导入应用流程信息
3. `createProductProcess()` - 创建产品流程 (依赖已部署的流程定义)
4. `createPackageProcess()` - 创建包流程 (依赖已部署的流程定义)

**测试重点**：
- 数据导入导出正确性
- 流程创建逻辑
- 数据一致性验证
- **依赖关系验证**：产品流程和包流程创建需要基础流程定义

#### 2.3 多实例任务管理接口测试

**测试顺序**：
1. `getAllProcessComponents()` - 获取所有流程组件
2. `addMultiInstance()` - 添加多实例任务 (依赖流程实例已启动)
3. `saveAndDeployThenMigrateProcess()` - 保存部署并迁移流程

**测试重点**：
- 多实例任务创建
- 流程组件管理
- 流程迁移逻辑

### 第三阶段：ProcessController 测试 (优先级：高)

#### 3.1 流程实例启动接口测试 (按依赖顺序)

**前置条件**：流程定义已部署

**测试顺序**：
1. `startProcessInstanceByKey()` - 根据Key启动流程实例
2. `startProcessInstanceById()` - 根据ID启动流程实例
3. `startProcessInstanceByKeyAndRunFirstTask()` - 启动流程并执行第一个任务
4. `startProcessInstanceByIdAndRunFirstTask()` - 根据ID启动流程并执行第一个任务

**测试重点**：
- 流程启动参数验证
- 流程变量设置
- 第一个任务自动执行
- 启动失败处理
- **依赖关系验证**：确保流程定义已部署才能启动

#### 3.2 任务管理接口测试 (按任务生命周期顺序)

**前置条件**：流程实例已启动，任务已创建

**测试顺序**：
1. `getTaskByTaskId()` - 根据任务ID获取任务
2. `candidateTask()` - 设置候选任务
3. `claimTask()` - 认领任务 (依赖候选任务设置)
4. `assigneeTask()` - 分配任务 (可替代认领)
5. `delegateTask()` - 委派任务 (依赖任务已分配)
6. `resolveTask()` - 解决任务 (依赖任务已委派)
7. `completeTask()` - 完成任务 (依赖任务已认领或分配)
8. `unclaimTask()` - 取消认领任务

**测试重点**：
- 任务状态转换
- 用户权限验证
- 任务分配逻辑
- 批量操作处理
- **状态依赖验证**：任务状态变更的先后顺序

#### 3.3 任务查询接口测试

**前置条件**：任务已创建和分配

**测试顺序**：
1. `getCurrentTasksByProcDefKey()` - 根据流程定义Key查询当前任务
2. `getCurrentTasksByProcDef()` - 根据流程定义查询当前任务
3. `getCurrentTasksByUser()` - 根据用户查询当前任务
4. `findUserTasksWithPagination()` - 分页查询用户任务
5. `findUserTaskHistoryWithPagination()` - 分页查询用户任务历史 (依赖任务已完成)

**测试重点**：
- 查询条件组合
- 分页功能正确性
- 数据过滤逻辑
- 性能优化验证

#### 3.4 流程历史和审计接口测试

**前置条件**：流程实例已启动，任务已处理

**测试顺序**：
1. `getHistoryTasksByProcInst()` - 获取流程实例历史任务
2. `getTaskAuditHistory()` - 获取任务审计历史
3. `deleteTask()` - 删除任务 (仅限运行中任务无法删除的测试)
4. `deleteProcessInstance()` - 删除流程实例

**测试重点**：
- 历史数据完整性
- 审计日志记录
- 删除操作权限
- 数据一致性

#### 3.5 流程实例管理接口测试

**前置条件**：流程实例已启动

**测试顺序**：
1. `findProcessInstWithPagination()` - 分页查询流程实例
2. `processModification()` - 流程修改
3. `taskTransfer()` - 任务转移 (依赖任务已存在)
4. `migrateProcessDefinitionInstances()` - 迁移流程定义实例

**测试重点**：
- 流程实例查询
- 流程迁移逻辑
- 任务转移记录
- 数据迁移完整性

#### 3.6 流程实例修改接口测试

**前置条件**：流程实例已启动，活动已执行

**测试顺序**：
1. `startBeforeActivity()` - 在指定活动前启动
2. `startAfterActivity()` - 在指定活动后启动
3. `cancelAllForActivity()` - 取消指定活动的所有实例

**测试重点**：
- 流程实例状态验证
- 活动节点操作正确性
- 并发场景处理
- 错误恢复机制

### 第四阶段：服务层深度测试 (优先级：中)

#### 4.1 ProcessEngineService 测试
**测试重点**：
- 核心业务逻辑
- 数据库操作
- 缓存机制
- 事务处理

#### 4.2 ProcessService 测试
**测试重点**：
- 流程定义管理
- 部署逻辑
- 版本控制
- 文件处理

#### 4.3 ProcessInstanceService 测试
**测试重点**：
- 流程实例生命周期
- 状态管理
- 变量处理
- 迁移逻辑

### 第五阶段：集成测试 (优先级：中)

#### 5.1 数据库集成测试
- [ ] 使用 Testcontainers 启动 PostgreSQL
- [ ] 测试数据持久化
- [ ] 测试事务回滚
- [ ] 测试并发访问

#### 5.2 Camunda 引擎集成测试
- [ ] 测试流程引擎启动
- [ ] 测试流程部署
- [ ] 测试流程执行
- [ ] 测试任务处理

### 第六阶段：性能和边界测试 (优先级：低)

#### 6.1 性能测试
- [ ] 大数据量查询测试
- [ ] 并发任务处理测试
- [ ] 内存使用测试
- [ ] 响应时间测试

#### 6.2 边界测试
- [ ] 极限参数测试
- [ ] 异常输入测试
- [ ] 资源耗尽测试
- [ ] 网络异常测试

## 测试执行顺序示例

### 完整业务流程测试顺序

```java
// 1. 流程定义管理
@Test
@Order(1)
void testQueryProcessDefList() { /* 查询空列表 */ }

@Test
@Order(2) 
void testSaveOcprocessDefInfo() { /* 保存流程定义 */ }

@Test
@Order(3)
void testGetOcprocessDefInfoById() { /* 获取已保存的流程定义 */ }

@Test
@Order(4)
void testDeployProcessdefByOCProcessdefId() { /* 部署流程定义 */ }

// 2. 流程实例管理
@Test
@Order(5)
void testStartProcessInstanceByKey() { /* 启动流程实例 */ }

@Test
@Order(6)
void testFindProcessInstWithPagination() { /* 查询流程实例 */ }

// 3. 任务管理
@Test
@Order(7)
void testGetTaskByTaskId() { /* 获取任务 */ }

@Test
@Order(8)
void testCandidateTask() { /* 设置候选人 */ }

@Test
@Order(9)
void testClaimTask() { /* 认领任务 */ }

@Test
@Order(10)
void testCompleteTask() { /* 完成任务 */ }

// 4. 历史查询
@Test
@Order(11)
void testGetHistoryTasksByProcInst() { /* 查询历史任务 */ }

// 5. 清理
@Test
@Order(12)
void testDeleteProcessInstance() { /* 删除流程实例 */ }
```

## 测试数据管理策略

### 测试数据分类
1. **静态测试数据**：预定义的流程定义、用户信息等
2. **动态测试数据**：测试过程中生成的流程实例、任务等
3. **Mock 数据**：用于单元测试的模拟数据

### 数据清理策略
1. **测试前清理**：确保测试环境干净
2. **测试后清理**：清理测试产生的数据
3. **失败恢复**：测试失败时的数据恢复机制

### 依赖数据管理
1. **数据继承**：后续测试使用前面测试创建的数据
2. **数据隔离**：每个测试类使用独立的数据集
3. **状态验证**：验证数据状态符合后续测试的前置条件

## 测试覆盖率目标

### 代码覆盖率目标
- **行覆盖率**：≥ 80%
- **分支覆盖率**：≥ 70%
- **方法覆盖率**：≥ 90%

### 功能覆盖率目标
- **正常流程**：100%
- **异常流程**：≥ 80%
- **边界条件**：≥ 70%

## 测试环境配置

### 测试配置文件
- `application-test.yml`：测试环境配置
- `application-test-db.yml`：测试数据库配置
- `logback-test.xml`：测试日志配置

### 测试依赖
```kotlin
testImplementation("org.springframework.boot:spring-boot-starter-test")
testImplementation("org.testcontainers:testcontainers")
testImplementation("org.testcontainers:postgresql")
testImplementation("com.h2database:h2")
testImplementation("org.mockito:mockito-junit-jupiter")
```

## 持续集成配置

### CI/CD 流水线
1. **代码检查**：静态代码分析
2. **单元测试**：执行所有单元测试
3. **集成测试**：执行集成测试
4. **覆盖率报告**：生成测试覆盖率报告
5. **质量门禁**：根据覆盖率和测试结果决定是否通过

### 测试报告
- **JUnit 报告**：测试执行结果
- **JaCoCo 报告**：代码覆盖率
- **SonarQube 报告**：代码质量分析

## 实施时间计划

### 第一周：基础设施搭建
- 完善测试基类和工具类
- 配置测试环境
- 建立测试数据管理机制

### 第二周：ProcessEngineController 核心接口测试
- 流程定义管理接口测试 (按依赖顺序)
- 应用流程管理接口测试

### 第三周：ProcessEngineController 剩余接口测试
- 多实例任务管理接口测试
- 流程实例修改接口测试

### 第四周：ProcessController 核心接口测试
- 流程实例启动接口测试 (按依赖顺序)
- 任务管理接口测试 (按生命周期顺序)

### 第五周：ProcessController 剩余接口测试
- 任务查询接口测试
- 流程历史和审计接口测试
- 流程实例管理接口测试

### 第六周：服务层和集成测试
- 服务层深度测试
- 数据库集成测试
- Camunda 引擎集成测试

### 第七周：性能测试和优化
- 性能测试
- 边界测试
- 测试优化和重构

### 第八周：文档和总结
- 完善测试文档
- 总结测试经验
- 建立测试最佳实践

## 风险评估和应对策略

### 技术风险
1. **Camunda 引擎复杂性**：深入学习 Camunda API，建立测试最佳实践
2. **数据库事务复杂性**：使用 @Transactional 和 @Rollback 注解管理测试事务
3. **异步处理测试**：使用 @Async 测试工具和 CompletableFuture
4. **依赖关系复杂性**：建立清晰的测试数据管理和状态验证机制

### 进度风险
1. **测试用例编写时间超预期**：优先测试核心功能，次要功能可后续补充
2. **环境配置复杂**：提前准备测试环境模板，标准化配置流程
3. **依赖关系理解偏差**：与业务专家确认流程依赖关系的正确性

### 质量风险
1. **测试覆盖率不足**：建立覆盖率监控机制，定期检查和补充
2. **测试数据污染**：严格执行数据清理策略，确保测试独立性
3. **依赖关系测试不充分**：重点验证接口间的依赖关系和状态转换

## 成功标准

### 量化指标
- 代码覆盖率达到目标值
- 所有测试用例通过率 100%
- 测试执行时间控制在合理范围内
- 依赖关系验证覆盖率 100%

### 质量指标
- 测试用例可维护性良好
- 测试文档完整清晰
- 测试环境稳定可靠
- 依赖关系测试充分

### 业务指标
- 核心业务功能测试覆盖完整
- 异常场景处理验证充分
- 性能瓶颈识别和优化
- 业务流程完整性验证

## 后续维护计划

### 测试维护
1. **定期更新**：随着业务功能更新，及时更新测试用例
2. **重构优化**：定期重构测试代码，提高可维护性
3. **性能监控**：持续监控测试执行性能，优化测试效率
4. **依赖关系维护**：业务流程变更时及时更新依赖关系测试

### 知识传承
1. **文档维护**：保持测试文档的时效性和准确性
2. **团队培训**：定期进行测试最佳实践培训
3. **经验总结**：建立测试经验库，积累测试知识
4. **依赖关系文档**：维护接口依赖关系图和测试顺序文档

---

**备注**：本方案采用渐进式实施策略，严格按照业务依赖关系确定测试顺序。建议先完成高优先级的测试用例，确保核心功能的测试覆盖，然后逐步完善其他测试内容。特别注意接口间的依赖关系，确保测试数据的状态符合后续测试的前置条件。 