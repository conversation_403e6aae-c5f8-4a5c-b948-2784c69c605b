dubbo:
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
    register-mode: instance
    group: dubbo-${spring.cloud.nacos.discovery.group}
    parameters:
      namespace: ${spring.cloud.nacos.discovery.namespace}
  protocol:
    name: tri
    #    port: -1
    #    host: ${spring.cloud.nacos.discovery.ip}
    prefer-serialization: open-care-fury-compatible
    serialization: hessian2,fury
  application:
    name: "dubbo-${spring.application.name}"
    logger: slf4j
    version: 1.0