
# 登录拦截校验
open-care:
  auth:
    login:
      interrupt:
        include-patterns: /**
        # 接口不需要登录
        exclude-patterns:
          /**

sa-token:
  # 打开 RPC 调用鉴权
  check-same-token: true
  # jwt秘钥
  jwt-secret-key: asdasdasifhueuiwyurfewbfjsdafjk
  #  sso-client:
  #    server-url: http://*************
  #    is-http: false

  # 配置Sa-Token单独使用的Redis连接 （此处需要和SSO-Server端连接同一个Redis）
  alone-redis:
    # Redis数据库索引 (默认为0)
    database: 1
    # Redis服务器地址
    host: ${spring.data.redis.host}
    # Redis服务器连接端口
    port: ${spring.data.redis.port}
    # Redis服务器连接密码（默认为空）
    password: ${spring.data.redis.password}
    # 连接超时时间
    timeout: ${spring.data.redis.timeout}
    lettuce:
      pool:
        #最大连接数
        max-active: ${spring.data.redis.lettuce.pool.max-active}
        #最大阻塞等待时间(负数表示没限制)
        max-wait: ${spring.data.redis.lettuce.pool.max-wait}
        #最大空闲
        max-idle: ${spring.data.redis.lettuce.pool.max-idle}
        #最小空闲
        min-idle: ${spring.data.redis.lettuce.pool.min-idle}