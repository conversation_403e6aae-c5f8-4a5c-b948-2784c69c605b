spring:
  application:
    name: camunda-server-test
  
  # 数据源配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL;DATABASE_TO_LOWER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password:
  
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.H2Dialect
  
  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis配置 - 使用开发环境Redis
  data:
    redis:
      host: host.docker.internal
      port: 6379
      password: opencare
      database: 3
      lettuce:
        pool:
          max-active: 200
          max-wait: -1ms
          max-idle: 10
          min-idle: 0
      timeout: 2000ms

# Camunda配置
camunda:
  bpm:
    admin-user:
      id: admin
      password: admin
      firstName: Admin
      lastName: User
    database:
      schema-update: create-drop
    job-execution:
      enabled: true
    history-level: full
    webapp:
      application-path: /camunda
    cors:
      enabled: true
      allowed-origins: "*"

# 自定义配置
myconfig:
  bpmnFilePathPrefix: ./test-bpmn-files
  bpmnFilePathPostfix: processes
  url: http://localhost:9999

gatewayServer:
  url: http://localhost:8301
messageServer:
  url: http://localhost:8306
configurationServer:
  url: http://localhost:8302

# 日志配置
logging:
  level:
    com.open_care: INFO
    org.springframework: WARN
    org.hibernate: WARN
    org.camunda: INFO
    root: WARN

# 服务端口 - 使用随机端口避免冲突
server:
  port: 0

# 禁用各种服务发现和配置
cloud:
  nacos:
    discovery:
      enabled: false
    config:
      enabled: false

# 禁用Seata
seata:
  enabled: false

# 禁用Sentinel
feign:
  sentinel:
    enabled: false

# Sa-Token配置
sa-token:
  check-same-token: true
  jwt-secret-key: test-secret-key
  alone-redis:
    database: 3
    host: host.docker.internal
    port: 6379
    password: opencare
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: ["health", "info"]
  endpoint:
    health:
      show-details: always 