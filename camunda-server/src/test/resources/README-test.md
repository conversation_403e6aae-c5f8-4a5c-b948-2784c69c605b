# BPM Service 集成测试说明

## 测试概述

本测试套件实现了 BPM Service 的完整集成测试，涵盖从第一层（基础数据层）到第十四层（清理层）的所有功能测试，总计60个测试用例，确保 Camunda 7 流程引擎功能的完整性和正确性。

## 测试环境配置

### 1. 测试框架
- **Spring Boot Test**: 提供完整的Spring容器环境，使用`RANDOM_PORT`模式启动真实HTTP服务器
- **TestRestTemplate**: 执行真实HTTP请求进行集成测试
- **H2 Database**: 内存数据库用于测试数据存储
- **Redis**: 使用开发环境Redis服务器进行缓存和会话管理

### 2. 测试配置文件
- `application-test.yml`: 集成测试环境专用配置
  - 使用H2内存数据库（`jdbc:h2:mem:testdb`）
  - 连接开发环境Redis服务器（`host.docker.internal:6379`）
  - 启用完整的Spring容器和HTTP上下文
  - 配置Camunda引擎参数
  - 设置测试日志级别

### 3. 集成测试特点
- **真实HTTP调用**: 使用TestRestTemplate进行真实的HTTP请求
- **完整Spring容器**: 启动所有Spring组件和服务
- **真实数据库操作**: 使用H2数据库进行真实的数据持久化
- **Redis集成**: 连接真实的Redis服务器进行缓存操作
- **无Mock依赖**: 不使用任何Mock，测试真实的业务逻辑

## 测试文件结构

```
src/test/java/com/open_care/controller/
├── BaseControllerTest.java                      # 集成测试基础类，提供HTTP调用和测试工具方法
├── BmpServiceTestSuite.java                     # 测试套件，按依赖关系顺序执行所有测试
│
├── ProcessEngineControllerBaseTest.java         # 流程引擎控制器基础测试（第一层：基础数据层）
├── ProcessEngineControllerManagementTest.java  # 流程引擎控制器管理测试（第二层：流程定义管理层 + 异常场景）
├── ProcessEngineControllerExtensionTest.java   # 流程引擎控制器扩展测试（扩展功能）
│
├── ProcessControllerInstanceTest.java           # 流程控制器实例测试（第三、四、五层：流程部署后操作 + 流程实例启动 + 实例查询）
├── ProcessControllerTaskTest.java               # 流程控制器任务测试（第六、七、八、九、十层：任务管理生命周期）
├── ProcessControllerHistoryTest.java            # 流程控制器历史测试（第十三、十四层：历史查询 + 清理）
└── ProcessControllerExtensionTest.java          # 流程控制器扩展测试（扩展功能：其他重要接口）
```

## 测试类说明

### BaseControllerTest
集成测试基础类，提供：
- Spring Boot测试配置（`@SpringBootTest`，`RANDOM_PORT`模式）
- TestRestTemplate HTTP调用工具方法
- 测试数据构建工具方法
- BPMN XML生成工具
- 测试DTO类定义
- HTTP请求封装方法（GET、POST等）

### ProcessEngineController 测试类组
实现第一层和第二层的流程定义管理集成测试：

#### ProcessEngineControllerBaseTest
- 第一层基础数据层测试（测试序号001-003）
- 流程定义的基础查询和保存
- 流程组件管理

#### ProcessEngineControllerManagementTest  
- 第二层流程定义管理层测试（测试序号004-010）
- 流程定义的获取、重命名、部署
- 异常场景处理

#### ProcessEngineControllerExtensionTest
- 扩展功能测试（测试序号008-010）
- 其他流程定义管理接口

### ProcessController 测试类组
实现第三层到第十四层的流程实例和任务管理集成测试：

#### ProcessControllerInstanceTest
- 第三、四、五层测试（测试序号007, 011-017）
- 流程部署后操作 + 流程实例启动 + 实例查询

#### ProcessControllerTaskTest
- 第六、七、八、九、十层测试（测试序号018-027）
- 任务管理生命周期：获取、候选人设置、认领、委派、完成

#### ProcessControllerHistoryTest
- 第十三、十四层测试（测试序号028-032）
- 历史查询 + 清理

#### ProcessControllerExtensionTest
- 扩展功能测试（测试序号033-040）
- 其他重要接口

### BmpServiceTestSuite
测试套件类，用于按正确顺序运行所有测试：
- 第一阶段：ProcessEngineController测试组（第一层、第二层）
- 第二阶段：ProcessController测试组（第三层到第十四层）
- 提供测试执行状态和总结信息

## 测试用例列表

### ProcessEngineController 测试用例

#### ProcessEngineControllerBaseTest - 第一层：基础数据层（无依赖）
1. **testQueryProcessDefList_Success** - 查询流程定义列表
2. **testSaveOcprocessDefInfo_Success** - 保存流程定义成功
3. **testGetAllProcessComponents_Success** - 获取所有流程组件

#### ProcessEngineControllerManagementTest - 第二层：流程定义管理层（依赖基础数据）
4. **testGetOcprocessDefInfoById_Success** - 根据ID获取流程定义
5. **testRenameById_Success** - 重命名未部署的流程定义
6. **testDeployProcessdefByOCProcessdefId_Success** - 部署流程定义

#### 异常场景测试
7. **testSaveOcprocessDefInfo_DuplicateKey** - 保存重复的流程Key
8. **testRenameById_AlreadyDeployed** - 重命名已部署的流程定义
9. **testQueryProcessDefList_WithFilters** - 带筛选条件查询
10. **testDeployProcessdefByOCProcessdefId_Exception** - 部署异常处理

#### ProcessEngineControllerExtensionTest - 扩展测试：其他流程定义管理接口
8. **testGetOcprocessDefInfoByIdWithOutVersion_Success** - 根据ID获取流程定义（不指定版本）
9. **testGetAppProcessInfo_Success** - 获取应用流程信息
10. **testImportAppProcessInfo_Success** - 导入应用流程信息

### ProcessController 测试用例

#### ProcessControllerInstanceTest - 第三、四、五层：流程部署后操作 + 流程实例启动 + 实例查询
7. **testGetProcessDefList_Success** - 获取已部署的流程定义列表（第三层）
11. **testStartProcessInstanceByKey_Success** - 根据Key启动流程实例（第四层）
12. **testStartProcessInstanceById_Success** - 根据ID启动流程实例（第四层）
13. **testStartProcessInstanceByKeyAndRunFirstTask_Success** - 启动流程并执行第一个任务（第四层）
14. **testStartProcessInstanceByIdAndRunFirstTask_Success** - 根据ID启动流程并执行第一个任务（第四层）
15. **testFindProcessInstWithPagination_Success** - 分页查询流程实例（第五层）
16. **testGetCurrentTasksByProcDefKey_Success** - 根据流程定义Key查询当前任务（第五层）
17. **testGetCurrentTasksByProcDef_Success** - 根据流程定义ID查询当前任务（第五层）

#### ProcessControllerTaskTest - 第六、七、八、九、十层：任务管理生命周期
18. **testGetTaskByTaskId_Success** - 根据任务ID获取任务（第六层）
19. **testGetCurrentTasksByUser_Success** - 根据用户查询当前任务（第六层）
20. **testFindUserTasksWithPagination_Success** - 分页查询用户任务（第六层）
21. **testCandidateTask_Success** - 设置任务候选人（第七层）
22. **testAssigneeTask_Success** - 分配任务（第七层）
23. **testClaimTask_Success** - 认领任务（第八层）
24. **testUnclaimTask_Success** - 取消认领任务（第八层）
25. **testDelegateTask_Success** - 委派任务（第九层）
26. **testResolveTask_Success** - 解决委派任务（第九层）
27. **testCompleteTask_Success** - 完成任务（第十层）

#### ProcessControllerHistoryTest - 第十三、十四层：历史查询 + 清理
28. **testGetHistoryTasksByProcInst_Success** - 获取流程实例历史任务（第十三层）
29. **testGetTaskAuditHistory_Success** - 获取任务审计历史（第十三层）
30. **testFindUserTaskHistoryWithPagination_Success** - 分页查询用户任务历史（第十三层）
31. **testDeleteTask_Success** - 删除任务（第十四层）
32. **testDeleteProcessInstance_Success** - 删除流程实例（第十四层）

#### ProcessControllerExtensionTest - 扩展测试：其他重要接口
33-40. **各种扩展功能测试** - 身份实体查询、任务优先级修改、变量设置、批量操作等

## 环境要求

### Redis服务器要求
- **主机**: `host.docker.internal`
- **端口**: `6379`
- **密码**: `opencare`
- **数据库**: `3`

确保Redis服务器在测试运行前已启动并可访问。

### H2数据库
- 自动创建内存数据库，无需额外配置
- 每次测试运行时自动初始化干净环境

## 运行测试

### 使用测试脚本（推荐）
```bash
# Linux/Mac 环境
./camunda-server/run-tests.sh

# Windows 环境
camunda-server\run-tests.bat
```

脚本提供以下选项：
1. 运行完整测试套件（推荐）
2. 仅运行流程定义管理测试
3. 仅运行流程实例和任务管理测试
4. 运行所有测试

### 手动运行测试

#### 运行所有测试
```bash
./gradlew :camunda-server:test
```

#### 运行测试套件
```bash
# 运行完整测试套件（推荐）
./gradlew :camunda-server:test --tests BmpServiceTestSuite
```

#### 运行特定测试类
```bash
# 运行流程定义管理测试
./gradlew :camunda-server:test --tests ProcessEngineControllerBaseTest
./gradlew :camunda-server:test --tests ProcessEngineControllerManagementTest
./gradlew :camunda-server:test --tests ProcessEngineControllerExtensionTest

# 运行流程实例和任务管理测试
./gradlew :camunda-server:test --tests ProcessControllerInstanceTest
./gradlew :camunda-server:test --tests ProcessControllerTaskTest
./gradlew :camunda-server:test --tests ProcessControllerHistoryTest
./gradlew :camunda-server:test --tests ProcessControllerExtensionTest
```

#### 运行特定测试方法
```bash
# 运行流程定义保存测试
./gradlew :camunda-server:test --tests ProcessEngineControllerBaseTest.testSaveOcprocessDefInfo_Success

# 运行流程实例启动测试
./gradlew :camunda-server:test --tests ProcessControllerInstanceTest.testStartProcessInstanceByKey_Success
```

### 查看测试报告
测试报告生成在：`camunda-server/build/reports/tests/test/index.html`

## 测试数据管理

### 测试数据隔离
- 每个测试方法使用 `@Transactional` 注解，测试完成后自动回滚
- 使用内存数据库，每次测试运行时都是干净的环境
- 每个测试类维护独立的静态变量，避免类间数据污染

### 测试数据依赖关系
- 测试方法使用 `@Order` 注解严格控制执行顺序
- 静态变量在测试间共享数据状态：
  - **ProcessEngineController测试组**: savedProcessDefId, savedProcessKey, savedProcessName
  - **ProcessController测试组**: processInstanceId, processDefinitionKey, taskId, businessKey
- 测试文件拆分后，通过静态方法在测试类间共享数据：
  - `ProcessControllerInstanceTest.getProcessInstanceId()`
  - `ProcessControllerTaskTest.getTaskId()`
  - 等等
- 依赖关系验证：
  - 流程定义保存 → 流程定义部署 → 流程实例启动 → 任务创建 → 任务处理 → 流程完成
  - 每一层的测试都依赖前一层的成功执行和数据状态

## 集成测试特点

### HTTP集成测试
- **真实HTTP服务器**: 使用`SpringBootTest.WebEnvironment.RANDOM_PORT`启动真实HTTP服务器
- **TestRestTemplate**: 执行真实的HTTP请求，包括GET、POST、PUT、DELETE等
- **完整请求生命周期**: 测试包括HTTP请求解析、Controller处理、Service业务逻辑、数据库操作等完整流程

### 数据持久化测试
- **真实数据库操作**: 使用H2数据库进行真实的数据持久化操作
- **事务管理**: 测试Spring事务管理功能
- **数据完整性**: 验证数据的正确存储和查询

### 缓存集成测试
- **Redis集成**: 连接真实的Redis服务器进行缓存操作
- **会话管理**: 测试基于Redis的会话管理功能
- **缓存一致性**: 验证缓存与数据库的数据一致性

## 注意事项

1. **环境依赖**
   - 确保Redis服务器运行在`host.docker.internal:6379`
   - 确保Redis服务器设置密码为`opencare`
   - 网络连接正常，可以访问Redis服务器

2. **API路径映射**
   - 测试使用的路径必须与相应的Controller定义一致
   - **ProcessEngineController**: 使用实际的Controller路径
   - **ProcessController**: 使用实际的Controller路径

3. **请求方法类型**
   - 注意区分GET、POST、DELETE、PUT请求方法
   - 确保请求头和请求体格式正确

4. **响应格式**
   - 所有响应都封装在 `OcResponse` 对象中或直接返回JSON
   - status="0" 表示成功，status="1" 表示失败
   - 验证响应的HTTP状态码和业务状态码

5. **测试执行顺序**
   - 使用 `@TestMethodOrder(MethodOrderer.OrderAnnotation.class)` 严格控制执行顺序
   - 确保依赖关系正确，避免测试失败连锁反应
   - ProcessEngineController测试必须在ProcessController测试之前执行

6. **状态管理**
   - 流程定义状态：保存 → 部署 → 可用于启动流程实例
   - 任务状态：创建 → 分配/候选 → 认领 → 完成
   - 流程实例状态：启动 → 运行中 → 完成/删除

7. **集成测试性能**
   - 集成测试比单元测试慢，因为启动了完整的Spring容器
   - 包含真实的数据库和网络操作
   - 建议在CI/CD环境中并行运行测试以提高效率

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查Redis服务器是否运行
   - 验证主机名`host.docker.internal`是否可访问
   - 确认Redis密码设置正确

2. **测试顺序执行错误**
   - 确保所有测试类都正确设置了`@Order`注解
   - 检查测试间的数据依赖关系

3. **HTTP请求失败**
   - 检查Controller路径映射是否正确
   - 验证请求方法类型（GET/POST/PUT/DELETE）
   - 确认请求体格式和Content-Type

4. **数据库操作失败**
   - 检查JPA实体映射配置
   - 验证数据库表结构是否正确创建
   - 确认事务配置正确

## 扩展测试

后续可以添加：
- 性能测试：大量数据的查询和处理
- 并发测试：多线程环境下的测试
- 端到端测试：完整业务流程的自动化测试
- 容错测试：异常情况和错误恢复测试 