import org.gradle.internal.impldep.org.junit.experimental.categories.Categories.CategoryFilter.exclude
import org.gradle.kotlin.dsl.implementation

plugins {
    java
    id("org.springframework.boot")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
}

configurations.all {
    // 确保每次构建都获取SNAPSHOT的最新版本
    resolutionStrategy.cacheDynamicVersionsFor(0, "seconds")
    resolutionStrategy.cacheChangingModulesFor(0, "seconds")

}

dependencies {
    implementation(project(":camunda-server-nacos-support"))
    implementation(project(":camunda-pulsar-connector-plugin"))
    implementation(project(":camunda-bpm-service"))
    implementation(project(":camunda-common"))

    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter")
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest")

    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")

    // 添加Spring Boot DevTools依赖，实现快速重启
//    implementation("org.springframework.boot:spring-boot-devtools")

//    implementation("org.flywaydb:flyway-core")
//    implementation("org.flywaydb:flyway-database-postgresql")

    implementation("cn.hutool:hutool-all")

    implementation("com.open-care:hibernate-dialect")
    runtimeOnly("net.logstash.logback:logstash-logback-encoder")

    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")

    testCompileOnly("org.projectlombok:lombok")
    testAnnotationProcessor("org.projectlombok:lombok")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("com.h2database:h2")
    testImplementation("org.springframework.cloud:spring-cloud-starter-bootstrap")
}

// 设置bootJar任务的重复处理策略
tasks.bootJar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}