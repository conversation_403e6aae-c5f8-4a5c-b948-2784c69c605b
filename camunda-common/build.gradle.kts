plugins {
    id("java")
    id("java-library")
}



dependencies {
    implementation(project(":camunda-bpm-core"))

    // Camunda依赖
    implementation("org.camunda.bpm:camunda-engine-rest-core-jakarta")
    // 注释掉可能导致问题的依赖
    // implementation("org.camunda.bpm:camunda-engine-rest-jakarta:7.23.0")
    // 添加缺失的依赖，修改为正确的GroupId
    implementation("org.camunda.bpm:camunda-engine-rest-jakarta")
    implementation("org.camunda.bpm:camunda-engine")
    implementation("org.slf4j:slf4j-api")


    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter")
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp")
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest")
    // 不再使用Reactor实现
     implementation("org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring")
    implementation("org.camunda.bpm:camunda-engine-plugin-spin")
    implementation("org.camunda.spin:camunda-spin-dataformat-json-jackson")
    
    // Jackson依赖
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")


    // Spring依赖
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    
    // API模块依赖
    implementation(project(":camunda-bpm-api"))
    
    // 工具类依赖
    implementation("cn.hutool:hutool-core")
    implementation("cn.hutool:hutool-cache")
    implementation("cn.hutool:hutool-json")
    
    // 单点登录依赖
    api("com.open-care:auth-core")
    api("com.open-care:auth-client")
    api("com.open-care:auth-feign-interrupt")
    // dubbo 相关依赖
//    implementation("com.open-care:auth-dubbo-api")

    implementation("org.apache.dubbo:dubbo-spring-boot-starter")
    implementation("org.apache.dubbo:dubbo-nacos-spring-boot-starter")
    implementation("com.open-care:dubbo-serialization-fury")
    implementation("com.open-care:api-jsonschema-definition-common")


    // 添加Lombok依赖 - 确保Lombok在MapStruct之前
    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")
    
    // 添加MapStruct依赖
    implementation("org.mapstruct:mapstruct")
    annotationProcessor("org.mapstruct:mapstruct-processor")
    
    // Lombok和MapStruct集成
    annotationProcessor("org.projectlombok:lombok-mapstruct-binding")
}

