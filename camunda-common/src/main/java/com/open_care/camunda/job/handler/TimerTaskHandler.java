package com.open_care.camunda.job.handler;

import cn.hutool.json.JSONUtil;
import com.open_care.api.common.dto.TaskEventDTO;
import com.open_care.api.common.enums.TaskEventType;
import com.open_care.camunda.enums.TaskTimeoutType;
import com.open_care.camunda.event.TaskEvent;
import com.open_care.camunda.mapper.TaskConversionContext;
import com.open_care.camunda.mapper.TaskMapper;
import com.open_care.camunda.service.SendEventService;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.impl.interceptor.CommandContext;
import org.camunda.bpm.engine.impl.jobexecutor.JobHandler;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.JobEntity;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 定时任务委托处理器
 * 用于处理定时器触发的任务超时事件
 *
 * <AUTHOR>
 * @date :2025/4/15
 */
@Component
@Log4j2
public class TimerTaskHandler implements JobHandler<TimerTaskConfig> {
    public static final String TYPE = "TimerTaskHandler";

    @Autowired
    @Lazy
    private TaskService taskService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private SendEventService eventClientService;

    /**
     * 为指定任务创建上下文
     */
    private TaskConversionContext createContextForTask(String taskId) {
        return TaskConversionContext.createWithTask(taskService, taskId);
    }

    /**
     * 为多个任务创建上下文
     */
    private TaskConversionContext createContextForTasks(List<String> taskIds) {
        return TaskConversionContext.createWithTasks(taskService, taskIds);
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public void execute(TimerTaskConfig configuration, ExecutionEntity execution, CommandContext commandContext, String tenantId) {

        // 获取任务ID
        String taskId = configuration.getTaskId();

        try {
            // 查询任务是否存在
            Task task = taskService.createTaskQuery()
                    .taskId(taskId)
                    .active()
                    .singleResult();

            if (task == null) {
                log.warn("找不到要处理的任务，可能已完成或被删除: taskId={}", taskId);
                return;
            }

            // 获取超时类型，默认为普通超时
            TaskTimeoutType timeoutType = configuration.getTimeoutType();

            // 记录超时信息
            recordTimeoutOccurrence(taskService, task, timeoutType);

            // 创建上下文，可以在多个任务间共享
            TaskConversionContext context = createContextForTask(taskId);
            
            // 根据超时类型执行不同处理逻辑
            switch (timeoutType) {
                case TIMEOUT_ASSIGNMENT:
                    processAssignmentTimeout(task, context);
                    break;
                case TIMEOUT_COMPLETE:
                    processCompleteTimeout(task, context);
                    break;
                case TIMEOUT_LIFECYCLE:
                default:
                    processDefaultTimeout(task, context);
                    break;
            }

        } catch (Exception e) {
            log.error("处理任务超时过程中发生错误: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    @Override
    public TimerTaskConfig newConfiguration(String canonicalString) {
        return JSONUtil.toBean(canonicalString, TimerTaskConfig.class);
    }

    /**
     * Clean up before job is deleted. Like removing of auxiliary entities specific for this job handler.
     *
     * @param configuration the job handler configuration
     * @param jobEntity     the job entity to be deleted
     */
    @Override
    public void onDelete(TimerTaskConfig configuration, JobEntity jobEntity) {
        // 清理工作，如果需要的话
    }

    /**
     * 记录超时发生情况
     */
    private void recordTimeoutOccurrence(TaskService taskService, Task task, TaskTimeoutType timeoutType) {
        String taskId = task.getId();
        Date currentTime = new Date();

        // 设置超时触发变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("triggerTimeout", true);
        variables.put("timeoutTimestamp", currentTime.getTime());
        variables.put("timeoutDate", currentTime);
        variables.put("timeoutType", timeoutType);
        variables.put("timeoutCount", getTimeoutCount(taskService, taskId) + 1);

        // 设置任务变量
        taskService.setVariablesLocal(taskId, variables);

        log.info("记录任务超时事件: taskId={}, 任务名称={}, 超时类型={}, 时间={}",
                taskId, task.getName(), timeoutType, currentTime);
    }

    /**
     * 获取当前任务的超时计数
     */
    private int getTimeoutCount(TaskService taskService, String taskId) {
        Object countObj = taskService.getVariable(taskId, "timeoutCount");
        if (countObj instanceof Integer) {
            return (Integer) countObj;
        }
        return 0;
    }

    /**
     * 处理任务分配超时
     * 处理任务未被申领的情况
     */
    private void processAssignmentTimeout(Task task, TaskConversionContext context) {
        String taskId = task.getId();
        String taskName = task.getName();
        String processInstanceId = task.getProcessInstanceId();

        log.info("处理任务分配超时: taskId={}, 任务名称={}", taskId, taskName);

        // 创建任务DTO并设置状态
        TaskEventDTO taskDTO = createTaskDTO(task, TaskEventType.ASSIGNMENT_TIMEOUT, context);

        // 发送任务事件
        eventClientService.sendTaskEventAsync(taskDTO);
    }

    /**
     * 处理任务完成超时
     * 处理任务已被申领但未完成的情况
     */
    private void processCompleteTimeout(Task task, TaskConversionContext context) {
        String taskId = task.getId();
        String taskName = task.getName();
        String processInstanceId = task.getProcessInstanceId();
        String assignee = task.getAssignee();

        log.info("处理任务完成超时: taskId={}, 任务名称={}, 受理人={}", taskId, taskName, assignee);

        // 创建任务DTO并设置状态
        TaskEventDTO taskDTO = createTaskDTO(task, TaskEventType.COMPLETE_TIMEOUT, context);

        // 发送任务事件
        eventClientService.sendTaskEventAsync(taskDTO);
    }

    /**
     * 处理默认超时
     * 处理一般性的任务超时，无论任务是否被申领
     */
    private void processDefaultTimeout(Task task, TaskConversionContext context) {
        String taskId = task.getId();
        String taskName = task.getName();

        log.info("处理任务默认超时: taskId={}, 任务名称={}", taskId, taskName);

        // 创建任务DTO并设置状态
        TaskEventDTO taskDTO = createTaskDTO(task, TaskEventType.TIMEOUT_LIFECYCLE, context);

        // 发送任务事件
        eventClientService.sendTaskEventAsync(taskDTO);
    }

    /**
     * 创建任务DTO
     */
    private TaskEventDTO createTaskDTO(Task task, TaskEventType taskEventType, TaskConversionContext context) {
        // 先创建TaskEvent对象
        if (task instanceof DelegateTask delegateTask) {
            // 创建TaskEvent对象
            TaskEvent taskEvent = new TaskEvent(delegateTask);
            
            // 使用TaskMapper将Event对象转换为TaskDTO
            return taskMapper.toTaskEventDTO(taskEvent,taskEventType,context);
        } else {
            // 如果task不是DelegateTask类型，仍然使用旧方法
            return taskMapper.toTaskEventDTO(task,taskEventType,context);
        }
    }
}