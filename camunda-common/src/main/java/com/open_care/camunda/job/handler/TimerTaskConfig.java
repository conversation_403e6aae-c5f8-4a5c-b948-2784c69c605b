package com.open_care.camunda.job.handler;

import cn.hutool.json.JSONUtil;
import com.open_care.camunda.enums.TaskTimeoutType;
import com.open_care.camunda.enums.TimerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.camunda.bpm.engine.impl.jobexecutor.JobHandlerConfiguration;

/**
 * <AUTHOR>
 * @date :2025/4/15
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TimerTaskConfig implements JobHandlerConfiguration {

    private String timerName;

    private TimerType type;

    private String expression;

    private String processInstanceId;

    private String taskId;

    private TaskTimeoutType timeoutType;

    private boolean isSync;


    @Override
    public String toCanonicalString() {
        return JSONUtil.toJsonStr(this);
    }
}
