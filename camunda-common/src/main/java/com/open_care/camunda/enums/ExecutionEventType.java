package com.open_care.camunda.enums;

/**
 * 执行事件类型枚举
 */
public enum ExecutionEventType {
    EXECUTION_START("start", "开始"),
    EXECUTION_END("end", "结束"),
    EXECUTION_TAKE("take", "接管"),
    ;

    private final String code;
    private final String desc;

    ExecutionEventType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ExecutionEventType getByCode(String code) {
        for (ExecutionEventType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}