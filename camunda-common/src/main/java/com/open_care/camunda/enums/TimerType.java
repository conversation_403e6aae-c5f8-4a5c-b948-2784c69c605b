package com.open_care.camunda.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date :2025/4/15
 */
@AllArgsConstructor
@Getter
public enum TimerType {
    DATE("date","指定时间"),
    DURATION("duration","时间段"),
    CYCLE("duration","循环执行")
    ;

    private final String code;

    private final String desc;

    public static TimerType valueOfCode(String code) {
        return Arrays.stream(TimerType.values())
                .filter(type -> type.codeEquals(code))
                .findFirst()
                .orElse(null);
    }

    public boolean codeEquals(String type) {
        return StrUtil.equalsIgnoreCase(type, this.code);
    }
}
