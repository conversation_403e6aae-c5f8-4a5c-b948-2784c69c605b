package com.open_care.camunda.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务超时类型枚举
 */
@AllArgsConstructor
@Getter
public enum TaskTimeoutType {
    TIMEOUT_LIFECYCLE("lifecycle", "持续时间超时"),
    TIMEOUT_ASSIGNMENT("assignment", "分配超时"),
    TIMEOUT_COMPLETE("completion", "完成超时"),
    ;

    private final String eventName;
    private final String desc;




    public boolean codeEquals(String typeCode){
        return StrUtil.equalsIgnoreCase(typeCode, this.eventName);
    }


}