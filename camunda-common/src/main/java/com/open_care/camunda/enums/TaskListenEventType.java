package com.open_care.camunda.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.camunda.bpm.engine.delegate.TaskListener;

/**
 * <AUTHOR>
 * @date :2025/4/14
 */
@AllArgsConstructor
@Getter
public enum TaskListenEventType {

    CREATE(TaskListener.EVENTNAME_CREATE, "创建"),

    ASSIGNMENT(TaskListener.EVENTNAME_ASSIGNMENT,"分配"),

    COMPLETE(TaskListener.EVENTNAME_COMPLETE,"完成"),

    DELETE(TaskListener.EVENTNAME_DELETE,"删除"),

    UPDATE(TaskListener.EVENTNAME_UPDATE,"更新"),

    TIMEOUT(TaskListener.EVENTNAME_TIMEOUT, "超时");

    private final String eventName;
    private final String desc;




}
