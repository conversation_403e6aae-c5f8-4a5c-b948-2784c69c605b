package com.open_care.camunda.event.listener.processor;

import com.open_care.api.client.CamundaListenerService;
import com.open_care.api.common.dto.ExecutionEventDTO;
import com.open_care.api.common.enums.ExecutionEventType;
import com.open_care.camunda.mapper.ExecutionMapper;
import com.open_care.camunda.service.SendEventService;
import com.open_care.camunda.event.context.ExecutionEventContext;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.log4j.Log4j2;

import java.util.List;

/**
 * 执行事件处理器
 * 处理流程执行过程中的事件
 * 
 * <AUTHOR>
 * @date :2025/4/11
 */
@Component
@Log4j2
public class GeneralExecutionEventProcessor {

    @Autowired(required = false)
    private List<CamundaListenerService> executionListeners;
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private ExecutionMapper executionMapper;
    
    @Autowired
    private SendEventService eventClientService;

    /**
     * 处理执行事件
     * 
     * @param context 执行事件上下文
     */
    public void processExecutionEvent(ExecutionEventContext context) {
        boolean isSync = context.isSync();
        
        // 使用Event对象创建执行事件数据DTO
        final ExecutionEventDTO executionEventDTO = executionMapper.toExecutionDTO(context.getEvent());

        // 然后通知远程服务
        if (isSync) {
            eventClientService.sendExecutionEvent(executionEventDTO);
        } else {
            eventClientService.sendExecutionEventAsync(executionEventDTO);
        }
    }
}
