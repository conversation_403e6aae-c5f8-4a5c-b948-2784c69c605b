package com.open_care.camunda.event;

/**
 * 事件接口
 * 用于统一封装Task和Execution类型
 *
 * <AUTHOR>
 * @date :2025/4/20
 * @param <T> 原始对象类型
 */
public interface Event<T> {
    
    /**
     * 获取事件名称
     *
     * @return 事件名称
     */
    String getEventName();
    
    /**
     * 获取对象ID
     *
     * @return 对象ID
     */
    String getId();
    
    /**
     * 获取对象名称
     *
     * @return 对象名称
     */
    String getName();
    
    /**
     * 获取流程实例ID
     *
     * @return 流程实例ID
     */
    String getProcessInstanceId();
    
    /**
     * 获取流程定义ID
     *
     * @return 流程定义ID
     */
    String getProcessDefinitionId();
    
    /**
     * 获取对象类型
     * 
     * @return 对象类型（"task"或"execution"）
     */
    String getEventType();
    
    /**
     * 获取变量或扩展属性值
     * 
     * @param propName 属性名
     * @param defaultValue 默认值
     * @return 属性值
     */
    String getVariableOrExtensionProperty(String propName, String defaultValue);
    
    /**
     * 检查是否应触发特定事件
     * 
     * @return 是否应触发
     */
    boolean shouldTriggerEvent();
    
    /**
     * 检查是否需要同步处理事件
     * 
     * @return 是否同步处理
     */
    boolean isSyncEvent();
    
    /**
     * 获取原始对象
     *
     * @return 原始对象
     */
    T getOriginal();
} 