package com.open_care.camunda.event.listener;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.camunda.util.ProcessUtil;
import com.open_care.bpm.core.ProcessVariableUtil;
import com.open_care.camunda.enums.TaskListenEventType;
import com.open_care.camunda.event.context.TaskEventContext;
import com.open_care.camunda.event.TaskEvent;
import com.open_care.camunda.event.listener.processor.GeneralTaskEventProcessor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.camunda.bpm.extension.reactor.bus.CamundaSelector;
import org.camunda.bpm.extension.reactor.spring.listener.ReactorTaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 基于注解的任务事件监听器
 * 使用注解自动注册，替代传统的插件注入方式
 *
 * <AUTHOR>
 * @date :2025/4/25
 */
@Component
@CamundaSelector
@Log4j2
public class ReactorTaskEventListener extends ReactorTaskListener implements TaskListener {

    @Autowired
    @Lazy
    private GeneralTaskEventProcessor taskEventProcessor;

    @Override
    public void notify(DelegateTask delegateTask) {
        // 创建事件对象
        TaskEvent event = new TaskEvent(delegateTask);
        String eventName = event.getEventName();

        // 超时事件不做处理，通过定时任务实现
        if (StrUtil.equals(eventName, TaskListenEventType.TIMEOUT.getEventName())) {
            log.debug("超时通知  事件={}, 任务ID={}, 任务名称={}，直接跳过", eventName, delegateTask.getId(), delegateTask.getName());
            return;
        }

        log.info("任务监听器触发: 事件={}, 任务ID={}, 任务名称={}",
                eventName, delegateTask.getId(), delegateTask.getName());
        // 尝试发送事件
        trySendEvent(event, eventName);

        // 创建超时定时任务
        if (StrUtil.equals(eventName, TaskListenEventType.CREATE.getEventName())) {
            // 创建超时定时任务
            taskEventProcessor.checkAndCreateTimerTasksForCreation(event);
            // 根据扩展属性设置相关任务变量
            initTaskVariable(event);
        }
        if (StrUtil.equals(eventName, TaskListenEventType.ASSIGNMENT.getEventName())) {
            // 检查并创建申领后完成超时定时任务
            taskEventProcessor.checkAndCreateTimerTasksForAssignment(event);
        }
    }

    /**
     * 初始化任务变量
     * @param event
     */
    private void initTaskVariable(TaskEvent event) {
        initIsAssignment(event);
    }

    private static void initIsAssignment(TaskEvent event) {
        String isAssignmentStr = event.getVariableOrExtensionProperty(ProcessVariableUtil.VARIABLE_TASK_IS_ASSIGNMENT, Boolean.FALSE.toString());
        boolean isAssignment = BooleanUtil.toBoolean(isAssignmentStr);

        event.getOriginal().setVariableLocal(ProcessVariableUtil.VARIABLE_TASK_IS_ASSIGNMENT, ProcessUtil.createTypedVariable(isAssignment));
    }

    private void trySendEvent(TaskEvent event, String eventName) {
        // 直接使用event中的shouldTriggerEvent方法
        if (!event.shouldTriggerEvent()) {
            log.debug("跳过处理任务{}事件 - 任务ID: {}", eventName, event.getId());
            return;
        }

        // 创建任务事件上下文并处理
        taskEventProcessor.processTaskEvent(new TaskEventContext(event, event.isSyncEvent()));
    }
} 