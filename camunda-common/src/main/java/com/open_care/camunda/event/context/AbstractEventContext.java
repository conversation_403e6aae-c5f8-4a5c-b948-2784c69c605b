package com.open_care.camunda.event.context;

import com.open_care.camunda.event.Event;

/**
 * 事件上下文抽象基类
 * 实现基本的上下文功能
 *
 * @param <T> 原始对象类型
 * <AUTHOR>
 * @date :2025/4/20
 */
public abstract class AbstractEventContext<T> implements EventContext<T> {

    /**
     * 事件对象
     */
    protected final Event<T> event;

    /**
     * 是否同步处理
     */
    protected final boolean sync;


    /**
     * 构造函数
     *
     * @param event 事件对象
     * @param sync  是否同步处理
     */
    public AbstractEventContext(Event<T> event, boolean sync) {
        this.event = event;
        this.sync = sync;

    }


    @Override
    public Event<T> getEvent() {
        return event;
    }

    @Override
    public boolean isSync() {
        return sync;
    }


} 