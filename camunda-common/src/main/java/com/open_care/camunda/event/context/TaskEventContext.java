package com.open_care.camunda.event.context;

import com.open_care.camunda.event.Event;
import org.camunda.bpm.engine.delegate.DelegateTask;

/**
 * 任务事件上下文
 * 提供任务事件相关的上下文信息
 *
 * <AUTHOR>
 * @date :2025/4/20
 */
public class TaskEventContext extends AbstractEventContext<DelegateTask> {

    /**
     * 构造函数
     *
     * @param event 任务事件对象
     * @param sync  是否同步处理
     */
    public TaskEventContext(Event<DelegateTask> event, boolean sync) {
        super(event, sync);
    }


    /**
     * 获取任务对象
     *
     * @return 任务对象
     */
    public DelegateTask getTask() {
        return getEvent().getOriginal();
    }

} 