package com.open_care.camunda.event;

import com.open_care.camunda.util.EventListenConst;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import com.open_care.camunda.util.PropertyJsonWrapper;
import lombok.Getter;
import org.camunda.bpm.engine.delegate.DelegateTask;

/**
 * 任务事件
 * 用于封装Task对象
 *
 * <AUTHOR>
 * @date :2025/4/20
 */
public class TaskEvent implements Event<DelegateTask> {

    private final DelegateTask delegateTask;

    @Getter
    private final PropertyJsonWrapper propsWrapper;

    private static final String EVENT_TYPE = EventListenConst.TASK;

    public TaskEvent(DelegateTask delegateTask) {
        this.delegateTask = delegateTask;
        // 获取所有属性
        this.propsWrapper = ExtensionPropertyUtils.getAllPropertiesAsJsonWrapper(delegateTask);
    }

    @Override
    public String getEventName() {
        return delegateTask.getEventName();
    }

    @Override
    public String getId() {
        return delegateTask.getId();
    }

    @Override
    public String getName() {
        return delegateTask.getName();
    }

    @Override
    public String getProcessInstanceId() {
        return delegateTask.getProcessInstanceId();
    }

    @Override
    public String getProcessDefinitionId() {
        return delegateTask.getProcessDefinitionId();
    }

    @Override
    public String getEventType() {
        return EVENT_TYPE;
    }

    @Override
    public String getVariableOrExtensionProperty(String propName, String defaultValue) {
        return propsWrapper.getPropWithDefaultValue(EventListenConst.EMPTY, propName, defaultValue);
    }

    @Override
    public boolean shouldTriggerEvent() {
        // 直接使用相对路径访问task.listen前缀下的属性
        return getPropsWrapper().getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(EventListenConst.TASK,EventListenConst.LISTEN),
                EventListenConst.EMPTY,
                true,
                getEventName(),
                EventListenConst.ENABLE

        );
    }

    @Override
    public boolean isSyncEvent() {
        return getPropsWrapper().getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(EventListenConst.TASK,EventListenConst.LISTEN),
                EventListenConst.SYNC,
                false,
                getEventName()
        );
    }

    @Override
    public DelegateTask getOriginal() {
        return delegateTask;
    }

} 