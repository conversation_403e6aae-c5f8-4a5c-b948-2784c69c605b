package com.open_care.camunda.event.listener.processor;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.api.common.dto.TaskEventDTO;
import com.open_care.api.common.enums.TaskEventType;
import com.open_care.camunda.command.CreateTimerJobCommand;
import com.open_care.camunda.enums.TaskTimeoutType;
import com.open_care.camunda.enums.TaskTimerProp;
import com.open_care.camunda.event.TaskEvent;
import com.open_care.camunda.job.handler.TimerTaskHandler;
import com.open_care.camunda.enums.TimerType;
import com.open_care.camunda.mapper.TaskConversionContext;
import com.open_care.camunda.mapper.TaskMapper;
import com.open_care.camunda.service.SendEventService;
import com.open_care.camunda.event.context.TaskEventContext;
import com.open_care.camunda.util.EventListenConst;
import com.open_care.camunda.util.PropertyJsonWrapper;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;

import java.util.List;
import java.util.Map;

/**
 * 任务事件处理器
 * 处理各种任务事件，实现业务逻辑
 *
 * <AUTHOR>
 * @date :2025/4/11
 */
@Component
@Log4j2
public class GeneralTaskEventProcessor {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private SendEventService eventClientService;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private CommandExecutor commandExecutor;

    @Autowired
    private TaskService taskService;

    // 用于处理超时任务的默认Java类
    private static final String DEFAULT_TIMEOUT_HANDLER_CLASS = TimerTaskHandler.class.getName();

    private static final String AUTO_COMPLETE_TASKKEY = "autoCompleteTaskKey";

    // BusinessCalendar实例，用于验证定时器表达式
    private static final org.camunda.bpm.engine.impl.calendar.BusinessCalendar DATE_CALENDAR =
            new org.camunda.bpm.engine.impl.calendar.DueDateBusinessCalendar();
    private static final org.camunda.bpm.engine.impl.calendar.BusinessCalendar DURATION_CALENDAR =
            new org.camunda.bpm.engine.impl.calendar.DurationBusinessCalendar();
    private static final org.camunda.bpm.engine.impl.calendar.BusinessCalendar CYCLE_CALENDAR =
            new org.camunda.bpm.engine.impl.calendar.CycleBusinessCalendar();

    /**
     * 处理任务事件
     *
     * @param context 任务事件上下文
     */
    public void processTaskEvent(TaskEventContext context) {
        String eventName = context.getEvent().getEventName();
        DelegateTask task = context.getTask();
        boolean isSync = context.isSync();

        // 处理各种任务事件
        String taskId = task.getId();
        // 使用Event对象创建任务事件监听DTO
        TaskEventDTO taskEventDTO = taskMapper.toTaskEventDTO(context.getEvent(), TaskEventType.valueOfStatus(eventName), TaskConversionContext.createWithTask(taskService, taskId));

        boolean autoCompleted = false;

        // 使用switch表达式处理不同的事件类型
        switch (eventName) {
            case TaskListener.EVENTNAME_CREATE -> {
                autoCompleted = handleTaskCreate(task);
            }
        }
        if (autoCompleted) {
            log.info("任务自动完成: {}", taskEventDTO);
            return;
        }

        // 发送任务事件DTO
        if (isSync) {
            eventClientService.sendTaskEvent(taskEventDTO);
        } else {
            eventClientService.sendTaskEventAsync(taskEventDTO);
        }
    }

    /**
     * 处理任务创建事件
     *
     * @param task 任务对象
     * @return 创建的任务DTO，如果任务自动完成则返回null
     */
    private boolean handleTaskCreate(DelegateTask task) {
        String taskId = task.getId();
        String taskName = task.getName();
        log.debug("处理任务创建事件 - ID: {}, 名称: {}", taskId, taskName);

        // 初始化任务候选人
        initTaskExecutors(task);

        // 检查是否自动完成
        String autoCompleteTaskKey = MapUtil.getStr(task.getVariables(), AUTO_COMPLETE_TASKKEY);
        if (task.getTaskDefinitionKey().equals(autoCompleteTaskKey)) {
            task.removeVariable(AUTO_COMPLETE_TASKKEY);
            task.complete();
            // 完成后不需要继续处理
            return true;
        }

        // 任务优先级默认处理
        initTaskPriority(task);

        return false;
    }

    /**
     * 处理任务分配事件
     *
     * @param task 任务对象
     * @return 创建的任务DTO，如果不需要处理则返回null
     */
    private void handleTaskAssignment(DelegateTask task) {
        String taskId = task.getId();
        String assignee = task.getAssignee();
        log.debug("处理任务分配事件 - ID: {}, 分配给: {}", taskId, assignee);
    }

    /**
     * 初始化任务候选人
     */
    private void initTaskExecutors(DelegateTask delegateTask) {
        // 简化版的任务候选人初始化
        Map<String, Object> variables = delegateTask.getVariables();
        if (variables.containsKey("candidates")) {
            Object candidates = variables.get("candidates");
            if (candidates instanceof List<?> candidateList) {
                if (candidateList.size() == 1) {
                    // 单个候选人，设为受理人
                    delegateTask.setAssignee(candidateList.get(0).toString());
                } else {
                    // 多个候选人，添加候选人
                    for (Object candidate : candidateList) {
                        delegateTask.addCandidateUser(candidate.toString());
                    }
                    // 发送任务创建事件已注释掉
                    // sendTaskEvent(delegateTask, TaskEventType.CREATE.getCode(), false);
                }
            }
        }
    }

    /**
     * 初始化任务优先级
     */
    private void initTaskPriority(DelegateTask delegateTask) {
        // 默认优先级处理
        Object priorityVar = delegateTask.getVariable("priority");
        if (ObjectUtil.isNull(priorityVar)) {
            priorityVar = runtimeService.getVariable(delegateTask.getProcessInstanceId(), "priority");
            if (ObjectUtil.isNotNull(priorityVar)) {
                delegateTask.setVariable("priority", priorityVar);
            }
        }

        // 如果有优先级设置，则设置任务优先级
        if (ObjectUtil.isNotNull(priorityVar)) {
            try {
                int priority = Integer.parseInt(priorityVar.toString());
                delegateTask.setPriority(priority);
            } catch (NumberFormatException e) {
                log.warn("任务优先级格式不正确: {}", priorityVar);
            }
        }
    }

    /**
     * 检查并创建任务创建时的超时定时任务
     * 主要为了处理任务创建时的超时检查
     *
     * @param taskEvent 任务对象
     */
    public void checkAndCreateTimerTasksForCreation(TaskEvent taskEvent) {
        String processInstanceId = taskEvent.getProcessInstanceId();
        String taskId = taskEvent.getId();
        DelegateTask delegateTask = taskEvent.getOriginal();

        // 直接使用TaskEvent中已经获取的task.listen前缀属性
        PropertyJsonWrapper propsWrapper = taskEvent.getPropsWrapper();

        // 创建默认超时作业
        checkAndCreateTimeoutJob(
                delegateTask,
                propsWrapper,
                processInstanceId,
                taskId,
                TaskTimeoutType.TIMEOUT_LIFECYCLE
        );

        // 创建申领超时作业
        checkAndCreateTimeoutJob(
                delegateTask,
                propsWrapper,
                processInstanceId,
                taskId,
                TaskTimeoutType.TIMEOUT_ASSIGNMENT
        );
    }

    /**
     * 检查并创建任务分配后的超时定时任务
     * 主要为了处理任务分配后完成的超时检查
     * 需要检查全局超时开关 task.listen.timeout.enable
     *
     * @param taskEvent 任务对象
     */
    public void checkAndCreateTimerTasksForAssignment(TaskEvent taskEvent) {
        String processInstanceId = taskEvent.getOriginal().getProcessInstanceId();
        String taskId = taskEvent.getOriginal().getId();
        DelegateTask delegateTask = taskEvent.getOriginal();

        // 直接使用TaskEvent中已经获取的task.listen前缀属性
        PropertyJsonWrapper propsWrapper = taskEvent.getPropsWrapper();

        // 创建完成超时作业
        checkAndCreateTimeoutJob(
                delegateTask,
                propsWrapper,
                processInstanceId,
                taskId,
                TaskTimeoutType.TIMEOUT_COMPLETE
        );
    }

    /**
     * 检查配置并创建超时作业
     *
     * @param delegateTask      任务对象
     * @param propsWrapper      属性包装器
     * @param processInstanceId 流程实例ID
     * @param taskId            任务ID
     * @param timeoutType       超时类型枚举
     * @return 是否成功创建了任何超时作业
     */
    private void checkAndCreateTimeoutJob(
            DelegateTask delegateTask,
            PropertyJsonWrapper propsWrapper,
            String processInstanceId,
            String taskId,
            TaskTimeoutType timeoutType
    ) {
        // 检查和创建基本超时任务（无后缀）
        checkAndCreateTimeoutJob(propsWrapper, processInstanceId, taskId, timeoutType, StrUtil.EMPTY);

        // 检查和创建带数字后缀的超时任务
        checkAndCreateNumericSuffixTimeoutJobs(propsWrapper, processInstanceId, taskId, timeoutType);
    }

    /**
     * 检查配置并创建超时作业（带后缀）
     *
     * @param propsWrapper      属性包装器
     * @param processInstanceId 流程实例ID
     * @param taskId            任务ID
     * @param timeoutType       超时类型枚举
     * @param timerName         后缀名称（如果是数字后缀，则传入数字；如果是基本配置，则传入空字符串）
     */
    private void checkAndCreateTimeoutJob(PropertyJsonWrapper propsWrapper, String processInstanceId, String taskId, TaskTimeoutType timeoutType, String timerName) {
        // 先检查特定超时类型开关，再检查全局开关
        boolean timerSwitch = propsWrapper.getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(EventListenConst.TASK, EventListenConst.LISTEN),
                timerName,
                true,
                EventListenConst.TIMEOUT,
                timerName,
                timeoutType.getEventName(),
                EventListenConst.ENABLE
        );

        if (!timerSwitch) {
            log.info("不创建超时任务 - 超时类型: {}, 后缀: {}, 任务ID: {}", timeoutType, StrUtil.isBlank(timerName) ? "无" : timerName, taskId);
            return;
        }

        // 由于我们已经使用task.listen前缀过滤了属性，这里应该使用相对路径
        String effectiveTimeoutPrefix = ExtensionPropertyUtils.compose(EventListenConst.TASK, EventListenConst.LISTEN, EventListenConst.TIMEOUT);

        // 获取超时类型和表达式（按路径优先级获取）
        String typeValue = propsWrapper.getProp(effectiveTimeoutPrefix, TaskTimerProp.TYPE.getName(), timerName, timeoutType.getEventName());
        String expressValue = propsWrapper.getPropWithDefaultValue(effectiveTimeoutPrefix, TaskTimerProp.EXPRESS.getName(), timerName, timeoutType.getEventName());

        boolean isSync = propsWrapper.getBooleanByPrefixPathSuffix(effectiveTimeoutPrefix, EventListenConst.SYNC, false, timerName, timeoutType.getEventName());

        // 判断timer配置是否有效
        if (isValidTimerConfig(typeValue, expressValue)) {
            TimerType type = TimerType.valueOfCode(typeValue);
            createTimerJob(timerName, type, expressValue, processInstanceId, taskId, timeoutType, isSync);
            log.info("成功创建超时任务 - 类型: {}, 表达式: {}, 超时类型: {}, 后缀: {}, 任务ID: {}",
                    type, expressValue, timeoutType, StrUtil.isBlank(timerName) ? "无" : timerName, taskId);
        } else {
            log.debug("跳过超时任务创建 - 无效的定时器配置: 类型={}, 表达式={}, 后缀={}, 任务ID={}",
                    typeValue, expressValue, StrUtil.isBlank(timerName) ? "无" : timerName, taskId);
        }
    }

    /**
     * 检查并创建带数字后缀的超时作业
     *
     * @param propsWrapper      属性包装器
     * @param processInstanceId 流程实例ID
     * @param taskId            任务ID
     * @param timeoutType       超时类型
     * @return 是否成功创建了任何超时作业
     */
    private void checkAndCreateNumericSuffixTimeoutJobs(
            PropertyJsonWrapper propsWrapper,
            String processInstanceId,
            String taskId,
            TaskTimeoutType timeoutType
    ) {
        // 由于我们已经使用task.listen前缀过滤了属性，这里timeoutPrefix应该是相对于task.listen的路径
        String timeoutPrefix = ExtensionPropertyUtils.compose(EventListenConst.TASK, EventListenConst.LISTEN, EventListenConst.TIMEOUT);

        // 获取task.listen.timeout下的所有子属性名称
        List<String> subProps = propsWrapper.getKeysByPrefix(timeoutPrefix);

        if (subProps.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug("未找到任何超时子配置: taskId={}", taskId);
            }
            return;
        }

        // 筛选出数字后缀的属性
        List<String> numericSuffixes = subProps.stream()
                .filter(prop -> prop.matches("\\d+"))  // 仅匹配纯数字
                .toList();

        if (numericSuffixes.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug("未找到有效的数字后缀超时配置: taskId={}", taskId);
            }
            return;
        }

        log.debug("找到{}个数字后缀超时配置: {}, taskId={}",
                numericSuffixes.size(), String.join(",", numericSuffixes), taskId);

        // 处理每个数字后缀配置
        for (String number : numericSuffixes) {
            // 调用带后缀参数的方法
            checkAndCreateTimeoutJob(propsWrapper, processInstanceId, taskId, timeoutType, number);
        }
    }

    /**
     * 创建定时任务
     *
     * @param type              定时任务类型
     * @param express           定时表达式
     * @param processInstanceId 流程实例ID
     * @param taskId            任务ID
     * @param timeoutType       超时类型
     * @return 是否成功创建
     */
    private boolean createTimerJob(
            String timerName,
            TimerType type,
            String express,
            String processInstanceId,
            String taskId,
            TaskTimeoutType timeoutType,
            boolean isSync
    ) {
        try {
            // 参数校验
            if (type == null || StrUtil.isBlank(express)) {
                log.warn("创建定时任务失败 - 参数无效: type={}, express={}, taskId={}", type, express, taskId);
                return false;
            }

            // 使用CreateTimerJobCommand创建定时任务
            commandExecutor.execute(
                    new CreateTimerJobCommand(
                            timerName,
                            processInstanceId,
                            taskId,
                            type,
                            express,
                            timeoutType,
                            isSync
                    ));
            log.info("成功创建定时任务 - 类型: {}, 表达式: {}, 超时类型: {}, 任务ID: {}",
                    type, express, timeoutType, taskId);
            return true;
        } catch (Exception e) {
            log.error("创建定时任务失败 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查定时器配置是否有效
     *
     * @param typeValue    定时器类型值
     * @param expressValue 定时器表达式值
     * @return 是否有效
     */
    private boolean isValidTimerConfig(String typeValue, String expressValue) {
        // 类型和表达式都不能为空
        if (StrUtil.isBlank(typeValue) || StrUtil.isBlank(expressValue)) {
            return false;
        }

        try {
            // 检查类型是否有效
            TimerType type = TimerType.valueOfCode(typeValue);
            if (type == null) {
                return false;
            }

            // 根据不同类型验证表达式
            switch (type) {
                case DATE:
                    // 日期格式验证，尝试使用DueDateBusinessCalendar解析表达式
                    try {
                        DATE_CALENDAR.resolveDuedate(expressValue);
                        return true;
                    } catch (Exception e) {
                        log.warn("日期表达式验证失败: 表达式={}, 错误: {}", expressValue, e.getMessage());
                        return false;
                    }
                case DURATION:
                    // 持续时间格式验证，使用DurationBusinessCalendar解析表达式
                    try {
                        DURATION_CALENDAR.resolveDuedate(expressValue);
                        return true;
                    } catch (Exception e) {
                        log.warn("持续时间表达式验证失败: 表达式={}, 错误: {}", expressValue, e.getMessage());
                        return false;
                    }
                case CYCLE:
                    // 周期格式验证，使用CycleBusinessCalendar解析表达式
                    try {
                        CYCLE_CALENDAR.resolveDuedate(expressValue);
                        return true;
                    } catch (Exception e) {
                        log.warn("周期表达式验证失败: 表达式={}, 错误: {}", expressValue, e.getMessage());
                        return false;
                    }
                default:
                    return false;
            }
        } catch (Exception e) {
            log.warn("验证定时器配置失败: 类型={}, 表达式={}, 错误: {}", typeValue, expressValue, e.getMessage());
            return false;
        }
    }


}