package com.open_care.camunda.event.listener;

import com.open_care.camunda.event.context.ExecutionEventContext;
import com.open_care.camunda.event.ExecutionEvent;
import com.open_care.camunda.event.listener.processor.GeneralExecutionEventProcessor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.extension.reactor.bus.CamundaSelector;
import org.camunda.bpm.extension.reactor.spring.listener.ReactorExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 基于注解的执行事件监听器
 * 使用注解自动注册，替代传统的插件注入方式
 *
 * <AUTHOR>
 * @date :2025/4/25
 */
@Component
@CamundaSelector
@Log4j2
public class ReactorExecutionEventListener extends ReactorExecutionListener implements ExecutionListener {

    @Autowired
    @Lazy
    private GeneralExecutionEventProcessor executionEventProcessor;


    @Override
    public void notify(DelegateExecution execution) {
        // 创建执行事件对象
        ExecutionEvent event = new ExecutionEvent(execution);
        String eventName = event.getEventName();

        log.debug("执行监听器触发: 事件={}, 执行ID={}, 活动ID={}",
                eventName, execution.getId(), execution.getCurrentActivityId());

        // 直接使用event中的shouldTriggerEvent方法
        if (!event.shouldTriggerEvent()) {
            log.debug("跳过处理执行{}事件 - 执行ID: {}", eventName, event.getId());
            return;
        }

        // 创建执行事件上下文并处理
        executionEventProcessor.processExecutionEvent( new ExecutionEventContext(event, event.isSyncEvent()));
    }
} 