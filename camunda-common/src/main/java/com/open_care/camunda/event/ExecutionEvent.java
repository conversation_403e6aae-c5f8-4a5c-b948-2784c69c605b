package com.open_care.camunda.event;

import com.open_care.camunda.util.EventListenConst;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import com.open_care.camunda.util.PropertyJsonWrapper;
import lombok.Getter;
import org.camunda.bpm.engine.delegate.DelegateExecution;

/**
 * 执行事件
 * 用于封装Execution对象
 *
 * <AUTHOR>
 * @date :2025/4/20
 */
public class ExecutionEvent implements Event<DelegateExecution> {

    private final DelegateExecution delegateExecution;
    @Getter
    private final PropertyJsonWrapper propsWrapper;

    private static final String EVENT_TYPE = EventListenConst.EXECUTION;

    public ExecutionEvent(DelegateExecution delegateExecution) {
        this.delegateExecution = delegateExecution;
        // 获取所有属性
        this.propsWrapper = ExtensionPropertyUtils.getAllPropertiesAsJsonWrapper(delegateExecution);
    }

    @Override
    public String getEventName() {
        return delegateExecution.getEventName();
    }

    @Override
    public String getId() {
        return delegateExecution.getId();
    }

    @Override
    public String getName() {
        // Execution没有name属性，使用processDefinitionId作为替代
        return delegateExecution.getProcessDefinitionId();
    }

    @Override
    public String getProcessInstanceId() {
        return delegateExecution.getProcessInstanceId();
    }

    @Override
    public String getProcessDefinitionId() {
        return delegateExecution.getProcessDefinitionId();
    }

    @Override
    public String getEventType() {
        return EVENT_TYPE;
    }

    @Override
    public String getVariableOrExtensionProperty(String propName, String defaultValue) {
        return propsWrapper.getPropWithDefaultValue("", propName, defaultValue);
    }

    @Override
    public boolean shouldTriggerEvent() {
        String eventName = getEventName();
        // 直接使用相对路径访问execution.listen前缀下的属性
        return getPropsWrapper().getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(EventListenConst.EXECUTION, EventListenConst.LISTEN),
                EventListenConst.EMPTY,
                false,
                eventName,
                EventListenConst.ENABLE
        );
    }

    @Override
    public boolean isSyncEvent() {
        String eventName = getEventName();
        // 直接使用相对路径访问execution.listen前缀下的属性
        return getPropsWrapper().getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(EventListenConst.EXECUTION, EventListenConst.LISTEN),
                EventListenConst.SYNC,
                false,
                eventName
        );
    }

    @Override
    public DelegateExecution getOriginal() {
        return delegateExecution;
    }


} 