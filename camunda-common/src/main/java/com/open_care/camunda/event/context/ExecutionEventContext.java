package com.open_care.camunda.event.context;

import com.open_care.camunda.event.Event;
import org.camunda.bpm.engine.delegate.DelegateExecution;

/**
 * 执行事件上下文
 * 提供执行事件相关的上下文信息
 *
 * <AUTHOR>
 * @date :2025/4/20
 */
public class ExecutionEventContext extends AbstractEventContext<DelegateExecution> {

    /**
     * 构造函数
     *
     * @param event 执行事件对象
     * @param sync 是否同步处理
     */
    public ExecutionEventContext(Event<DelegateExecution> event, boolean sync) {
        super(event, sync);
    }

    
    /**
     * 获取执行对象
     *
     * @return 执行对象
     */
    public DelegateExecution getExecution() {
        return getEvent().getOriginal();
    }

}