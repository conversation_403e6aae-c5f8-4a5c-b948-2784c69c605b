package com.open_care.camunda.util;

import cn.hutool.json.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.bpm.core.ProcessVariableUtil;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.TypedValue;
import org.camunda.spin.Spin;
import org.camunda.spin.json.SpinJsonNode;
import org.camunda.spin.plugin.variable.value.impl.JsonValueImpl;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.Map;

/**
 * 流程相关工具类
 *
 * <AUTHOR>
 * @date :2024/07/26
 */
@Component
public class ProcessUtil extends ProcessVariableUtil implements InitializingBean {

    public static ObjectMapper OBJECT_MAPPER;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void afterPropertiesSet() throws Exception {
        OBJECT_MAPPER = objectMapper;
    }

    /**
     * 创建 Camunda 类型化变量的静态方法，方便外部调用。
     * 该方法与实例方法功能相同，但可以在没有Spring容器的环境中使用。
     *
     * @param value 变量值
     * @return TypedValue
     */
    public static TypedValue createTypedVariable(Object value) {
        if (value == null) {
            return Variables.untypedNullValue();
        }

        // 如果是枚举类型，则取其名称
        if (value instanceof Enum) {
            return Variables.stringValue(((Enum<?>) value).name());
        }

        // 判断是否是 Camunda 内置支持的类型
        if (value instanceof File) {
            return Variables.fileValue((File) value);
        } else if (value instanceof String) {
            return Variables.stringValue((String) value);
        } else if (value instanceof Boolean) {
            return Variables.booleanValue((Boolean) value);
        } else if (value instanceof Integer) {
            return Variables.integerValue((Integer) value);
        } else if (value instanceof Long) {
            return Variables.longValue((Long) value);
        } else if (value instanceof Double || value instanceof Float) {
            return Variables.doubleValue(((Number) value).doubleValue());
        } else if (value instanceof Date) {
            return Variables.dateValue((Date) value);
        } else if (value instanceof byte[]) {
            return Variables.byteArrayValue((byte[]) value);
        } else if (value instanceof Short) {
            return Variables.shortValue((Short) value);
        } else if (value instanceof SpinJsonNode | value instanceof JsonNode | value instanceof JSON) {
            return new JsonValueImpl(value.toString());
        } else if (value instanceof Map<?, ?>) {
            return new JsonValueImpl(Spin.JSON(value).toString());
        } else {
            // 序列化失败时，回退到默认的 object 类型
            return Variables.objectValue(value).serializationDataFormat(Variables.SerializationDataFormats.JSON).create();
        }
    }
}