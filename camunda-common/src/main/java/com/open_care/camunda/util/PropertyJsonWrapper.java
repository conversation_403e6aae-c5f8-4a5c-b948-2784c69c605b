package com.open_care.camunda.util;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 属性Json包装器，支持层级访问和扁平访问
 * 重新实现，不再依赖JSONObject
 *
 * <AUTHOR>
 * @date :2025/4/11
 */
@Log4j2
public class PropertyJsonWrapper {
    private final Map<String, Object> flatProperties = new HashMap<>();
    /**
     * 创建属性包装器
     *
     * @param properties 属性映射
     */
    public PropertyJsonWrapper(Map<String, Object> properties) {
        if (properties != null) {
            initProperties(properties);
        }
    }

    /**
     * 初始化属性，构建扁平和层级结构
     *
     * @param properties 属性映射
     */
    private void initProperties(Map<String, Object> properties) {
        // 先将所有属性添加到扁平map
        this.flatProperties.putAll(properties);
    }


    
    /**
     * 获取属性值，支持"enable"属性简化
     * 如果只有路径本身作为键存在且值为true，也视为启用
     * 例如：
     * "task.listen.timeout=true" 等同于 "task.listen.timeout.enable=true"
     *
     * @param path 属性路径
     * @param defaultValue 默认值
     * @return 属性值
     */
    public String get(String path, String defaultValue) {
        if (StrUtil.isBlank(path)) {
            return defaultValue;
        }
        
        // 先尝试直接获取
        Object value = flatProperties.get(path);
        if (value != null) {
            return value.toString();
        }
        
        return defaultValue;
    }

    
    /**
     * 根据前缀、后缀和中间路径获取属性值
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 属性值
     */
    public String getProp(String prefix, String suffix,  String... paths) {
        return getPropWithDefaultValue(prefix, suffix, null, paths);
    }
    
    /**
     * 根据前缀、后缀和默认值获取属性值
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @param defaultValue 默认值
     * @return 属性值
     */
    public String getPropWithDefaultValue(String prefix, String suffix, String defaultValue) {
        if (StrUtil.isBlank(prefix) && StrUtil.isBlank(suffix)) {
            return defaultValue;
        }
        
        String path = buildPath(prefix, suffix);
        return get(path, defaultValue);
    }
    
    /**
     * 根据前缀、路径和后缀获取属性值
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @param defaultValue 默认值
     * @param paths 中间路径
     * @return 属性值
     */
    public String getPropWithDefaultValue(String prefix, String suffix, String defaultValue, String... paths) {
        if ((StrUtil.isBlank(prefix) && StrUtil.isBlank(suffix)) && (paths == null || paths.length == 0)) {
            return defaultValue;
        }
        
        // 构建完整的层级路径列表，按优先级顺序（从最具体到最通用）
        List<String> pathsToTry = buildHierarchicalPaths(prefix, suffix, paths);
        
        // 按优先级顺序尝试获取值
        for (String path : pathsToTry) {
            String value = get(path, null);
            if (StrUtil.isNotBlank(value)) {
                if (log.isDebugEnabled()) {
                    log.debug("找到属性值 - 路径: {}, 值: {}", path, value);
                }
                return value;
            }
        }
        
        return defaultValue;
    }
    
    /**
     * 构建按优先级排序的层级路径列表
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @param paths 中间路径
     * @return 路径列表，按优先级排序（从最具体到最通用）
     */
    private List<String> buildHierarchicalPaths(String prefix, String suffix, String... paths) {
        List<String> result = new ArrayList<>();
        
        if (paths == null || paths.length == 0) {
            // 只有前缀和后缀的情况
            result.add(buildPath(prefix, suffix));
            return result;
        }
        
        // 过滤掉空路径
        List<String> validPaths = new ArrayList<>();
        for (String path : paths) {
            if (StrUtil.isNotBlank(path)) {
                validPaths.add(path);
            }
        }
        
        if (validPaths.isEmpty()) {
            // 如果所有中间路径都是空的，只添加前缀和后缀
            result.add(buildPath(prefix, suffix));
            return result;
        }
        
        // 从最具体到最通用构建路径
        StringBuilder pathBuilder = new StringBuilder();
        if (StrUtil.isNotBlank(prefix)) {
            pathBuilder.append(prefix);
        }
        
        // 先添加最具体的完整路径
        for (String path : validPaths) {
            if (pathBuilder.length() > 0) {
                pathBuilder.append(EventListenConst.DOT);
            }
            pathBuilder.append(path);
        }
        
        if (StrUtil.isNotBlank(suffix)) {
            if (pathBuilder.length() > 0) {
                pathBuilder.append(EventListenConst.DOT);
            }
            pathBuilder.append(suffix);
        }
        result.add(pathBuilder.toString());
        
        // 然后逐步减少路径部分，构建更通用的路径
        for (int i = validPaths.size() - 1; i >= 0; i--) {
            pathBuilder = new StringBuilder();
            if (StrUtil.isNotBlank(prefix)) {
                pathBuilder.append(prefix);
            }
            
            for (int j = 0; j < i; j++) {
                if (pathBuilder.length() > 0) {
                    pathBuilder.append(EventListenConst.DOT);
                }
                pathBuilder.append(validPaths.get(j));
            }
            
            if (StrUtil.isNotBlank(suffix)) {
                if (pathBuilder.length() > 0) {
                    pathBuilder.append(EventListenConst.DOT);
                }
                pathBuilder.append(suffix);
            }
            result.add(pathBuilder.toString());
        }
        
        // 最后添加最通用的前缀+后缀
        if (StrUtil.isNotBlank(prefix) || StrUtil.isNotBlank(suffix)) {
            result.add(buildPath(prefix, suffix));
        }
        
        return result;
    }
    
    /**
     * 构建路径
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 完整路径
     */
    private String buildPath(String prefix, String suffix) {
        if (StrUtil.isBlank(prefix)) {
            return suffix;
        }
        if (StrUtil.isBlank(suffix)) {
            return prefix;
        }
        return prefix + EventListenConst.DOT + suffix;
    }
    
    /**
     * 获取布尔属性，支持层级访问
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @param defaultValue 默认值
     * @param paths 中间路径
     * @return 布尔值
     */
    public boolean getBooleanByPrefixPathSuffix(String prefix, String suffix, boolean defaultValue, String... paths) {
        String value = getPropWithDefaultValue(prefix, suffix, null, paths);
        if (value != null) {
            return BooleanUtil.toBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * 获取指定前缀下的所有子属性名称
     *
     * @param prefix 前缀
     * @return 子属性名称列表
     */
    public List<String> getKeysByPrefix(String prefix) {
        if (StrUtil.isBlank(prefix)) {
            return new ArrayList<>(flatProperties.keySet());
        }
        
        String prefixWithDot = prefix + EventListenConst.DOT;
        return flatProperties.keySet().stream()
                .filter(key -> key.startsWith(prefixWithDot))
                .map(key -> key.substring(prefixWithDot.length()))
                .map(key -> {
                    int dotIndex = key.indexOf(EventListenConst.DOT);
                    return dotIndex >= 0 ? key.substring(0, dotIndex) : key;
                })
                .distinct()
                .collect(Collectors.toList());
    }
} 