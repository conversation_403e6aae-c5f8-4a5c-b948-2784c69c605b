package com.open_care.camunda.util;

import cn.hutool.core.util.StrUtil;
import com.open_care.camunda.enums.TaskListenEventType;

/**
 * 任务和执行监听器工具类，用于获取属性名和配置路径
 *
 * <p>超时配置支持以下方式：</p>
 * <ol>
 *   <li>特定类型超时配置：使用task.listen.timeout.duration.type等特定配置</li>
 *   <li>统一超时配置：使用task.listen.timeout.type和task.listen.timeout.express设置所有超时的统一配置</li>
 *   <li>数字后缀超时配置：使用task.listen.timeout.0.type和task.listen.timeout.0.express等带数字后缀的配置
 *       可以配置多个不同的超时任务，如task.listen.timeout.0、task.listen.timeout.1等</li>
 * </ol>
 * <p>配置优先级：特定类型超时配置 > 数字后缀超时配置 > 统一超时配置</p>
 * <p>举例：</p>
 * <pre>
 * task.listen.timeout.0=true
 * task.listen.timeout.0.type=duration
 * task.listen.timeout.0.express=PT30M
 * task.listen.timeout.1=true
 * task.listen.timeout.1.type=duration
 * task.listen.timeout.1.express=PT1H
 * </pre>
 *
 * <AUTHOR>
 * @date :2025/4/14
 */
public class EventListenConst {

    public static final String EMPTY = TaskConst.EMPTY;

    public static final String DOT = TaskConst.DOT;

    public static final String TASK = TaskConst.TASK;

    public static final String EXECUTION = TaskConst.EXECUTION;


    public static final String LISTEN = TaskConst.LISTEN;

    public static final String ENABLE = TaskConst.ENABLE;
    public static final String SYNC = TaskConst.SYNC;

    public static final String TIMEOUT = TaskConst.TIMEOUT;



}
