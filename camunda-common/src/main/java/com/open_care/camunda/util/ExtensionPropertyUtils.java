package com.open_care.camunda.util;

import camundajar.impl.scala.collection.View;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.open_care.api.common.dto.ExecutionPropertiesDTO;
import com.open_care.api.common.dto.TaskPropertiesDTO;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.VariableScope;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaPropertyImpl;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaPropertiesImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Supplier;


/**
 * Camunda扩展属性工具类
 * 用于从DelegateTask和DelegateExecution中获取扩展属性
 *
 * <p>
 * 该工具类通过前缀和路径获取属性（层级式），使用 {@code getHierarchicalProperty} 方法
 * </p>
 *
 * <p>
 * <b>层级式方法使用示例：</b>
 * <pre>
 * String value = ExtensionPropertyUtils.getHierarchicalProperty(
 *     variableScope,
 *     "task.listen",
 *     null,
 *     ".",
 *     "timeout",
 *     "type"
 * );
 * </pre>
 * </p>
 *
 * <AUTHOR>
 * @date :2025/4/11
 */
@Log4j2
public class ExtensionPropertyUtils {


    public static TaskPropertiesDTO generateProperties(Map<String, Object> extensionProperties) {
        return TaskPropertiesDTO.builder()
                .jobHandler(ObjectUtil.toString(extensionProperties.get(EventHandlerConst.TASK_HANDLER_NAME)))
                .listenHandler(ObjectUtil.toString(extensionProperties.get(EventHandlerConst.TASK_LISTEN_HANDLER_NAME)))
                .build();
    }


    public static String compose(String... paths) {
        return String.join(EventListenConst.DOT, paths);
    }


    /**
     * 从任务中获取所有扩展属性
     *
     * @param delegateTask 任务委托对象
     * @return 扩展属性Map
     */
    public static Map<String, String> getTaskExtensionProperties(DelegateTask delegateTask) {
        if (delegateTask == null || delegateTask.getProcessEngineServices() == null) {
            return MapUtil.empty();
        }

        try {
            BpmnModelInstance modelInstance = getBpmnModelInstance(delegateTask.getProcessDefinitionId(),
                    delegateTask.getProcessEngineServices().getRepositoryService()::getBpmnModelInstance);

            if (modelInstance == null) {
                return MapUtil.empty();
            }

            return modelInstance.getModelElementsByType(UserTask.class).stream()
                    .filter(userTask -> userTask.getId().equals(delegateTask.getTaskDefinitionKey()))
                    .findFirst()
                    .map(ExtensionPropertyUtils::extractCamundaProperties)
                    .orElse(MapUtil.empty());

        } catch (Exception e) {
            logError("获取任务扩展属性失败", e,
                    () -> String.format("taskId=%s, processDefinitionId=%s",
                            delegateTask.getId(), delegateTask.getProcessDefinitionId()));
            return MapUtil.empty();
        }
    }

    /**
     * 从执行中获取所有扩展属性
     *
     * @param execution 执行对象
     * @return 扩展属性Map
     */
    public static Map<String, String> getExecutionExtensionProperties(DelegateExecution execution) {
        if (execution == null || execution.getProcessEngineServices() == null) {
            return Collections.emptyMap();
        }

        String activityId = execution.getCurrentActivityId();
        if (activityId == null) {
            return Collections.emptyMap();
        }

        try {
            BpmnModelInstance modelInstance = getBpmnModelInstance(execution.getProcessDefinitionId(),
                    execution.getProcessEngineServices().getRepositoryService()::getBpmnModelInstance);

            if (modelInstance == null) {
                return Collections.emptyMap();
            }

            FlowElement flowElement = modelInstance.getModelElementById(activityId);
            return flowElement != null ? extractCamundaProperties(flowElement) : Collections.emptyMap();

        } catch (Exception e) {
            logError("获取执行扩展属性失败", e,
                    () -> String.format("executionId=%s, processDefinitionId=%s",
                            execution.getId(), execution.getProcessDefinitionId()));
            return Collections.emptyMap();
        }
    }

    /**
     * 根据变量作用域类型获取扩展属性
     *
     * @param variableScope 变量作用域
     * @return 扩展属性Map
     */
    public static Map<String, String> getExtensionProperties(VariableScope variableScope) {
        if (variableScope instanceof DelegateTask) {
            return getTaskExtensionProperties((DelegateTask) variableScope);
        } else if (variableScope instanceof DelegateExecution) {
            return getExecutionExtensionProperties((DelegateExecution) variableScope);
        }
        return Collections.emptyMap();
    }


    // ----------------------------- 私有辅助方法 -----------------------------

    /**
     * 获取BPMN模型实例
     */
    private static BpmnModelInstance getBpmnModelInstance(String processDefinitionId,
                                                          Function<String, BpmnModelInstance> modelProvider) {
        return processDefinitionId != null ? modelProvider.apply(processDefinitionId) : null;
    }

    /**
     * 从FlowElement提取Camunda扩展属性
     */
    private static Map<String, String> extractCamundaProperties(FlowElement element) {
        Map<String, String> properties = new HashMap<>();
        if (element.getExtensionElements() != null) {
            element.getExtensionElements().getElements().stream()
                    .filter(ext -> ext instanceof CamundaPropertiesImpl)
                    .map(ext -> (CamundaPropertiesImpl) ext)
                    .flatMap(props -> props.getCamundaProperties().stream())
                    .filter(prop -> prop instanceof CamundaPropertyImpl)
                    .map(prop -> (CamundaPropertyImpl) prop)
                    .forEach(prop -> properties.put(prop.getCamundaName(), prop.getCamundaValue()));
        }
        return properties;
    }

    /**
     * 从Process提取Camunda扩展属性
     */
    private static Map<String, String> extractCamundaProperties(org.camunda.bpm.model.bpmn.instance.Process process) {
        Map<String, String> properties = new HashMap<>();
        if (process.getExtensionElements() != null) {
            process.getExtensionElements().getElements().stream()
                    .filter(ext -> ext instanceof CamundaPropertiesImpl)
                    .map(ext -> (CamundaPropertiesImpl) ext)
                    .flatMap(props -> props.getCamundaProperties().stream())
                    .filter(prop -> prop instanceof CamundaPropertyImpl)
                    .map(prop -> (CamundaPropertyImpl) prop)
                    .forEach(prop -> properties.put(prop.getCamundaName(), prop.getCamundaValue()));
        }
        return properties;
    }


    /**
     * 记录错误日志
     */
    private static void logError(String message, Exception e, Supplier<String> detailsProvider) {
        log.error("{}: {}", message, detailsProvider.get(), e);
    }

    /**
     * 将所有属性（变量和扩展属性）转换为PropertyJsonWrapper
     * 变量优先级高于扩展属性（变量会覆盖同名扩展属性）
     * 支持层级嵌套和扁平访问
     *
     * @param delegateTask 任务对象
     * @return PropertyJsonWrapper
     */
    public static PropertyJsonWrapper getAllPropertiesAsJsonWrapper(DelegateTask delegateTask) {
        return getAllPropertiesAsJsonWrapper(delegateTask, EventListenConst.TASK);
    }

    /**
     * 将所有属性（变量和扩展属性）转换为PropertyJsonWrapper
     * 变量优先级高于扩展属性（变量会覆盖同名扩展属性）
     * 支持层级嵌套和扁平访问
     *
     * @param execution 执行对象
     * @return PropertyJsonWrapper
     */
    public static PropertyJsonWrapper getAllPropertiesAsJsonWrapper(DelegateExecution execution) {
        return getAllPropertiesAsJsonWrapper(execution, EventListenConst.EXECUTION);
    }


    /**
     * 将所有属性（变量和扩展属性）转换为PropertyJsonWrapper
     * 变量优先级高于扩展属性（变量会覆盖同名扩展属性）
     * 支持层级嵌套和扁平访问
     *
     * @param task 任务对象
     * @param taskService 任务服务
     * @param repositoryService 仓库服务
     * @return PropertyJsonWrapper
     */
    public static PropertyJsonWrapper getAllPropertiesAsJsonWrapper(Task task, TaskService taskService,  RepositoryService repositoryService) {
        if (task == null) {
            return new PropertyJsonWrapper(new HashMap<>());
        }

        try {
            Map<String, Object> allProps = new HashMap<>();

            // 获取任务扩展属性
            BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(task.getProcessDefinitionId());
            
            if (modelInstance != null) {
                modelInstance.getModelElementsByType(UserTask.class).stream()
                        .filter(userTask -> userTask.getId().equals(task.getTaskDefinitionKey()))
                        .findFirst()
                        .ifPresent(userTask -> {
                            Map<String, String> extensionProps = extractCamundaProperties(userTask);
                            // 扩展属性不覆盖任务变量
                            extensionProps.forEach((key, value) -> {
                                if (!allProps.containsKey(key)) {
                                    allProps.put(key, value);
                                }
                            });
                        });
            }

            
            if (log.isDebugEnabled()) {
                log.debug("转换Task属性为JSONWrapper - TaskId: {}, 属性数量: {}", 
                        task.getId(), allProps.size());
            }
            
            return new PropertyJsonWrapper(allProps);
        } catch (Exception e) {
            log.error("获取Task属性失败: taskId={}, error={}", task.getId(), e.getMessage(), e);
            return new PropertyJsonWrapper(new HashMap<>());
        }
    }

    /**
     * 将指定前缀的属性（变量和扩展属性）转换为PropertyJsonWrapper
     * 变量优先级高于扩展属性（变量会覆盖同名扩展属性）
     * 支持层级嵌套和扁平访问
     *
     * @param variableScope 变量作用域对象（任务或执行）
     * @param prefix        属性前缀，如果为null或空则获取所有属性
     * @return PropertyJsonWrapper
     */
    public static PropertyJsonWrapper getAllPropertiesAsJsonWrapper(VariableScope variableScope, String prefix) {
        Map<String, Object> allProps = new HashMap<>();

        // 筛选带前缀的变量
        if (StrUtil.isNotBlank(prefix)) {
            // 从变量中获取带前缀的属性
            Map<String, Object> variables = variableScope.getVariables();
            variables.forEach((key, value) -> {
                if (key.startsWith(prefix)) {
                    allProps.put(key, value);
                }
            });

            // 从扩展属性中获取带前缀的属性
            if (variableScope instanceof DelegateTask delegateTask) {
                getTaskExtensionPropertiesWithPrefix(delegateTask, prefix).forEach(allProps::put);
            } else if (variableScope instanceof DelegateExecution execution) {
                getExecutionExtensionPropertiesWithPrefix(execution, prefix).forEach(allProps::put);
            }
        } else {
            // 如果没有指定前缀，则获取所有属性
            allProps.putAll(variableScope.getVariables());
            allProps.putAll(getExtensionProperties(variableScope));
        }

        if (log.isDebugEnabled()) {
            log.debug("转换属性为JSONWrapper - 前缀: {}, 属性数量: {}",
                    prefix == null ? "全部" : prefix, allProps.size());
        }

        return new PropertyJsonWrapper(allProps);
    }

    /**
     * 获取指定前缀的任务扩展属性
     *
     * @param delegateTask 任务委托对象
     * @param prefix       属性前缀
     * @return 扩展属性Map
     */
    private static Map<String, Object> getTaskExtensionPropertiesWithPrefix(DelegateTask delegateTask, String prefix) {
        if (delegateTask == null || delegateTask.getProcessEngineServices() == null) {
            return MapUtil.empty();
        }

        try {
            BpmnModelInstance modelInstance = getBpmnModelInstance(delegateTask.getProcessDefinitionId(),
                    delegateTask.getProcessEngineServices().getRepositoryService()::getBpmnModelInstance);

            if (modelInstance == null) {
                return MapUtil.empty();
            }

            return modelInstance.getModelElementsByType(UserTask.class).stream()
                    .filter(userTask -> userTask.getId().equals(delegateTask.getTaskDefinitionKey()))
                    .findFirst()
                    .map(userTask -> extractCamundaPropertiesWithPrefix(userTask, prefix))
                    .orElse(MapUtil.empty());
        } catch (Exception e) {
            logError("获取任务扩展属性失败", e,
                    () -> String.format("taskId=%s, processDefinitionId=%s",
                            delegateTask.getId(), delegateTask.getProcessDefinitionId()));
            return MapUtil.empty();
        }
    }

    /**
     * 获取指定前缀的执行扩展属性
     *
     * @param execution 执行对象
     * @param prefix    属性前缀
     * @return 扩展属性Map
     */
    private static Map<String, Object> getExecutionExtensionPropertiesWithPrefix(DelegateExecution execution, String prefix) {
        if (execution == null || execution.getProcessEngineServices() == null) {
            return Collections.emptyMap();
        }

        String activityId = execution.getCurrentActivityId();
        if (activityId == null) {
            return Collections.emptyMap();
        }

        try {
            BpmnModelInstance modelInstance = getBpmnModelInstance(execution.getProcessDefinitionId(),
                    execution.getProcessEngineServices().getRepositoryService()::getBpmnModelInstance);

            if (modelInstance == null) {
                return Collections.emptyMap();
            }

            FlowElement flowElement = modelInstance.getModelElementById(activityId);
            return flowElement != null ? extractCamundaPropertiesWithPrefix(flowElement, prefix) : Collections.emptyMap();
        } catch (Exception e) {
            logError("获取执行扩展属性失败", e,
                    () -> String.format("executionId=%s, processDefinitionId=%s",
                            execution.getId(), execution.getProcessDefinitionId()));
            return Collections.emptyMap();
        }
    }

    /**
     * 从FlowElement提取指定前缀的Camunda扩展属性
     */
    private static Map<String, Object> extractCamundaPropertiesWithPrefix(FlowElement element, String prefix) {
        Map<String, Object> properties = new HashMap<>();
        if (element.getExtensionElements() != null) {
            element.getExtensionElements().getElements().stream()
                    .filter(ext -> ext instanceof CamundaPropertiesImpl)
                    .map(ext -> (CamundaPropertiesImpl) ext)
                    .flatMap(props -> props.getCamundaProperties().stream())
                    .filter(prop -> prop instanceof CamundaPropertyImpl)
                    .map(prop -> (CamundaPropertyImpl) prop)
                    .filter(prop -> StrUtil.isNotBlank(prop.getCamundaName()) && StrUtil.isNotBlank(prop.getCamundaValue()))
                    .filter(prop -> StrUtil.startWith(prop.getCamundaName(), prefix))
                    .forEach(prop -> properties.put(prop.getCamundaName(), prop.getCamundaValue()));
        }
        return properties;
    }

    /**
     * 处理对象属性添加到JSON中，如果值是对象则将其属性平铺后再添加
     *
     * @param rootJson 根JSON对象
     * @param key      属性键
     * @param value    属性值
     */
    private static void putByPathWithObjectHandling(JSONObject rootJson, String key, Object value) {
        // 如果值为null，直接添加
        if (value == null) {
            rootJson.putByPath(key, null);
            return;
        }

        // 如果是基本类型或字符串，直接添加
        if (value instanceof Number || value instanceof Boolean || value instanceof String || value instanceof Character || value.getClass().isPrimitive()) {
            rootJson.putByPath(key, value);
            return;
        }

        // 如果是Map类型，处理每个条目
        if (value instanceof Map<?, ?> mapValue) {
            if (mapValue.isEmpty()) {
                rootJson.putByPath(key, value);
            } else {
                for (Map.Entry<?, ?> entry : mapValue.entrySet()) {
                    String entryKey = entry.getKey().toString();
                    String fullPath = key + "." + entryKey;
                    putByPathWithObjectHandling(rootJson, fullPath, entry.getValue());
                }
            }
            return;
        }


        // 尝试使用JSONObject处理自定义对象类型
        try {
            JSONObject jsonObj = new JSONObject(value);
            if (jsonObj.isEmpty()) {
                rootJson.putByPath(key, value);
            } else {
                for (String fieldName : jsonObj.keySet()) {
                    String fullPath = key + EventListenConst.DOT + fieldName;
                    putByPathWithObjectHandling(rootJson, fullPath, jsonObj.get(fieldName));
                }
            }
        } catch (Exception e) {
            // 如果无法转换为JSONObject，则直接添加原始对象
            rootJson.putByPath(key, value);
            if (log.isDebugEnabled()) {
                log.debug("无法平铺对象属性，直接添加 - 键: {}, 值类型: {}", key, value.getClass().getName());
            }
        }
    }

    /**
     * 获取流程定义的扩展属性
     *
     * @param processDefinition 流程定义对象
     * @param repositoryService 仓库服务
     * @return 扩展属性Map
     */
    public static Map<String, Object> getExtensionProperties(org.camunda.bpm.engine.repository.ProcessDefinition processDefinition, RepositoryService repositoryService) {
        if (processDefinition == null || repositoryService == null) {
            return Collections.emptyMap();
        }

        try {
            BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(processDefinition.getId());
            if (modelInstance == null) {
                return Collections.emptyMap();
            }

            // 获取流程定义对象
            org.camunda.bpm.model.bpmn.instance.Process process = modelInstance.getModelElementsByType(org.camunda.bpm.model.bpmn.instance.Process.class)
                    .stream()
                    .filter(p -> p.getId().equals(processDefinition.getKey()))
                    .findFirst()
                    .orElse(null);

            if (process == null) {
                return Collections.emptyMap();
            }

            // 提取流程定义的扩展属性
            Map<String, Object> properties = new HashMap<>();
            Map<String, String> rawProperties = extractCamundaProperties(process);
            rawProperties.forEach((key, value) -> properties.put(key, value));
            
            return properties;
        } catch (Exception e) {
            log.error("获取流程定义扩展属性失败: processDefinitionId={}, error={}", 
                      processDefinition.getId(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取流程定义的所有属性
     *
     * @param processDefinition 流程定义对象
     * @param repositoryService 仓库服务
     * @return 属性Map
     */
    public static Map<String, Object> getAllPropertiesAsMap(org.camunda.bpm.engine.repository.ProcessDefinition processDefinition, RepositoryService repositoryService) {
        if (processDefinition == null || repositoryService == null) {
            return Collections.emptyMap();
        }

        try {
            // 基本属性
            Map<String, Object> allProps = new HashMap<>();
            allProps.put("id", processDefinition.getId());
            allProps.put("key", processDefinition.getKey());
            allProps.put("name", processDefinition.getName());
            allProps.put("version", processDefinition.getVersion());
            allProps.put("deploymentId", processDefinition.getDeploymentId());
            allProps.put("resourceName", processDefinition.getResourceName());
            allProps.put("diagramResourceName", processDefinition.getDiagramResourceName());
            allProps.put("category", processDefinition.getCategory());
            allProps.put("description", processDefinition.getDescription());
            allProps.put("tenantId", processDefinition.getTenantId());
            allProps.put("versionTag", processDefinition.getVersionTag());
            allProps.put("historyTimeToLive", processDefinition.getHistoryTimeToLive());
            
            // 扩展属性
            Map<String, Object> extensionProps = getExtensionProperties(processDefinition, repositoryService);
            allProps.putAll(extensionProps);
            
            return allProps;
        } catch (Exception e) {
            log.error("获取流程定义所有属性失败: processDefinitionId={}, error={}", 
                      processDefinition.getId(), e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据流程定义ID获取流程定义的扩展属性
     *
     * @param processDefinitionId 流程定义ID
     * @param repositoryService 仓库服务
     * @return 扩展属性Map
     */
    public static Map<String, Object> getExtensionProperties(String processDefinitionId, RepositoryService repositoryService) {
        if (StrUtil.isBlank(processDefinitionId) || repositoryService == null) {
            return Collections.emptyMap();
        }

        try {
            // 获取流程定义对象
            org.camunda.bpm.engine.repository.ProcessDefinition processDefinition = 
                    repositoryService.getProcessDefinition(processDefinitionId);
            
            if (processDefinition == null) {
                return Collections.emptyMap();
            }
            
            // 使用已有方法获取扩展属性
            return getExtensionProperties(processDefinition, repositoryService);
        } catch (Exception e) {
            log.error("根据ID获取流程定义扩展属性失败: processDefinitionId={}, error={}", 
                      processDefinitionId, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据流程定义ID获取流程定义的所有属性
     *
     * @param processDefinitionId 流程定义ID
     * @param repositoryService 仓库服务
     * @return 属性Map
     */
    public static Map<String, Object> getAllPropertiesAsMap(String processDefinitionId, RepositoryService repositoryService) {
        if (StrUtil.isBlank(processDefinitionId) || repositoryService == null) {
            return Collections.emptyMap();
        }

        try {
            // 获取流程定义对象
            org.camunda.bpm.engine.repository.ProcessDefinition processDefinition = 
                    repositoryService.getProcessDefinition(processDefinitionId);
            
            if (processDefinition == null) {
                return Collections.emptyMap();
            }
            
            // 使用已有方法获取所有属性
            return getAllPropertiesAsMap(processDefinition, repositoryService);
        } catch (Exception e) {
            log.error("根据ID获取流程定义所有属性失败: processDefinitionId={}, error={}", 
                      processDefinitionId, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

}