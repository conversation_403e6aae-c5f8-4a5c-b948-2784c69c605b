package com.open_care.camunda.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.Date;
import java.util.Map;

/**
 * 流程组件实体
 * 用于存储各类流程组件，可以是节点或完整流程定义
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "process_component")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ProcessComponent {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "uuid")
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    private String id;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    private String createdBy;
    
    /**
     * 创建时间
     */
    @Column
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @CreatedDate
    private Date createTime;
    
    /**
     * 更新人
     */
    @Column
    @LastModifiedBy
    private String updatedBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @LastModifiedDate
    @Column
    private Date updateTime;

    /**
     * 组件名称
     */
    @Column
    private String name;

    /**
     * 组件描述
     */
    @Column
    private String description;

    /**
     * 组件类型
     */
    @Enumerated(EnumType.STRING)
    @Column
    private ProcessComponentTypeEnum componentType;

    /**
     * 组件内容 (XML或JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String content;

    /**
     * 组件唯一标识
     */
    @Column
    private String componentKey;

    /**
     * 组件BPMN类型
     */
    @Column
    private String type;

    /**
     * 组件分组
     */
    @Column(name = "component_group")
    private String group;


    @Column(name = "values")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String,Object> values;
    /**
     * 组件属性值
     */
    @Column(name = "extension_properties")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> extensionProperties;
} 