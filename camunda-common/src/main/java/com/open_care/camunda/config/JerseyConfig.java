package com.open_care.camunda.config;

import com.open_care.camunda.provider.CustomObjectMapperProvider;
import jakarta.ws.rs.ApplicationPath;
import jakarta.ws.rs.Priorities;
import org.camunda.bpm.spring.boot.starter.rest.CamundaJerseyResourceConfig;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date :2025/5/8
 */

/**
 * Jersey配置类
 * 用于注册自定义的ObjectMapperProvider到JAX-RS应用程序
 *
 * <AUTHOR>
 */
@Component
@ApplicationPath("/engine-rest")
public class JerseyConfig extends CamundaJerseyResourceConfig {

    @Override
    protected void registerAdditionalResources() {
        register(CustomObjectMapperProvider.class, -100);
    }

}