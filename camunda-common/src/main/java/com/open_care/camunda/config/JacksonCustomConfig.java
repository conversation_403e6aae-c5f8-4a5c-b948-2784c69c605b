package com.open_care.camunda.config;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.open_care.camunda.jackson.module.CustomerDateModule;
import com.open_care.camunda.jackson.module.SpinJsonNodeModule;
import com.open_care.camunda.jackson.serializer.CustomDateDeserializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 * @date :2025/5/8
 */
@Configuration
public class JacksonCustomConfig {


    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizer() {
        return builder -> {

            builder.modulesToInstall(modules -> {
                modules.add(new JavaTimeModule());
//                modules.add(new SpinJsonNodeModule());
                modules.add(new CustomerDateModule());
            });

        };
    }



}
