package com.open_care.camunda.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * Camunda配置类
 *
 * <AUTHOR>
 * @date :2023/10/08
 */
@ComponentScan(basePackages = {"com.open_care.camunda","com.open_care.auth","org.camunda"})
@EnableDiscoveryClient
@EntityScan(basePackages = {"org.camunda"})
@EnableJpaRepositories("org.camunda")
@EnableAsync
@Configuration
public class CamundaConfig {



}
