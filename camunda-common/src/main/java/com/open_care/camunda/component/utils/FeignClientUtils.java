package com.open_care.camunda.component.utils;

import cn.hutool.cache.Cache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.slf4j.Slf4jLogger;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Feign客户端工具类
 * 用于手动构建Feign客户端
 *
 * <AUTHOR>
 * @date :2025/4/11
 */
@Component
@Log4j2
public class FeignClientUtils {

    @Autowired(required = false)
    private ObjectMapper objectMapper;

    @Autowired(required = false)
    private Encoder encoder;

    @Autowired(required = false)
    private Decoder decoder;

    @Autowired(required = false)
    private List<RequestInterceptor> requestInterceptors;

    @Autowired
    private LoadBalancerClient loadBalancerClient;

    // 创建缓存，默认30分钟过期
    private final Cache<String, Object> clientCache = CacheUtil.newLFUCache(128);

    /**
     * 创建Feign客户端
     *
     * @param serviceName 服务名称
     * @param apiType     API接口类型
     * @param <T>         API接口类型
     * @return Feign客户端实例
     */
    public <T> T createClient(String serviceName, Class<T> apiType) {
        String cacheKey = generateCacheKey(serviceName, apiType);

        Object cache = clientCache.get(cacheKey);
        if (ObjectUtil.isNotNull(cache) && apiType.isAssignableFrom(cache.getClass())) {
            return (T) cache;
        }

        log.debug("手动创建Feign客户端, 服务URL: {}, 接口类型: {}", serviceName, apiType.getName());

        // 构建Feign客户端
        Feign.Builder builder = Feign.builder()
                .encoder(getEncoder())
                .decoder(getDecoder())
                .contract(new SpringMvcContract())
                .logger(new Slf4jLogger(apiType))
                .logLevel(Logger.Level.FULL);

        // 添加所有拦截器
        for (RequestInterceptor interceptor : CollUtil.emptyIfNull(requestInterceptors)) {
            builder.requestInterceptor(interceptor);
            log.debug("添加请求拦截器: {}", interceptor.getClass().getName());
        }

        // 创建客户端并放入缓存
        ServiceInstance serviceInstance = loadBalancerClient.choose(serviceName);
        if (ObjectUtil.isNull(serviceInstance)) {
            log.error("服务 {} 未找到实例", serviceName);
            return null;
        }
        T client = builder.target(apiType, serviceInstance.getUri().toString());

        clientCache.put(cacheKey, client);

        return client;
    }

    /**
     * 生成缓存键
     */
    private <T> String generateCacheKey(String serviceUrl, Class<T> apiType) {
        return serviceUrl + ":" + apiType.getName();
    }

    /**
     * 获取编码器
     */
    private Encoder getEncoder() {
        if (encoder != null) {
            return encoder;
        }

        log.debug("没有注入Encoder，使用默认的SpringEncoder");
        // 如果没有注入，使用SpringEncoder
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(
                objectMapper != null ? objectMapper : new ObjectMapper());
        ObjectFactory<HttpMessageConverters> objectFactory =
                () -> new HttpMessageConverters(converter);
        return new SpringEncoder(objectFactory);
    }

    /**
     * 获取解码器
     */
    private Decoder getDecoder() {
        if (decoder != null) {
            return decoder;
        }

        log.debug("没有注入Decoder，使用默认的SpringDecoder");
        // 如果没有注入，使用SpringDecoder
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(
                objectMapper != null ? objectMapper : new ObjectMapper());
        ObjectFactory<HttpMessageConverters> objectFactory =
                () -> new HttpMessageConverters(converter);
        return new SpringDecoder(objectFactory);
    }
}