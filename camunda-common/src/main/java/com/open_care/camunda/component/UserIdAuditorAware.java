package com.open_care.camunda.component;

import com.open_care.auth.core.context.UserInfoContext;
import com.open_care.auth.core.dto.UserBaseDTO;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date :2025/5/19
 */
@Component
public class UserIdAuditorAware implements AuditorAware<String> {
    /**
     * Returns the current auditor of the application.
     *
     * @return the current auditor.
     */
    @Override
    public Optional<String> getCurrentAuditor() {
        UserBaseDTO userInfo = UserInfoContext.getUserBase();
        if (userInfo != null && userInfo.getUserId() != null) {
            return Optional.of(userInfo.getUserId());
        }
        return Optional.empty();
    }
}
