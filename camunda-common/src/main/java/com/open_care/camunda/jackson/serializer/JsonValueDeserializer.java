package com.open_care.camunda.jackson.serializer;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.camunda.spin.impl.json.jackson.JacksonJsonNode;
import org.camunda.spin.plugin.variable.value.impl.JsonValueImpl;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date :2025/5/16
 */
public class JsonValueDeserializer extends JsonDeserializer<JsonValueImpl> {


    @Override
    public JsonValueImpl deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        // 读取 JSON 字符串
        String jsonStr = p.getText();
        if (jsonStr == null || jsonStr.isEmpty()) {
            return null;
        }

        try {
            // 使用 Camunda Spin 的 JSON 方法创建 SpinJsonNode
            return new JsonValueImpl(JacksonJsonNode.JSON(jsonStr));
        } catch (Exception e) {
            throw new IOException("无法将 JSON 字符串转换为 SpinJsonNode: " + jsonStr, e);
        }
    }
}
