package com.open_care.camunda.jackson.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.camunda.spin.impl.json.jackson.JacksonJsonNode;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.camunda.spin.plugin.variable.value.impl.JsonValueImpl;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date :2025/5/16
 */
public class JsonValueSerializer extends JsonSerializer<JsonValue> {
    /**
     * Method that can be called to ask implementation to serialize
     * values of type this serializer handles.
     *
     * @param value       Value to serialize; can <b>not</b> be null.
     * @param gen         Generator used to output resulting Json content
     * @param serializers Provider that can be used to get serializers for
     *                    serializing Objects value contains, if any.
     */
    @Override
    public void serialize(JsonValue value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        // SpinJsonNode 的 toString 方法返回格式化的 JSON 字符串
        // 这里直接写入原始 JSON 内容，而不是作为字符串值
        gen.writeRawValue(value.getValue().stringValue());
    }
}
