package com.open_care.camunda.jackson.serializer;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.util.StdDateFormat;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.TimeZone;

/**
 * 自定义Date反序列化器
 * 支持多种常见日期格式的解析
 * 支持处理@JsonFormat注解指定的日期格式
 *
 * <AUTHOR>
 * @date :2025/5/8
 */
public class CustomDateDeserializer extends StdDeserializer<Date> implements ContextualDeserializer {

    public final static List<DateFormat> DATE_FORMATS = ListUtil.of(
            new StdDateFormat().withLocale(Locale.CHINA),
            new SimpleDateFormat(DatePattern.NORM_DATETIME_MS_PATTERN), // yyyy-MM-dd HH:mm:ss.SSS

            // 添加常见的日期格式
            new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN),   // yyyy-MM-dd HH:mm:ss
            new SimpleDateFormat(DatePattern.NORM_DATE_PATTERN),      // yyyy-MM-dd
            new SimpleDateFormat("yyyy/MM/dd"),                // yyyy/MM/dd
            new SimpleDateFormat(DatePattern.PURE_DATE_PATTERN),      // yyyyMMdd

            // 可以根据需要添加更多格式
            new SimpleDateFormat(DatePattern.CHINESE_DATE_PATTERN),
            new SimpleDateFormat(DatePattern.CHINESE_DATE_TIME_PATTERN)
    );


    private final List<DateFormat> dateFormats = new ArrayList<>();
    // 字段上的JsonFormat注解定义的日期格式
    private String pattern;
    private TimeZone timeZone;

    public CustomDateDeserializer() {
        super(Date.class);
        // 初始化公共的日期格式列表
        initDateFormats();
    }

    /**
     * 带有特定格式的构造函数
     *
     * @param pattern  日期格式
     * @param timeZone 时区
     */
    public CustomDateDeserializer(String pattern, TimeZone timeZone) {
        super(Date.class);
        this.pattern = pattern;
        this.timeZone = timeZone;
        initDateFormats();
    }

    /**
     * 初始化支持的日期格式列表
     */
    private void initDateFormats() {
        // 如果有注解指定的格式，将其添加为首选格式
        if (pattern != null && !pattern.isEmpty()) {
            SimpleDateFormat annotationFormat = new SimpleDateFormat(pattern);
            if (timeZone != null) {
                annotationFormat.setTimeZone(timeZone);
            }
            dateFormats.add(annotationFormat);
        }

        dateFormats.addAll(DATE_FORMATS);

    }

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext context) throws IOException {
        String dateStr = jsonParser.getText();
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        Optional<Date> date = tryDeserializeDate(dateStr, dateFormats);
        if (date.isEmpty()) {
            // 如果所有格式都解析失败，则抛出异常
            throw new IOException("无法解析日期: " + dateStr +
                    (pattern != null ? "，指定格式: " + pattern : "") +
                    "，支持的格式包括：yyyy-MM-dd HH:mm:ss.SSS, yyyy-MM-dd HH:mm:ss, yyyy-MM-dd, yyyy/MM/dd, yyyyMMdd 等");
        }


        return date.get();

    }

    public static Optional<Date> tryDeserializeDate(String dateStr, List<DateFormat> dateFormats) {
        Date result = null;
        // 尝试使用所有支持的格式解析日期
        for (DateFormat format : dateFormats) {
            try {
                result = format.parse(dateStr);
            } catch (ParseException ignored) {
                // 忽略解析异常，尝试下一个格式
            }
        }
        return Optional.ofNullable(result);
    }

    /**
     * 从属性上下文创建一个带有特定注解格式的反序列化器
     *
     * @param ctxt     上下文
     * @param property 包含注解信息的Bean属性
     * @return 上下文特定的反序列化器
     */
    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property) {
        if (property != null) {
            // 使用StdDeserializer提供的findFormatOverrides方法获取JsonFormat注解信息
            JsonFormat.Value formatOverrides = findFormatOverrides(ctxt, property, handledType());
            if (formatOverrides != null) {
                // 获取格式化模式
                String pattern = formatOverrides.getPattern();

                // 获取时区
                TimeZone timeZone = formatOverrides.getTimeZone();

                // 如果注解指定了格式，创建一个新的带有该格式的反序列化器
                if (pattern != null && !pattern.isEmpty()) {
                    return new CustomDateDeserializer(pattern, timeZone);
                }
            }
        }
        // 如果没有注解或注解没有指定格式，返回默认的反序列化器
        return this;
    }
}
