package com.open_care.camunda.jackson.module;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.open_care.camunda.jackson.serializer.JacksonSpinJsonNodeDeserializer;
import com.open_care.camunda.jackson.serializer.JacksonSpinJsonNodeSerializer;
import com.open_care.camunda.jackson.serializer.JsonValueDeserializer;
import com.open_care.camunda.jackson.serializer.JsonValueSerializer;
import org.camunda.spin.impl.json.jackson.JacksonJsonNode;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.camunda.spin.plugin.variable.value.impl.JsonValueImpl;

import java.util.Date;

/**
 * Jackson模块，用于注册SpinJsonNode的序列化和反序列化器
 * 使用Camunda官方提供的JacksonJsonDataFormatMapper来处理SpinJsonNode
 *
 * <AUTHOR>
 * @date :2025/5/8
 */
public class SpinJsonNodeModule extends SimpleModule {


    public SpinJsonNodeModule() {


        // 注册SpinJsonNode序列化器和反序列化器
        addSerializer(JacksonJsonNode.class, new JacksonSpinJsonNodeSerializer());
        addDeserializer(JacksonJsonNode.class, new JacksonSpinJsonNodeDeserializer());
        addSerializer(JsonValue.class, new JsonValueSerializer());
        addDeserializer(JsonValueImpl.class, new JsonValueDeserializer());

    }
}
