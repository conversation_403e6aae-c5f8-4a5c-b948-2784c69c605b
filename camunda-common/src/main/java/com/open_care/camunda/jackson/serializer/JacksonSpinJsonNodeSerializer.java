package com.open_care.camunda.jackson.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.camunda.spin.impl.json.jackson.JacksonJsonNode;
import org.camunda.spin.json.SpinJsonNode;

import java.io.IOException;

/**
 * Camunda SpinJsonNode 序列化器
 * 将 SpinJsonNode 对象序列化为 JSON 字符串
 * 
 * <AUTHOR>
 * @date :2025/5/8
 */
public class JacksonSpinJsonNodeSerializer extends JsonSerializer<JacksonJsonNode> {

    @Override
    public void serialize(JacksonJsonNode value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        
        // SpinJsonNode 的 toString 方法返回格式化的 JSON 字符串
        // 这里直接写入原始 JSON 内容，而不是作为字符串值
        gen.writeRawValue(value.toString());
    }
}
