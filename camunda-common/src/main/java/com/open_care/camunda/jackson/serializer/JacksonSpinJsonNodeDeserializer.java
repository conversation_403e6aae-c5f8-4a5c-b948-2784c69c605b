package com.open_care.camunda.jackson.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.camunda.spin.impl.json.jackson.JacksonJsonNode;
import org.camunda.spin.json.SpinJsonNode;

import java.io.IOException;

/**
 * Camunda SpinJsonNode 反序列化器
 * 将 JSON 字符串反序列化为 SpinJsonNode 对象
 *
 * <AUTHOR>
 * @date :2025/5/8
 */
public class JacksonSpinJsonNodeDeserializer extends JsonDeserializer<JacksonJsonNode> {

    @Override
    public JacksonJsonNode deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        // 读取 JSON 字符串
        String jsonStr = p.getText();
        if (jsonStr == null || jsonStr.isEmpty()) {
            return null;
        }

        try {
            // 使用 Camunda Spin 的 JSON 方法创建 SpinJsonNode
            return (JacksonJsonNode) JacksonJsonNode.JSON(jsonStr);
        } catch (Exception e) {
            throw new IOException("无法将 JSON 字符串转换为 SpinJsonNode: " + jsonStr, e);
        }
    }
}
