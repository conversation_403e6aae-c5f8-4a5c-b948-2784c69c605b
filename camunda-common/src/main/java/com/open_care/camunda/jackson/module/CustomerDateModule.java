package com.open_care.camunda.jackson.module;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.open_care.camunda.jackson.serializer.CustomDateDeserializer;

import java.util.Date;

/**
 * Jackson日期模块
 * 用于注册日期的序列化和反序列化器
 * 支持处理@JsonFormat注解指定的日期格式
 *
 * <AUTHOR>
 * @date :2025/5/14
 */
public class CustomerDateModule extends SimpleModule {

    public CustomerDateModule() {
        // 注册序列化器和反序列化器
        addDeserializer(Date.class, new CustomDateDeserializer());
    }
}
