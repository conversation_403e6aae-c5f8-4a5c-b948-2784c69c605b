package com.open_care.camunda.command;

import com.open_care.camunda.enums.TaskTimeoutType;
import com.open_care.camunda.job.handler.TimerTaskConfig;
import com.open_care.camunda.enums.TimerType;
import com.open_care.camunda.job.handler.TimerTaskHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.impl.calendar.BusinessCalendar;
import org.camunda.bpm.engine.impl.calendar.CycleBusinessCalendar;
import org.camunda.bpm.engine.impl.calendar.DueDateBusinessCalendar;
import org.camunda.bpm.engine.impl.calendar.DurationBusinessCalendar;
import org.camunda.bpm.engine.impl.interceptor.Command;
import org.camunda.bpm.engine.impl.interceptor.CommandContext;
import org.camunda.bpm.engine.impl.persistence.entity.TimerEntity;

/**
 * 创建定时任务命令
 * 用于编程方式创建Camunda定时任务
 *
 * <AUTHOR>
 * @date :2025/4/14
 */
@AllArgsConstructor
@Data
@Log4j2
public class CreateTimerJobCommand implements Command<TimerEntity> {

    private final String timerName;
    /**
     * 流程实例ID
     */
    private final String processInstanceId;

    /**
     * 任务ID
     */
    private final String taskId;

    /**
     * 定时器类型：
     * date - 固定日期 (ISO 8601格式，如: 2025-04-14T12:00:00Z)
     * duration - 持续时间 (ISO 8601格式，如: PT5M表示5分钟)
     * cycle - 循环周期 (ISO 8601格式，如: R3/PT10H表示每10小时执行一次，共执行3次)
     */
    private final TimerType timerType;

    /**
     * 表达式，根据type的不同而不同:
     * date类型 - ISO 8601日期时间格式 (如: 2025-04-14T12:00:00Z)
     * duration类型 - ISO 8601持续时间格式 (如: PT5M表示5分钟, P1D表示1天)
     * cycle类型 - ISO 8601重复间隔格式 (如: R3/PT10H表示每10小时执行一次，共执行3次)
     */
    private final String express;

    private final TaskTimeoutType timeoutType;

    private final boolean isSync;

    public static final BusinessCalendar DATE = new DueDateBusinessCalendar();
    public static final BusinessCalendar DURATION = new DurationBusinessCalendar();
    public static final BusinessCalendar CYCLE = new CycleBusinessCalendar();

    @Override
    public TimerEntity execute(CommandContext commandContext) {
        // 创建 Timer Job
        TimerEntity timer = new TimerEntity();
        switch (timerType) {
            case DATE -> {
                // Date类型 - 设置固定执行时间
                timer.setDuedate(DATE.resolveDuedate(express));
            }
            case DURATION -> {
                // Duration类型 - 设置相对当前时间的延迟
                timer.setDuedate(DURATION.resolveDuedate(express));
            }
            case CYCLE -> {
                timer.setDuedate(CYCLE.resolveDuedate(express));
                // Cycle类型 - 设置循环执行
                timer.setRepeat(express);
            }
            default -> {
                log.error("不支持的定时器类型: " + timerType + "，支持的类型有: date, duration, cycle");
            }
        }


        // 设置流程实例ID
        timer.setProcessInstanceId(processInstanceId);

        // 设置定时器处理器类型
        timer.setJobHandlerType(TimerTaskHandler.TYPE);

        TimerTaskConfig config = TimerTaskConfig.builder().timerName(timerName).taskId(taskId).type(timerType).processInstanceId(processInstanceId).timeoutType(timeoutType).expression(express).isSync(isSync).build();

        timer.setJobHandlerConfiguration(config);

        // 持久化 Job
        commandContext.getJobManager().schedule(timer);
        return timer;
    }
}
