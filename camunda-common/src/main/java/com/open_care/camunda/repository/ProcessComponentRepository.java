package com.open_care.camunda.repository;

import com.open_care.camunda.entity.ProcessComponent;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 流程组件仓库接口
 *
 * <AUTHOR>
 */
@Repository
public interface ProcessComponentRepository extends JpaRepository<ProcessComponent, String> {
    
    /**
     * 根据组件类型查找组件列表
     *
     * @param componentType 组件类型
     * @return 组件列表
     */
    List<ProcessComponent> findByComponentType(ProcessComponentTypeEnum componentType);
    
    /**
     * 根据组件名称模糊查询
     *
     * @param name 组件名称（模糊匹配）
     * @return 组件列表
     */
    List<ProcessComponent> findByNameContaining(String name);
    
    /**
     * 根据组件类型和名称模糊查询
     *
     * @param componentType 组件类型
     * @param name 组件名称（模糊匹配）
     * @return 组件列表
     */
    List<ProcessComponent> findByComponentTypeAndNameContaining(ProcessComponentTypeEnum componentType, String name);
} 