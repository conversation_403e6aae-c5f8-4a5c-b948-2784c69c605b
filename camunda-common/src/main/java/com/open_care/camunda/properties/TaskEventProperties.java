package com.open_care.camunda.properties;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 任务事件属性配置
 * 
 * <AUTHOR>
 * @date :2025/4/11
 */
@Component
@ConfigurationProperties(prefix = "open-care.camunda.task-event.listener")
@Data
public class TaskEventProperties {
    
    /**
     * 缓存一小时 (3600000毫秒)
     */
    private static final Cache<String, String> TENANT_ID_TO_SERVICE_CACHE = CacheUtil.newLRUCache(16, 3600000);
    
    /**
     * 默认的事件监听器服务名称
     */
    private String defaultListener;
    
    /**
     * 服务名称到租户ID的映射
     */
    private Map<String, String> mappings;
    
    /**
     * 根据租户ID获取对应的服务名称
     *
     * @param tenantId 租户ID
     * @return 服务名称
     */
    public String getServiceName(String tenantId) {
        if (StrUtil.isBlank(tenantId)) {
            return defaultListener;
        }
        
        // 先从缓存中查找
        String serviceName = TENANT_ID_TO_SERVICE_CACHE.get(tenantId);
        if (StrUtil.isNotBlank(serviceName)) {
            return serviceName;
        }
        
        // 从配置中查找
        if (MapUtil.isNotEmpty(mappings)) {
            for (Map.Entry<String, String> entry : mappings.entrySet()) {
                if (tenantId.equals(entry.getValue())) {
                    // 缓存结果
                    TENANT_ID_TO_SERVICE_CACHE.put(tenantId, entry.getKey());
                    return entry.getKey();
                }
            }
        }
        
        // 如果找不到对应的服务，使用默认监听器
        TENANT_ID_TO_SERVICE_CACHE.put(tenantId, defaultListener);
        return defaultListener;
    }
} 