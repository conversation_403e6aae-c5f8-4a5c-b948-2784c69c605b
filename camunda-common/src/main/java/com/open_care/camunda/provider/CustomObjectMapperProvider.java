package com.open_care.camunda.provider;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Priority;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.ext.ContextResolver;
import jakarta.ws.rs.ext.Provider;
import org.camunda.bpm.engine.rest.hal.Hal;
import org.camunda.bpm.engine.rest.mapper.JacksonConfigurator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;


/**
 * 自定义 ObjectMapper 提供者
 * 用于向 Camunda REST API 提供定制化的 ObjectMapper
 * 
 * <AUTHOR>
 */
@Provider
@Produces({MediaType.APPLICATION_JSON, Hal.APPLICATION_HAL_JSON})
@Component
@Primary
@Priority(-100) // 数值越小，优先级越高，默认是 0
public class CustomObjectMapperProvider extends JacksonConfigurator {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public ObjectMapper getContext(Class<?> type) {
        return objectMapper;
    }
}
