package com.open_care.camunda.mapper;

import com.open_care.api.common.dto.TaskEventDTO;
import com.open_care.api.common.dto.TaskPropertiesDTO;
import com.open_care.api.common.enums.TaskEventType;
import com.open_care.api.common.enums.TaskStateEnum;
import com.open_care.camunda.event.Event;
import com.open_care.camunda.util.EventHandlerConst;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.mapstruct.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 使用MapStruct将Task对象转换为TaskDTO
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskMapper {


    @Mapping(source = "task.id", target = "taskId")
    @Mapping(source = "task.name", target = "name")
    @Mapping(source = "task.name", target = "taskName")
    @Mapping(source = "task.processInstanceId", target = "processInstanceId")
    @Mapping(source = "task.processDefinitionId", target = "processDefinitionId")
    @Mapping(source = "task.taskDefinitionKey", target = "taskDefinitionKey")
    @Mapping(source = "task.createTime", target = "createTime")
    @Mapping(source = "task.priority", target = "priority")
    @Mapping(source = "task.owner", target = "owner")
    @Mapping(source = "task.assignee", target = "assignee")
    @Mapping(source = "task.description", target = "description")
    @Mapping(source = "task.tenantId", target = "tenantId")
    @Mapping(target = "taskStatus", expression = "java(eventType.getEventName())")
    @Mapping(target = "eventType", source = "eventType")
    @Mapping(target = "properties", ignore = true)
    public TaskEventDTO toTaskEventDTO(Task task, TaskEventType eventType, TaskConversionContext context);

    @Mapping(source = "task.id", target = "taskId")
    @Mapping(source = "task.name", target = "name")
    @Mapping(source = "task.name", target = "taskName")
    @Mapping(source = "task.processInstanceId", target = "processInstanceId")
    @Mapping(source = "task.processDefinitionId", target = "processDefinitionId")
    @Mapping(source = "task.taskDefinitionKey", target = "taskDefinitionKey")
    @Mapping(source = "task.createTime", target = "createTime")
    @Mapping(source = "task.priority", target = "priority")
    @Mapping(source = "task.owner", target = "owner")
    @Mapping(source = "task.assignee", target = "assignee")
    @Mapping(source = "task.description", target = "description")
    @Mapping(source = "task.tenantId", target = "tenantId")
    @Mapping(target = "taskStatus", expression = "java(eventType.getEventName())")
    @Mapping(target = "eventType", source = "eventType")
    @Mapping(target = "properties", ignore = true)
    public TaskEventDTO toTaskEventDTO(DelegateTask task, TaskEventType eventType, TaskConversionContext context);

    /**
     * 将Event<DelegateTask>转换为TaskDTO，指定事件类型和上下文
     *
     * @param event     任务事件对象
     * @param eventType 事件类型
     * @param context   任务上下文
     * @return 任务DTO
     */
    default TaskEventDTO toTaskEventDTO(Event<DelegateTask> event, TaskEventType eventType, TaskConversionContext context) {
        DelegateTask task = event.getOriginal();

        // 先使用基本的toTaskDTO方法
        TaskEventDTO dto = toTaskEventDTO(task, eventType, context);

        // 设置属性信息
        setPropertiesFromEvent(event, dto);

        return dto;
    }

    /**
     * 从Event对象设置属性到TaskDTO
     *
     * @param event 任务事件对象
     * @param dto   目标DTO
     */
    @AfterMapping
    default void setPropertiesFromEvent(Event<DelegateTask> event, @MappingTarget TaskEventDTO dto) {
        TaskPropertiesDTO taskPropertiesDTO = new TaskPropertiesDTO();
        
        // 从Event中获取jobHandler
        String jobHandler = event.getVariableOrExtensionProperty(EventHandlerConst.TASK_HANDLER_NAME, null);
        taskPropertiesDTO.setJobHandler(jobHandler);

        dto.setProperties(taskPropertiesDTO);
    }

    /**
     * 更新普通Task转换的候选人信息
     *
     * @param task    任务对象
     * @param context 任务转换上下文
     * @param taskDTO 需要更新的任务DTO
     */
    @AfterMapping
    default void setCandidateUsers(Task task, TaskConversionContext context, @MappingTarget TaskEventDTO taskDTO) {
        if (context != null) {
            List<String> candidates = context.getCandidateUsers(task.getId());
            if (candidates != null && !candidates.isEmpty()) {
                taskDTO.setCandidateUsers(candidates);
            }

            // 设置任务变量
            Map<String, Object> variables = context.getTaskVariables(task.getId());
            if (variables != null && !variables.isEmpty()) {
                taskDTO.setVariables(variables);
            }
        }
    }

    /**
     * 更新DelegateTask的候选人信息
     *
     * @param delegateTask 任务对象
     * @param taskDTO      需要更新的任务DTO
     */
    @AfterMapping
    default void setCandidateUsers(DelegateTask delegateTask, @MappingTarget TaskEventDTO taskDTO) {
        if (delegateTask.getCandidates() != null && !delegateTask.getCandidates().isEmpty()) {
            List<String> candidateUsers = delegateTask.getCandidates().stream()
                    .map(IdentityLink::getUserId)
                    .collect(Collectors.toList());
            taskDTO.setCandidateUsers(candidateUsers);
        }
    }
} 