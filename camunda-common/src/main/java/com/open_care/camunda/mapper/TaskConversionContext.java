package com.open_care.camunda.mapper;

import lombok.Data;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务转换上下文
 * 用于在转换过程中传递额外信息，支持多任务共享候选人数据
 *
 * <AUTHOR>
 * @date :2025/5/22
 */
@Data
public class TaskConversionContext {
    private Map<String, List<String>> taskCandidatesCache = new HashMap<>();
    private Map<String, Map<String, Object>> taskVariablesCache = new HashMap<>();
    private TaskService taskService;
    private List<String> taskIds;
    
    /**
     * 默认构造函数
     */
    public TaskConversionContext() {
    }

    
    /**
     * 使用TaskService创建上下文并预加载单个任务信息
     *
     * @param taskService 任务服务
     * @param taskId 任务ID
     * @return 任务转换上下文
     */
    public static TaskConversionContext createWithTask(TaskService taskService, String taskId) {
        if (taskService == null || taskId == null) {
            return new TaskConversionContext();
        }
        
        TaskConversionContext context = new TaskConversionContext();
        context.setTaskService(taskService);
        context.setTaskIds(List.of(taskId));
        
        // 加载候选人
        List<String> candidateUsers = taskService.getIdentityLinksForTask(taskId).stream()
                .filter(link -> "candidate".equals(link.getType()))
                .map(IdentityLink::getUserId)
                .filter(userId -> userId != null)
                .collect(Collectors.toList());
        context.taskCandidatesCache.put(taskId, candidateUsers);
        
        // 加载变量
        Map<String, Object> variables = taskService.getVariables(taskId);
        context.taskVariablesCache.put(taskId, variables);
        
        return context;
    }
    
    /**
     * 使用TaskService创建上下文并预加载多个任务信息
     *
     * @param taskService 任务服务
     * @param taskIds 任务ID列表
     * @return 任务转换上下文
     */
    public static TaskConversionContext createWithTasks(TaskService taskService, List<String> taskIds) {
        if (taskService == null || taskIds == null || taskIds.isEmpty()) {
            return new TaskConversionContext();
        }
        
        TaskConversionContext context = new TaskConversionContext();
        context.setTaskService(taskService);
        context.setTaskIds(taskIds);
        
        // 为每个任务加载候选人和变量
        for (String taskId : taskIds) {
            // 加载候选人
            List<String> candidateUsers = taskService.getIdentityLinksForTask(taskId).stream()
                    .filter(link -> "candidate".equals(link.getType()))
                    .map(IdentityLink::getUserId)
                    .filter(userId -> userId != null)
                    .collect(Collectors.toList());
            context.taskCandidatesCache.put(taskId, candidateUsers);
            
            // 加载变量
            Map<String, Object> variables = taskService.getVariables(taskId);
            context.taskVariablesCache.put(taskId, variables);
        }
        
        return context;
    }

    
    /**
     * 直接创建带有预设候选人和变量数据的上下文
     *
     * @param taskCandidates 任务ID到候选人列表的映射
     * @param taskVariables 任务ID到变量映射的映射
     * @return 任务转换上下文
     */
    public static TaskConversionContext create(
            Map<String, List<String>> taskCandidates,
            Map<String, Map<String, Object>> taskVariables) {
        TaskConversionContext context = new TaskConversionContext();
        if (taskCandidates != null) {
            context.taskCandidatesCache.putAll(taskCandidates);
        }
        if (taskVariables != null) {
            context.taskVariablesCache.putAll(taskVariables);
        }
        return context;
    }

    /**
     * 获取任务的候选人列表
     */
    public List<String> getCandidateUsers(String taskId) {
        return taskCandidatesCache.getOrDefault(taskId, Collections.emptyList());
    }



    /**
     * 获取任务变量
     */
    public Map<String, Object> getTaskVariables(String taskId) {
        return taskVariablesCache.getOrDefault(taskId, Collections.emptyMap());
    }

} 