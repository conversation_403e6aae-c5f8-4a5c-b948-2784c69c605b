package com.open_care.camunda.mapper;

import com.open_care.api.common.dto.ExecutionEventDTO;
import com.open_care.api.common.dto.ExecutionPropertiesDTO;
import com.open_care.api.common.enums.ExecutionEventType;
import com.open_care.camunda.util.EventHandlerConst;
import com.open_care.camunda.event.Event;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.mapstruct.*;

/**
 * 执行对象映射器
 * 使用MapStruct将DelegateExecution对象转换为ExecutionDTO
 *
 * <AUTHOR>
 * @date :2025/4/11
 */
@Mapper(componentModel = "spring",imports = {ExecutionEventType.class})
public interface ExecutionMapper {

    /**
     * 将DelegateExecution转换为ExecutionDTO
     *
     * @param execution Camunda执行对象
     * @return 执行DTO
     */
    @Mapping(source = "id", target = "executionId")
    @Mapping(source = "processInstanceId", target = "processInstanceId")
    @Mapping(source = "processDefinitionId", target = "processDefinitionId")
    @Mapping(source = "parentId", target = "parentId")
    @Mapping(source = "variables", target = "variables")
    @Mapping(source = "variablesLocal", target = "variablesLocal")
    @Mapping(source = "tenantId", target = "tenantId")
    @Mapping(source = "eventName", target = "eventName")
    @Mapping(source = "currentActivityId", target = "currentActivityId")
    @Mapping(source = "activityInstanceId", target = "activityInstanceId")
    @Mapping(source = "parentActivityInstanceId", target = "parentActivityInstanceId")
    @Mapping(target = "eventType", expression = "java(ExecutionEventType.valueOfType(execution.getEventName()))")
    @Mapping(target = "businessKey",source = "processBusinessKey") // 需要额外获取
    @Mapping(target = "superExecutionId", ignore = true) // 需要额外获取
    @Mapping(target = "currentActivityName", ignore = true) // 需要额外获取
    @Mapping(target = "currentTransitionId", ignore = true) // 需要额外获取
    @Mapping(target = "processInstanceActive", ignore = true) // 需要额外获取
    @Mapping(target = "concurrent", constant = "false") // 默认设置
    @Mapping(target = "sourceActivityId", ignore = true) // 需要额外设置
    @Mapping(target = "targetActivityId", ignore = true) // 需要额外设置
    @Mapping(target = "processInstanceStartTime", ignore = true) // 需要额外获取
    @Mapping(target = "processExceptionMessage", ignore = true) // 需要额外设置
    @Mapping(target = "processInstanceEnded", constant = "false") // 默认为false
    @Mapping(target = "processState", constant = "active")
    @Mapping(target = "properties",ignore = true)
    // 默认为活动状态
    ExecutionEventDTO toExecutionDTO(DelegateExecution execution);

    /**
     * 将Event<DelegateExecution>转换为ExecutionDTO
     *
     * @param event 执行事件对象
     * @return 执行DTO
     */
    default ExecutionEventDTO toExecutionDTO(Event<DelegateExecution> event) {
        DelegateExecution execution = event.getOriginal();

        ExecutionEventDTO dto = toExecutionDTO(execution);

        setPropertiesFromEvent(event, dto);
        return dto;
    }

    /**
     * 从Event对象设置属性到ExecutionDTO
     *
     * @param event 执行事件对象
     * @param dto 目标DTO
     */
    @AfterMapping
    default void setPropertiesFromEvent(Event<DelegateExecution> event, @MappingTarget ExecutionEventDTO dto) {
        ExecutionPropertiesDTO executionPropertiesDTO = new ExecutionPropertiesDTO();
        
        // 从Event中获取listenHandler
        String listenHandler = event.getVariableOrExtensionProperty(EventHandlerConst.EXECUTION_LISTEN_NAME, null);
        
        executionPropertiesDTO.setListenHandler(listenHandler);

        dto.setProperties(executionPropertiesDTO);
    }
}