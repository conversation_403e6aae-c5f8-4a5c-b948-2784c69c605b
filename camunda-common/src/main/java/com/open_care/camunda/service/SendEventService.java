package com.open_care.camunda.service;

import com.open_care.api.common.dto.ExecutionEventDTO;
import com.open_care.api.common.dto.TaskEventDTO;

/**
 * 事件发送服务接口
 * 用于发送任务和执行相关的事件
 *
 * <AUTHOR>
 * @date :2025/4/17
 */
public interface SendEventService {

    /**
     * 同步发送任务事件
     *
     * @param taskDTO 任务事件DTO
     */
    void sendTaskEvent(TaskEventDTO taskDTO);

    /**
     * 异步发送任务事件
     *
     * @param taskDTO 任务事件DTO
     */
    void sendTaskEventAsync(TaskEventDTO taskDTO);

    /**
     * 同步发送执行事件
     *
     * @param executionDTO 执行事件DTO
     */
    void sendExecutionEvent(ExecutionEventDTO executionDTO);

    /**
     * 异步发送执行事件
     *
     * @param executionDTO 执行事件DTO
     */
    void sendExecutionEventAsync(ExecutionEventDTO executionDTO);
}