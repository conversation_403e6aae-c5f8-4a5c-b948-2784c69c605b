package com.open_care.camunda.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.open_care.api.client.CamundaListenerService;
import com.open_care.api.common.dto.ExecutionEventDTO;
import com.open_care.api.common.dto.TaskEventDTO;
import com.open_care.camunda.properties.TaskEventProperties;
import com.open_care.camunda.service.SendEventService;
import com.open_care.camunda.component.utils.FeignClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 事件发送服务实现类
 * 用于发送任务和执行相关的事件
 *
 * <AUTHOR>
 * @date :2025/4/17
 */
@Slf4j
@Service
public class SendEventServiceImpl implements SendEventService {
    
    @Autowired
    private FeignClientUtils feignClientUtils;

    @Autowired
    private TaskEventProperties taskEventProperties;

    @Autowired
    private ThreadPoolTaskExecutor defaultThreadPoolTaskExecutor;

    @Autowired
    private LoadBalancerClient loadBalancerClient;

    @Override
    public void sendTaskEvent(TaskEventDTO taskDTO) {
        if (ObjectUtil.isNull(taskDTO)) {
            log.warn("Task event DTO is null, skipping send");
            return;
        }

        try {
            // TODO: 实现实际的事件发送逻辑
            log.info("Sending task event: {}", taskDTO);
        } catch (Exception e) {
            log.error("Failed to send task event: {}", taskDTO, e);
        }
    }

    @Override
    public void sendTaskEventAsync(TaskEventDTO taskDTO) {
        if (ObjectUtil.isNull(taskDTO)) {
            log.warn("Task event DTO is null, skipping async send");
            return;
        }

        ThreadUtil.execAsync(() -> {
            try {
                sendTaskEvent(taskDTO);
            } catch (Exception e) {
                log.error("Failed to send task event asynchronously: {}", taskDTO, e);
            }
        });
    }

    @Override
    public void sendExecutionEvent(ExecutionEventDTO executionDTO) {
        if (ObjectUtil.isNull(executionDTO)) {
            log.warn("Execution event DTO is null, skipping send");
            return;
        }

        try {
            // TODO: 实现实际的事件发送逻辑
            log.info("Sending execution event: {}", executionDTO);
        } catch (Exception e) {
            log.error("Failed to send execution event: {}", executionDTO, e);
        }
    }

    @Override
    public void sendExecutionEventAsync(ExecutionEventDTO executionDTO) {
        if (ObjectUtil.isNull(executionDTO)) {
            log.warn("Execution event DTO is null, skipping async send");
            return;
        }

        ThreadUtil.execAsync(() -> {
            try {
                sendExecutionEvent(executionDTO);
            } catch (Exception e) {
                log.error("Failed to send execution event asynchronously: {}", executionDTO, e);
            }
        });
    }

    /**
     * 获取监听器服务客户端
     * 
     * @param tenantId 租户ID
     * @return 监听器服务客户端
     */
    private CamundaListenerService getListenerService(String tenantId) {
        // 获取服务名称
        String serviceName = taskEventProperties.getServiceName(tenantId);
        if (serviceName == null) {
            log.error("未找到对应租户ID的服务名称, tenantId: {}", tenantId);
            return null;
        }

        log.debug("根据租户ID[{}]获取到服务名称[{}]，准备创建Feign客户端", tenantId, serviceName);

        // 使用FeignClientUtils创建客户端
        return feignClientUtils.createClient(serviceName, CamundaListenerService.class);
    }
}