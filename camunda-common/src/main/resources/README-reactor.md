# Camunda 事件监听器

本模块实现了基于标准接口的 Camunda 引擎事件监听器，取代了传统的插件注入方式。

## 功能特点

1. 基于Spring Bean注解实现的事件监听
2. 支持全局监听和特定流程/活动监听
3. 支持执行监听器和任务监听器
4. 自动注册为Spring Bean，可以注入其他服务

## 现有监听器

### 1. 通用监听器
- `ReactorTaskEventListener` - 所有任务事件的通用监听器
- `ReactorExecutionEventListener` - 所有执行事件的通用监听器

### 2. 特定监听器
- `SpecificProcessListener` - 针对特定流程和活动的执行事件监听器
- `SpecificTaskListener` - 针对特定任务类型的特定事件监听器

## 使用方法

### 1. 添加依赖

确保在项目中添加了Spring和Camunda依赖：

```gradle
implementation("org.springframework.boot:spring-boot-starter")
implementation("org.camunda.bpm:camunda-engine")
```

### 2. 添加配置类

确保引入了 `CamundaReactorConfig` 配置类，它注册了所有监听器：

```java
@Configuration
@ComponentScan("com.open_care.camunda.listener")
public class CamundaReactorConfig {
    // 配置代码
}
```

### 3. 创建自定义的监听器

#### 全局执行监听器示例：

```java
@Component
public class MyExecutionListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        // 实现您的业务逻辑
    }
}
```

#### 特定流程和活动的监听器：

```java
@Component
public class SpecificProcessListener implements ExecutionListener {

    private static final String TARGET_PROCESS_KEY = "myProcess";
    private static final String TARGET_ACTIVITY_ID = "approvalTask";
    private static final String TARGET_EVENT_NAME = "start";

    @Override
    public void notify(DelegateExecution execution) {
        // 检查是否是目标流程和活动
        String processDefKey = extractProcessDefinitionKey(execution.getProcessDefinitionId());
        
        if (!TARGET_PROCESS_KEY.equals(processDefKey) || 
            !TARGET_ACTIVITY_ID.equals(execution.getCurrentActivityId()) || 
            !TARGET_EVENT_NAME.equals(execution.getEventName())) {
            return;
        }
        
        // 实现业务逻辑
    }
    
    // 辅助方法...
}
```

#### 特定任务的监听器：

```java
@Component
public class MyTaskListener implements TaskListener {

    private static final String TARGET_TASK_DEF_KEY = "reviewTask";
    private static final List<String> TARGET_EVENT_NAMES = Arrays.asList("create", "complete");

    @Override
    public void notify(DelegateTask task) {
        // 检查是否是目标任务和事件
        if (!TARGET_TASK_DEF_KEY.equals(task.getTaskDefinitionKey()) || 
            !TARGET_EVENT_NAMES.contains(task.getEventName())) {
            return;
        }
        
        // 实现业务逻辑
    }
}
```

## 优势

1. 无需手动编写和注册复杂的插件类
2. 更符合Spring的Bean管理方式
3. 更精细的事件控制，可以针对特定流程、活动或任务
4. 代码结构更清晰，每个监听器只关注特定场景 