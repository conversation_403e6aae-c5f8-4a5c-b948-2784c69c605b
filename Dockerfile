ARG BASE_IMAGE=eclipse-temurin:17-jdk-jammy

FROM ${BASE_IMAGE}

# 设置编码为UTF-8
ENV LANG zh_CN.UTF-8
ENV LANGUAGE zh_CN:zh
ENV LC_ALL zh_CN.UTF-8

# 设置时区为CST
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY camunda-server/build/libs/camunda-server-1.0.0-SNAPSHOT.jar /opt/open-care/camunda.jar

COPY docker_files/start.sh /opt/open-care/start.sh

RUN chmod +x /opt/open-care/start.sh

ENTRYPOINT ["/opt/open-care/start.sh"]
EXPOSE 8080 