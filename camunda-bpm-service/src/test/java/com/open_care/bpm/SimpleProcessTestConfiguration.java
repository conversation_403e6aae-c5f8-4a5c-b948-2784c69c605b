/*
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm;

import cn.dev33.satoken.spring.SaBeanInject;
import cn.dev33.satoken.spring.SaBeanRegister;
import cn.dev33.satoken.spring.oauth2.SaOAuth2BeanInject;
import cn.dev33.satoken.spring.oauth2.SaOAuth2BeanRegister;
import cn.dev33.satoken.spring.sso.SaSsoBeanInject;
import cn.dev33.satoken.spring.sso.SaSsoBeanRegister;
import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.client.GatewayRemoteService;
import com.open_care.api.client.MessageRemoteService;
import com.open_care.bpm.config.AsyncConfig;
import com.open_care.bpm.config.BpmConfig;
import com.open_care.bpm.config.CamundaProperties;
import com.open_care.bpm.script.groovy.OcGroovyScriptExtendPlugin;
import com.open_care.bpm.script.groovy.context.OpenCareContext;
import com.open_care.bpm.script.groovy.context.service.*;
import com.open_care.bpm.util.SpringContextUtil;
import com.open_care.util.json.JacksonJsonConvert;
import com.open_care.util.json.JsonConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.mockito.Mockito;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration;
import org.springframework.boot.autoconfigure.r2dbc.R2dbcAutoConfiguration;
import org.springframework.boot.autoconfigure.r2dbc.R2dbcTransactionManagerAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.camunda.bpm.spring.boot.starter.CamundaBpmAutoConfiguration;
import org.camunda.bpm.spring.boot.starter.configuration.CamundaAuthorizationConfiguration;
import org.camunda.bpm.spring.boot.starter.rest.CamundaBpmRestJerseyAutoConfiguration;

import javax.sql.DataSource;

/**
 * 简化的测试配置类
 * 用于测试Camunda流程和Groovy脚本功能
 */
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {
        RedisAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class,
        R2dbcAutoConfiguration.class,
        R2dbcTransactionManagerAutoConfiguration.class,

        SaBeanRegister.class,
        SaBeanInject.class,
        SaSsoBeanRegister.class,
        SaSsoBeanInject.class,
        SaOAuth2BeanInject.class,
        SaOAuth2BeanRegister.class,
})
@Import({
        BpmConfig.class,
        AsyncConfig.class,

        DataSourceTransactionManagerAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        JdbcTemplateAutoConfiguration.class,

        CamundaBpmAutoConfiguration.class,
        CamundaBpmRestJerseyAutoConfiguration.class
})
@ComponentScan(basePackages = {
        "org.camunda",
        "com.open_care.bpm.config",
        "com.open_care.bpm.script"
})
@Log4j2
public class SimpleProcessTestConfiguration {


}