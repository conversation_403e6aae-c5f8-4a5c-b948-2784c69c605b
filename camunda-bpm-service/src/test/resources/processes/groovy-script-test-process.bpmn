<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn" 
                  exporter="Camunda Modeler" 
                  exporterVersion="5.0.0">
  
  <bpmn:process id="groovy-script-test-process" name="Groovy Script Test Process" isExecutable="true">
    
    <bpmn:startEvent id="StartEvent_1" name="Start">
      <bpmn:outgoing>SequenceFlow_1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:scriptTask id="ScriptTask_1" name="Execute Groovy Script" scriptFormat="groovy" camunda:resultVariable="scriptResult">
      <bpmn:incoming>SequenceFlow_1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_2</bpmn:outgoing>
      <bpmn:script><![CDATA[
// 获取测试类型
def testType = execution.getVariable("testType")
def result = [:]

try {
    if (testType == "save") {
        // 创建SaveRequestDTO对象
        def saveRequest = new com.open_care.api.common.dto.crud.SaveRequestDTO()
        saveRequest.setEntityName("TestEntity")
        def entityData = [
            "ocId": "test-id-123",
            "name": "Test Entity",
            "description": "Test Description"
        ]
        saveRequest.setData(entityData)
        
        def response = OC.crud.save(saveRequest)
        result = [success: true, saveSuccess: true, message: "Save operation completed", hasResponse: response != null]
    } else if (testType == "saveWithException") {
        // 测试异常处理 - 这会调用真实的Mock服务，触发异常
        def saveRequest = new com.open_care.api.common.dto.crud.SaveRequestDTO()
        saveRequest.setEntityName("TestEntity")
        def entityData = [
            "ocId": "exception-test-id",
            "name": "Exception Test Entity",
            "description": "This should trigger an exception"
        ]
        saveRequest.setData(entityData)
        
        def response = OC.crud.save(saveRequest)
        result = [success: true, saveSuccess: true, message: "Save operation completed", hasResponse: response != null]
    } else if (testType == "get") {
        def response = OC.crud.get("TestEntity", "test-id-123")
        result = [success: true, message: "Get operation completed", hasResponse: response != null]
    } else if (testType == "querySimple") {
        // 简化的查询测试，不依赖真实的远程服务调用
        result = [success: true, message: "Query operation completed (simplified)", querySuccess: true]
    } else if (testType == "query") {
        def queryRequest = new com.open_care.api.common.dto.crud.QueryRequestDTO()
        queryRequest.setEntityName("TestEntity")
        
        def response = OC.crud.query(queryRequest)
        result = [success: true, message: "Query operation completed", hasResponse: response != null]
    } else if (testType == "delete") {
        def response = OC.crud.delete("test-id-123")
        result = [success: true, message: "Delete operation completed", hasResponse: response != null]
    } else if (testType == "batchSaveSimple") {
        // 简化的批量保存测试，不依赖真实的远程服务调用
        result = [success: true, message: "BatchSave operation completed (simplified)", batchSaveSuccess: true]
    } else if (testType == "batchSave") {
        def batchSaveRequest = new com.open_care.api.common.dto.BatchSaveDTO()
        batchSaveRequest.setEntityName("TestEntity")
        
        // 创建SaveRequestDTO列表
        def saveRequestList = []
        def saveRequest1 = new com.open_care.api.common.dto.crud.SaveRequestDTO()
        saveRequest1.setEntityName("TestEntity")
        saveRequest1.setData(["ocId": "batch-id-1", "name": "Batch Entity 1"])
        saveRequestList.add(saveRequest1)
        
        def saveRequest2 = new com.open_care.api.common.dto.crud.SaveRequestDTO()
        saveRequest2.setEntityName("TestEntity")
        saveRequest2.setData(["ocId": "batch-id-2", "name": "Batch Entity 2"])
        saveRequestList.add(saveRequest2)
        
        batchSaveRequest.setData(saveRequestList)
        
        def response = OC.crud.batchSave(batchSaveRequest)
        result = [success: true, message: "BatchSave operation completed", hasResponse: response != null]
    } else {
        result = [success: false, message: "Unknown test type: " + testType]
    }
    
    execution.setVariable("testResult", result)
} catch (Exception e) {
    def errorResult = [success: false, error: e.getMessage(), message: "Script execution failed"]
    execution.setVariable("testResult", errorResult)
    throw e
}

result
      ]]></bpmn:script>
    </bpmn:scriptTask>
    
    <bpmn:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="ScriptTask_1" />
    
    <bpmn:sequenceFlow id="SequenceFlow_2" sourceRef="ScriptTask_1" targetRef="EndEvent_1" />
    
    <bpmn:endEvent id="EndEvent_1" name="End">
      <bpmn:incoming>SequenceFlow_2</bpmn:incoming>
    </bpmn:endEvent>
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="groovy-script-test-process">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="185" y="142" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ScriptTask_1_di" bpmnElement="ScriptTask_1">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="432" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="432" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="440" y="142" width="20" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  
</bpmn:definitions> 