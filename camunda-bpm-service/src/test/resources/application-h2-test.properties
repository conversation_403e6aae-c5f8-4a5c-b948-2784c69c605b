# H2 Test Database Configuration for SimpleProcessTest
spring.application.name=camunda-bpm-service-test

# Allow Bean definition overriding
spring.main.allow-bean-definition-overriding=true

# H2 In-Memory Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for H2
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Camunda Configuration for Testing
camunda.bpm.auto-deployment-enabled=true
camunda.bpm.job-execution.enabled=false
camunda.bpm.metrics.enabled=false
camunda.bpm.id-generator=strong
camunda.bpm.database.schema-update=true

# Disable History TTL enforcement for testing
camunda.bpm.generic-properties.properties.enforce-history-time-to-live=false

# Enable automatic deployment from processes directory
camunda.bpm.deployment-resource-pattern=classpath*:processes/*.bpmn

# Logging Configuration
logging.level.com.open_care.bpm=DEBUG
logging.level.org.camunda=WARN
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN
logging.level.org.h2=WARN

# Disable actuator endpoints for testing
management.endpoints.enabled=false
management.endpoint.health.enabled=false

# Test-specific properties
test.mode=true
test.database=h2 