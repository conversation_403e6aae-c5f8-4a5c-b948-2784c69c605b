package com.open_care;

import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.impl.juel.IdentifierNode;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 * @date :2025/4/10
 */
@ComponentScan(basePackages = {"com.open_care.bpm"})
@EnableJpaRepositories("com.open_care.bpm.repository")
@EntityScan(basePackages = {"com.open_care.bpm.entity"})
@EnableFeignClients(basePackages = {"com.open_care.api.client"})
@Configuration
@Log4j2
public class BpmConfiguration implements InitializingBean {
    /**
     * Invoked by the containing {@code BeanFactory} after it has set all bean properties
     * and satisfied {@link BeanFactoryAware}, {@code ApplicationContextAware} etc.
     * <p>This method allows the bean instance to perform validation of its overall
     * configuration and final initialization when all bean properties have been set.
     *
     * @throws Exception in the event of misconfiguration (such as failure to set an
     *                   essential property) or if initialization fails for any other reason
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Initializing BpmConfiguration");
    }
}
