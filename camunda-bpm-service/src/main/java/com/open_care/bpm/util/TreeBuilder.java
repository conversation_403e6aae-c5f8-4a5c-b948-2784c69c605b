package com.open_care.bpm.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.stream.StreamUtil;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class TreeBuilder {

    /**
     * 将实体集合转换为 DTO 的树结构。
     * 方法适用于entity定义当中，只有parent 没有child的数据模型
     * <p>
     * 不支持过滤条件查询的结构构建成tree(前提条件是entity包含了所有的parent信息，）
     * 例如
     * A
     * |-A1
     * |--A11
     * |-A2
     * B
     * |-B1
     * |-B2
     * <p>
     * <p>
     * 不可以单独根据A11构建成tree (前提是需要吧A11的parent和parent的parent查询出来，)
     *
     * @param <E>              The type of the entity objects.
     * @param <R>              The type of the DTO objects.
     * @param entityList       所有需要转换成tree的对象, 需要开发者自己把entity集合平铺好
     * @param parentFun        获取实体父级的函数。
     * @param entityKeyFunctor 获取实体主键的函数。
     * @param dtoFun           将实体转换为 DTO 的函数。
     * @param dtoKeyFunctor    获取 DTO 主键的函数。
     * @param childInitFun     初始化孩子节点Supplier 因为不知道孩子节点的具体类型，需要让调用者告知child的集合是什么类型
     * @param dtoSetterChild   dto设置child 的时候调用那个方法，调用者告知设置dto的child的时候调用那个方法
     * @return A collection of DTOs representing the tree structure.
     */
    public static <E, R> Collection<R> entityToDTOTree(Collection<E> entityList,
                                                       Function<E, E> parentFun,
                                                       Function<E, String> entityKeyFunctor,
                                                       Function<E, R> dtoFun,
                                                       Function<R, String> dtoKeyFunctor,
                                                       Supplier<Collection<R>> childInitFun,
                                                       BiConsumer<R, Collection<R>> dtoSetterChild) {
        Objects.requireNonNull(parentFun, "parentFun cannot be null");
        Objects.requireNonNull(entityKeyFunctor, "entityKeyFunctor cannot be null");
        Objects.requireNonNull(dtoFun, "dtoFun cannot be null");
        Objects.requireNonNull(dtoKeyFunctor, "dtoKeyFunctor cannot be null");
        Objects.requireNonNull(childInitFun, "childInitFun cannot be null");
        Objects.requireNonNull(dtoSetterChild, "dtoSetterChild cannot be null");
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        //FIXME: 有两个对象,A B, B是A的parent, 此时如果A 有一个属性例如ownOrgs, 关联了一个对象,
        // 此时会存在的问题是, A对象的parent(也就是B) 没有查询出ownOrgs,但是 B对象本身查询出了ownOrgs, 在过滤重复的对象的时候可以回留下A,可能回留下B,就会导致的
        // 可能会看到ownOrgs可能看不到ownOrgs

        // 深度优先平铺所有的entity
//        List<E> allEntity = flattenDepthFirst(entityList, parentFun, entityKeyFunctor);
        // 先把所有的entity 平铺成一个list
        List<E> allEntity = new ArrayList<>(entityList);
        Map<String, List<E>> parentChildMap = allEntity.stream()
                // 从所有的entity过滤掉没有父节点的数据
                .filter(item -> Objects.nonNull(parentFun.apply(item)))
                // 父亲节点id和孩子组合的map
                .collect(Collectors.groupingBy(item -> entityKeyFunctor.apply(parentFun.apply(item)), Collectors.toList()));

        LinkedList<R> rootNode = getRootNodes(allEntity, parentFun).stream().map(dtoFun).collect(Collectors.toCollection(LinkedList::new));

        depthFirstBuildTree(rootNode, dtoFun, dtoKeyFunctor, parentChildMap, childInitFun, dtoSetterChild);
        return rootNode;

    }

    public static <E, R> void depthFirstBuildTree(Collection<R> rootNode, Function<E, R> dtoFun, Function<R, String> dtoKeyFunctor, Map<String, List<E>> parentChildMap, Supplier<Collection<R>> childInitFun, BiConsumer<R, Collection<R>> dtoSetterChild) {
        Objects.requireNonNull(dtoFun, "dtoFun cannot be null");
        Objects.requireNonNull(dtoKeyFunctor, "dtoKeyFunctor cannot be null");
        Objects.requireNonNull(childInitFun, "childInitFun cannot be null");
        Objects.requireNonNull(dtoSetterChild, "dtoSetterChild cannot be null");
        // root为空了 就不设置了，
        if (CollUtil.isEmpty(rootNode)) {
            return;
        }

        for (R root : rootNode) {
            // 获取DTO的主键，找到所有节点所有的孩子
            String parentKey = dtoKeyFunctor.apply(root);

            Collection<R> childNodes = parentChildMap.getOrDefault(parentKey, Collections.emptyList()).stream().map(dtoFun).collect(Collectors.toCollection(childInitFun));
            // 没有孩子节点了 就不设置了，
            if (CollUtil.isEmpty(rootNode)) {
                continue;
            }
            dtoSetterChild.accept(root, childNodes);

            depthFirstBuildTree(childNodes, dtoFun, dtoKeyFunctor, parentChildMap, childInitFun, dtoSetterChild);
        }

    }

    /**
     * 从一个list当中获取所有的 没有父亲的节点，也就是所有的根节点
     *
     * @param allEntity 所有的entity数据
     * @param parentFun 获取父亲节点的方法。
     * @return 根节点的所有数据0
     */
    public static <E> LinkedList<E> getRootNodes(Collection<E> allEntity, Function<E, E> parentFun) {
        return allEntity.stream().filter(entity -> Objects.nonNull(entity) && Objects.isNull(parentFun.apply(entity))).collect(Collectors.toCollection(LinkedList::new));
    }

    /**
     * 使用深度优先方法展平实体集合。
     *
     * @param <E>              The type of the entity objects.
     * @param entityList       The collection of entity objects to be flattened.
     * @param parentFun        A function that retrieves the parent entity of a given entity.
     * @param entityKeyFunctor 获取key 然后根据id相同的进行去重操作
     * @return A list containing the flattened entities in a depth-first order.
     */
    public static <E> List<E> flattenDepthFirst(Collection<E> entityList, Function<E, E> parentFun, Function<E, String> entityKeyFunctor) {
        List<E> flattenedList = new LinkedList<>();
        for (E entity : entityList) {
            flattenDepthFirstRecursive(entity, parentFun, flattenedList);
        }

        return new ArrayList<>(flattenedList.stream().collect(Collectors.toMap(entityKeyFunctor, Function.identity(), (o, n) -> o)).values());
    }

    private static <E> void flattenDepthFirstRecursive(E entity, Function<E, E> parentFun, List<E> flattenedList) {
        flattenedList.add(entity);
        E parent = entity;
        while ((parent = parentFun.apply(parent)) != null) {
            flattenedList.add(parent);

        }
    }

    /**
     * 根据过滤条件过滤tree，中序遍历
     *
     * @param <T>         The type of the objects in the collection.
     * @param dtoTree     已经构建完成的tree结构数据.
     * @param getChildFun 从tree中获取孩子的方法.
     * @param setChildFun 设置treeChild的方法
     * @param predicates  过滤条件.
     * @return A list of root nodes representing the tree structure.
     */
    public static <T> List<T> filterTreeNodes(Collection<T> dtoTree, Function<T, Collection<T>> getChildFun, BiConsumer<T, Collection<T>> setChildFun, List<Predicate<T>> predicates) {

        if (CollUtil.isEmpty(dtoTree)) {
            return ListUtil.empty();
        }

        ArrayList<T> result = new ArrayList<>();
        for (T rootNode : dtoTree) {
            result.add(findMatchingNode(rootNode, getChildFun, setChildFun, predicates));
        }

        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static <T> T findMatchingNode(T currentRootNode, Function<T, Collection<T>> getChildFun, BiConsumer<T, Collection<T>> setChildFun, List<Predicate<T>> predicates) {
        Objects.requireNonNull(currentRootNode, "currentRootNode cannot be null");

        // 如果root节点的和过滤条相等，直接返回
        if (StreamUtil.of(predicates).allMatch(item -> item.test(currentRootNode))) {
            return currentRootNode;
        }

        Collection<T> child = getChildFun.apply(currentRootNode);
        if (CollUtil.isEmpty(child)) {
            return null;
        }

        List<T> newChildren = new ArrayList<>();
        // 当前节点值未匹配，查看所有的子节点
        for (T t : child) {
            T newChild = findMatchingNode(t, getChildFun, setChildFun, predicates);
            // 如果存在子节点匹配到目标字段，将匹配的子节点添加到newChildren
            if (newChild != null) newChildren.add(newChild);
        }

        // 如果有子节点与目标字段匹配，创建新的节点，设置子节点为过滤后的newChildren
        if (CollUtil.size(newChildren) > 0) {
            setChildFun.accept(currentRootNode, newChildren);
            return currentRootNode;
        } else {
            // 没有子节点匹配到目标字段，所以删除当前节点（返回null）
            return null;
        }

    }

    /**
     * DFS的对集合进行排序
     *
     * @param <T>          The type of objects in the collection.
     * @param collection   The collection of objects to be sorted.
     * @param childInitFun 如何或许子集合
     * @param comparator   排序规则
     * @return A sorted collection of objects and their child collections.
     */
    public static <T> Collection<T> sortTree(Collection<T> collection, Function<T, Collection<T>> childInitFun, Comparator<T> comparator) {
        if (collection == null || collection.isEmpty()) {
            return Collections.emptyList();
        }

        return collection.stream()
                .sorted(comparator)
                .peek(item -> sortTree(childInitFun.apply(item), childInitFun, comparator))
                .collect(Collectors.toList());
    }
}
