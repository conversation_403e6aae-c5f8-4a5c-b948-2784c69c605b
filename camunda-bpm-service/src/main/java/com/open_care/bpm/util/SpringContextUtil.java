package com.open_care.bpm.util;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring上下文工具类
 * 用于在非Spring管理的类中获取Spring Bean
 * 
 * <AUTHOR>
 * @date 2025/5/28
 */
@Log4j2
@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * Spring容器会自动调用此方法设置ApplicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
        log.info("SpringContextUtil: ApplicationContext自动设置完成: {}", applicationContext != null);
    }

    /**
     * 手动设置ApplicationContext（用于测试环境）
     */
    public static void setApplicationContextManually(ApplicationContext context) {
        applicationContext = context;
        log.info("SpringContextUtil: ApplicationContext手动设置完成: {}", context != null);
    }

    /**
     * 获取ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 根据类型获取Bean
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext == null) {
            log.error("SpringContextUtil: ApplicationContext为null，无法获取Bean: {}", clazz.getName());
            return null;
        }
        try {
            return applicationContext.getBean(clazz);
        } catch (Exception e) {
            log.error("SpringContextUtil: 获取Bean失败: {}", clazz.getName(), e);
            return null;
        }
    }

    /**
     * 根据名称获取Bean
     */
    public static Object getBean(String name) {
        if (applicationContext == null) {
            log.error("SpringContextUtil: ApplicationContext为null，无法获取Bean: {}", name);
            return null;
        }
        try {
            return applicationContext.getBean(name);
        } catch (Exception e) {
            log.error("SpringContextUtil: 获取Bean失败: {}", name, e);
            return null;
        }
    }

    /**
     * 根据名称和类型获取Bean
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        if (applicationContext == null) {
            log.error("SpringContextUtil: ApplicationContext为null，无法获取Bean: {} ({})", name, clazz.getName());
            return null;
        }
        try {
            return applicationContext.getBean(name, clazz);
        } catch (Exception e) {
            log.error("SpringContextUtil: 获取Bean失败: {} ({})", name, clazz.getName(), e);
            return null;
        }
    }
} 