//package com.open_care.bpm.config;
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import com.open_care.bpm.context.SaTokenContext;
//import feign.RequestInterceptor;
//import feign.RequestTemplate;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import jakarta.servlet.http.HttpServletRequest;
//import java.util.function.Supplier;
//
///**
// * <AUTHOR>
// * @date :2025/3/11
// */
//@Configuration
//@Log4j2
//public class FeignSaTokenFilter implements RequestInterceptor {
//
//    @Override
//    public void apply(RequestTemplate requestTemplate) {
//        setSaToken(requestTemplate);
//    }
//
//    private void setSaToken(RequestTemplate requestTemplate) {
//        log.debug("FeignSaTokenFilter 尝试传递 satoken 上下文");
//
//        HttpServletRequest request = getHttpServletRequest();
//
//        // 处理两个header
//        setHeaderFromRequestOrContext(
//                requestTemplate,
//                request,
//                SaTokenContext.SA_TOKEN_USER_NAME,
//                "satoken-user",
//                SaTokenContext::getSaTokenUser
//        );
//
//        setHeaderFromRequestOrContext(
//                requestTemplate,
//                request,
//                SaTokenContext.SA_SAME_TOKEN_NAME,
//                "sa-same-token",
//                SaTokenContext::getSaSameToken
//        );
//    }
//
//    private static HttpServletRequest getHttpServletRequest() {
//        // 获取请求上下文
//        ServletRequestAttributes servletRequestAttributes = null;
//        HttpServletRequest request = null;
//        try {
//            servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
//        } catch (IllegalStateException e) {
//            log.debug("FeignSaTokenFilter 当前不在 web 上下文中");
//        } catch (Exception e) {
//            log.warn("FeignSaTokenFilter 获取请求上下文异常", e);
//        }
//
//        if (ObjectUtil.isNotNull(servletRequestAttributes)) {
//            request = servletRequestAttributes.getRequest();
//        }
//
//        return request;
//    }
//
//    /**
//     * 从请求头或上下文中获取并设置Header
//     *
//     * @param requestTemplate Feign请求模板
//     * @param request HTTP请求
//     * @param headerName Header名称
//     * @param logName 日志中显示的名称
//     * @param contextValueSupplier 从上下文获取值的函数
//     */
//    private void setHeaderFromRequestOrContext(
//            RequestTemplate requestTemplate,
//            HttpServletRequest request,
//            String headerName,
//            String logName,
//            Supplier<String> contextValueSupplier
//    ) {
//        String headerValue = null;
//
//        // 先从请求头中获取
//        if (ObjectUtil.isNotNull(request)) {
//            headerValue = request.getHeader(headerName);
//            if (StrUtil.isNotBlank(headerValue)) {
//                log.debug("FeignSaTokenFilter 从请求头获取到 {} 信息", logName);
//            }
//        }
//
//        // 如果请求头中没有，则从上下文中获取
//        if (StrUtil.isBlank(headerValue)) {
//            log.debug("FeignSaTokenFilter 请求头中没有 {} 信息，尝试从上下文获取", logName);
//            headerValue = contextValueSupplier.get();
//        }
//
//        if (StrUtil.isNotBlank(headerValue)) {
//            requestTemplate.header(headerName, headerValue);
//        }
//    }
//}
