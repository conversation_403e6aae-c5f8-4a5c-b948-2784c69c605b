/**
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.config;

import groovy.beans.Bindable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Camunda BPM配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "camunda.bpm")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CamundaProperties {
    
    /**
     * 历史数据保留时间
     */
    @Builder.Default
    private String historyTimeToLive = "P30D";
    
    /**
     * 是否强制执行历史数据保留时间策略
     */
    @Builder.Default
    private Boolean enforceHistoryTimeToLive = true;
}
