package com.open_care.bpm.config;

import cn.hutool.core.util.ObjectUtil;
import com.open_care.bpm.context.SaTokenContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date :2025/3/24
 */
@Configuration
@EnableAsync
@Log4j2
public class AsyncConfig implements AsyncConfigurer {

    @Bean
    public ThreadPoolTaskExecutor defaultThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(1024);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("DefaultThreadPoolExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new CustomTaskDecorator());
        executor.initialize();

        return executor;
    }

    /**
     * 为@Async注解提供默认线程池
     */
    @Override
    public Executor getAsyncExecutor() {
        return defaultThreadPoolTaskExecutor();
    }

    /**
     * 处理异步方法执行过程中未捕获的异常
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("Async method '{}' execution error with params: {}", method.getName(), params, ex);
        };
    }

    public static class CustomTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = null;
            if (ObjectUtil.isNotNull(requestAttributes)) {
                request = ((ServletRequestAttributes) requestAttributes).getRequest();
            }
            String saSameToken;
            String satokenUser;
            if(ObjectUtil.isNotNull(request)) {
                saSameToken = request.getHeader(SaTokenContext.SA_SAME_TOKEN_NAME);
            } else {
                saSameToken = null;
            }
            if(ObjectUtil.isNotNull(request)) {
                satokenUser = request.getHeader(SaTokenContext.SA_TOKEN_USER_NAME);
            } else {
                satokenUser = null;
            }


            return () -> {
                SaTokenContext.setSaSameToken(saSameToken);
                SaTokenContext.setSaTokenUser(satokenUser);

                try {
                    runnable.run();
                }finally {
                    SaTokenContext.setSaSameToken(null);
                    SaTokenContext.setSaTokenUser(null);
                }
            };
        }
    }
}
