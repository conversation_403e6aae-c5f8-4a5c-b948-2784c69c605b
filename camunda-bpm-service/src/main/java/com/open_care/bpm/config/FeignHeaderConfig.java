///**
// * Copyright 2019 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.config;
//
//import com.open_care.api.common.ServiceException;
//import com.open_care.bpm.context.AppInfo;
//import com.open_care.bpm.context.AppInfoContext;
//import com.open_care.bpm.context.UserInfo;
//import com.open_care.bpm.context.UserInfoContext;
//import com.open_care.bpm.utils.HeaderUtil;
//import com.open_care.util.json.JsonConverter;
//import com.open_care.util.json.JsonUtil;
//import feign.RequestInterceptor;
//import feign.RequestTemplate;
//import org.springframework.context.annotation.Configuration;
//
//import java.io.UnsupportedEncodingException;
//import java.net.URLEncoder;
//
//@Configuration
//public class FeignHeaderConfig implements RequestInterceptor {
//
//    private final String key = "Authorization";
//
//    JsonConverter jsonConverter = JsonUtil.getJsonConverter();
//
//    @Override
//    public void apply(RequestTemplate requestTemplate) {
//        try {
//            UserInfo user = UserInfoContext.getUser();
//            AppInfo app = AppInfoContext.getApp();
//            if (user != null) {
//                requestTemplate.header("userinfo", URLEncoder.encode(jsonConverter.toJson(user), "UTF-8"));
//            }
//            if (app != null) {
//                requestTemplate.header("appinfo", URLEncoder.encode(jsonConverter.toJson(app), "UTF-8"));
//            }
//
//        } catch (UnsupportedEncodingException e) {
//            throw new ServiceException(e);
//        }
//    }
//
//
//}