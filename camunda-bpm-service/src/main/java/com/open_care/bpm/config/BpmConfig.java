/**
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.bpm.script.groovy.OcGroovyScriptExtendPlugin;
import com.open_care.bpm.script.groovy.context.OpenCareContext;
import com.open_care.util.json.JacksonJsonConvert;
import com.open_care.util.json.JsonConverter;
import org.camunda.bpm.engine.impl.cfg.AbstractProcessEnginePlugin;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.cfg.ProcessEnginePlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;


@Configuration
public class BpmConfig {

//    @Value("${camunda.bpmConfig.cacheCapacity:90}")
//    private int cacheCapacity;

    @Value("${rest.connect-timeout:5000}")
    private int connectTimeout;

    @Value("${rest.read-timeout:10000}")
    private int readTimeout;
    
    @Autowired
    private CamundaProperties camundaProperties;
    
    @Bean
    public JsonConverter jsonConverter(ObjectMapper objectMapper) {
        JacksonJsonConvert.JSON_CONVERTER = new JacksonJsonConvert(objectMapper);
        return JacksonJsonConvert.JSON_CONVERTER;
    }

    @Bean
    public OcGroovyScriptExtendPlugin pcGroovyScriptExtendPlugin(OpenCareContext openCareContext) {
        return new OcGroovyScriptExtendPlugin(openCareContext);
    }
    
    /**
     * 配置历史数据保留时间插件
     * 解决 "History Time To Live (TTL) cannot be null" 错误
     * 
     * @return 历史数据保留时间配置插件
     */
    @Bean
    public ProcessEnginePlugin historyTimeToLivePlugin() {
        return new AbstractProcessEnginePlugin() {
            @Override
            public void preInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
                processEngineConfiguration.setEnforceHistoryTimeToLive(camundaProperties.getEnforceHistoryTimeToLive());
                processEngineConfiguration.setHistoryTimeToLive(camundaProperties.getHistoryTimeToLive());
            }
        };
    }

    /**
     * 配置RestTemplate
     *
     * @param builder RestTemplateBuilder
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .setConnectTimeout(Duration.ofMillis(connectTimeout))
                .setReadTimeout(Duration.ofMillis(readTimeout))
                .build();
    }

    /**
     * 配置ClientHttpRequestFactory
     *
     * @return ClientHttpRequestFactory实例
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        return factory;
    }


}
