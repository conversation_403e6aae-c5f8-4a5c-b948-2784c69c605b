///**
// * Copyright 2019 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.config;
//
//import org.hibernate.dialect.MySQL57Dialect;
//import org.hibernate.dialect.MySQLDialect;
//
//public class MySQLNDB57Dialect extends MySQL57Dialect {
//    private static final String ENGINE_NDB = " ENGINE=NDB"; //$NON-NLS-1$
//
//    @Override
//    public boolean supportsCascadeDelete()
//    {
//        return true;
//    }
//
//    @Override
//    public boolean dropConstraints()
//    {
//        return true;
//    }
//
//    @Override
//    public String getTableTypeString()
//    {
//        return ENGINE_NDB;
//    }
//}
