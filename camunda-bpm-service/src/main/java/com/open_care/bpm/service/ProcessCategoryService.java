package com.open_care.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.open_care.api.common.delete.DeleteParamRequest;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.GetRequestDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.dto.crud.SaveRequestDTO;
import com.open_care.api.common.edit.EditDataResponse;
import com.open_care.api.common.edit.FieldData;
import com.open_care.api.common.export.Page;
import com.open_care.api.common.query.QueryDataResponse;
import com.open_care.bpm.entity.OCProcessCategory;
import com.open_care.bpm.dto.ProcessCategoryDTO;
import com.open_care.bpm.mapper.ProcessCategoryMapper;
import com.open_care.bpm.repository.ProcessCategoryRepository;
import com.open_care.bpm.util.TreeBuilder;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date :2025/2/27
 */
@Service
@Log4j2
public class ProcessCategoryService {
    public static final String CHINESE_DOT = "，";

    @Autowired
    private ProcessCategoryRepository processCategoryRepository;

    @Transactional
    public OcResponse<QueryDataResponse<?>> query(QueryRequestDTO queryRequestDTO) {
        List<FieldData> filters = queryRequestDTO.getFilters();
        String categoryName = getFilterValue(filters, LambdaUtil.getFieldName(ProcessCategoryDTO::getCategoryName));
        String categoryNo = getFilterValue(filters, LambdaUtil.getFieldName(ProcessCategoryDTO::getCategoryNo));
        String parentId = getFilterValue(filters, StrUtil.format("{}#{}", LambdaUtil.getFieldName(ProcessCategoryDTO::getParentCategory), LambdaUtil.getFieldName(ProcessCategoryDTO::getId)));
        Collection<String> idNotIn = getFilterValues(filters, LambdaUtil.getFieldName(ProcessCategoryDTO::getId), "notIn");
        String idNotEqual = getFilterValue(filters, LambdaUtil.getFieldName(ProcessCategoryDTO::getId), "notEqual");

        // 查询所有 流程分类
        List<OCProcessCategory> processCategories = processCategoryRepository.findAll();

        List<ProcessCategoryDTO> processCategoryDTOS = new ArrayList<>(
                TreeBuilder.entityToDTOTree(
                        processCategories,
                        OCProcessCategory::getParentCategory,
                        OCProcessCategory::getId,
                        ProcessCategoryMapper.INSTANCE::toDto,
                        ProcessCategoryDTO::getId,
                        ArrayList::new,
                        (processCategoryDTO, children) -> processCategoryDTO.setChildren(new ArrayList<>(children))
                )
        );

        // 查询条件
        if (parentId != null) {
            processCategoryDTOS = findByParentOcId(processCategoryDTOS, parentId);
        }
        List<Predicate<ProcessCategoryDTO>> predicates = CollUtil.list(false);
        if (StrUtil.isNotBlank(categoryName)) {
            predicates.add(processCategoryDTO -> StrUtil.contains(processCategoryDTO.getCategoryName(), categoryName));
        }
        if (StrUtil.isNotBlank(categoryNo)) {
            predicates.add(processCategoryDTO -> StrUtil.contains(processCategoryDTO.getCategoryNo(), categoryNo));
        }
        if (ObjectUtil.isNotEmpty(idNotIn)) {
            predicates.add(processCategoryDTO -> !CollUtil.contains(idNotIn, processCategoryDTO.getId()));
        }
        if(StrUtil.isNotBlank(idNotEqual)) {
            predicates.add(processCategoryDTO ->  StrUtil.equals(processCategoryDTO.getId(), idNotEqual));
        }

        // 过滤
        processCategoryDTOS = TreeBuilder.filterTreeNodes(
                processCategoryDTOS,
                ProcessCategoryDTO::getChildren,
                (processCategoryDTO, objects) -> processCategoryDTO.setChildren(new ArrayList<>(objects)),
                predicates
        );

        // 排序
        processCategoryDTOS = sortByCategoryNoAsc(processCategoryDTOS);

        // 内存中分页
        return pageInMemory(queryRequestDTO, processCategoryDTOS);
    }

    private String getFilterValue(List<FieldData> filters, String FieldName, String operator) {
        return CollUtil.emptyIfNull(filters)
                .stream()
                .filter(filter -> StrUtil.equals(filter.getFieldName(), FieldName))
                .filter(filter -> StrUtil.equalsIgnoreCase(filter.getOperator(), operator))
                .findFirst()
                .map(FieldData::getFieldValue)
                .map(Object::toString)
                .orElse(null);
    }

    private String getFilterValue(List<FieldData> filters, String FieldName) {
        return CollUtil.emptyIfNull(filters)
                .stream()
                .filter(filter -> StrUtil.equals(filter.getFieldName(), FieldName))
                .findFirst()
                .map(FieldData::getFieldValue)
                .map(Object::toString)
                .orElse(null);
    }

    private Collection<String> getFilterValues(List<FieldData> filters, String FieldName, String operator) {
        Object fieldValues = CollUtil.emptyIfNull(filters)
                .stream()
                .filter(filter -> StrUtil.equals(filter.getFieldName(), FieldName))
                .filter(filter -> StrUtil.equalsIgnoreCase(filter.getOperator(), operator))
                .findFirst()
                .map(FieldData::getFieldValue)
                .orElse(null);

        return (Collection<String>) fieldValues;
    }

    private OcResponse<QueryDataResponse<?>> pageInMemory(QueryRequestDTO queryRequestDTO, List<ProcessCategoryDTO> processCategoryDTOS) {
        OcResponse<QueryDataResponse<?>> response = new OcResponse<>();

        response.setData(page(queryRequestDTO.getPagination(), processCategoryDTOS));
        return response;
    }

    private static List<ProcessCategoryDTO> sortByCategoryNoAsc(List<ProcessCategoryDTO> processCategoryDTOS) {
        Comparator<ProcessCategoryDTO> comparator = ((o1, o2) -> StrUtil.compare(o1.getCategoryNo(), o2.getCategoryNo(), true));

        processCategoryDTOS = new ArrayList<>(TreeBuilder.sortTree(processCategoryDTOS, ProcessCategoryDTO::getChildren, comparator));
        return processCategoryDTOS;
    }

    private QueryDataResponse<?> page(Page pagination, List<ProcessCategoryDTO> processCategoryDTOS) {
        int start;
        int end;
        // 首页页码设置为1
        PageUtil.setOneAsFirstPageNo();
        if (pagination != null) {
            start = PageUtil.getStart(pagination.getCurrent(), pagination.getPageSize());
            end = PageUtil.getEnd(pagination.getCurrent(), pagination.getPageSize());
            pagination.setTotal(processCategoryDTOS.size());
        } else {
            start = PageUtil.getStart(1, Integer.MAX_VALUE);
            end = PageUtil.getEnd(1, Integer.MAX_VALUE);
        }


        QueryDataResponse<List<ProcessCategoryDTO>> queryDataResponse = new QueryDataResponse<>();

        queryDataResponse.setPagination(pagination);

        queryDataResponse.setData(CollUtil.sub(processCategoryDTOS, start, end));

        queryDataResponse.setAuthority(new ArrayList<>());

        return queryDataResponse;
    }


    private List<ProcessCategoryDTO> findByParentOcId(List<ProcessCategoryDTO> rootDto, String parentOcId) {
        if (CollUtil.isEmpty(rootDto) || StrUtil.isBlank(parentOcId)) {
            return new ArrayList<>();
        }
        ProcessCategoryDTO processCategoryDTO = rootDto.stream().filter(cate -> StrUtil.equals(cate.getId(), parentOcId)).findFirst().orElse(null);
        if (processCategoryDTO != null) {
            return processCategoryDTO.getChildren();
        }
        rootDto = new ArrayList<>(rootDto.stream().map(ProcessCategoryDTO::getChildren).filter(CollUtil::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList()));
        return findByParentOcId(rootDto, parentOcId);
    }

    @Transactional
    public OcResponse<EditDataResponse> save(SaveRequestDTO saveRequestDTO) {
        ProcessCategoryDTO processCategoryDTO = JSONUtil.toBean(JSONUtil.toJsonStr(saveRequestDTO.getData()), ProcessCategoryDTO.class);
        OCProcessCategory processCategory = ProcessCategoryMapper.INSTANCE.fromDtoIgnoreParentCategoryNo(processCategoryDTO);

        List<OCProcessCategory> existed = processCategoryRepository.findByCategoryNameEqualsAndIdNot(processCategoryDTO.getCategoryName(), processCategoryDTO.getId());
        if (CollUtil.isNotEmpty(existed)) {
            OcResponse<EditDataResponse> response = new OcResponse<>();
            response.setStatus("-1");
            response.setMsg(StrUtil.format("分类名称{}已经存在", processCategoryDTO.getCategoryName()));
            response.setError(response.getMsg());
            return response;
        }


        if (StrUtil.isNotBlank(processCategoryDTO.getId())) {
            OCProcessCategory existedCate = processCategoryRepository.findById(processCategoryDTO.getId()).get();

            processCategory = ProcessCategoryMapper.INSTANCE.mergeIgnoreParentCategoryNo(processCategory, existedCate);
        }
        // 设置分类编码
        processCategory.setCategoryNo(generateCategoryNo(processCategoryDTO));


        if (ObjectUtil.isNotNull(processCategoryDTO.getParentCategory()) && StrUtil.isNotBlank(processCategoryDTO.getParentCategory().getId())) {
            processCategory.setParentCategory(processCategoryRepository.findById(processCategoryDTO.getParentCategory().getId()).get());
            String parentId = processCategory.getParentCategory().getId();
            if (StrUtil.equals(parentId, processCategoryDTO.getId())) {
                OcResponse<EditDataResponse> response = new OcResponse<>();
                response.setStatus("-1");
                response.setMsg(StrUtil.format("父分类{}不能是自己", processCategoryDTO.getCategoryName()));
                response.setError(response.getMsg());
                return response;
            }
        }


        processCategory = processCategoryRepository.save(processCategory);

        OcResponse<EditDataResponse> response = new OcResponse<>();

        EditDataResponse editDataResponse = new EditDataResponse();

        editDataResponse.setData(ProcessCategoryMapper.INSTANCE.toDto(processCategory));

        response.setData(editDataResponse);

        return response;
    }

    private String generateCategoryNo(ProcessCategoryDTO processCategoryDTO) {
        String yyyyMMdd = DateUtil.format(new Date(), "yyyyMMdd");
        String categoryName = processCategoryDTO.getCategoryName();
        StringBuilder sb = new StringBuilder();
        for (char c : categoryName.toCharArray()) {
            if (PinyinUtil.isChinese(c)) {
                sb.append(Character.toUpperCase(PinyinUtil.getFirstLetter(c)));
            } else {
                sb.append(c);
            }
        }

        return yyyyMMdd + sb.toString();
    }

    @Transactional
    public OcResponse<EditDataResponse> get(GetRequestDTO getRequestDTO0, String id) {
        OCProcessCategory processCategory = processCategoryRepository.findById(id).orElse(null);


        EditDataResponse editDataResponse = new EditDataResponse();
        editDataResponse.setData(ProcessCategoryMapper.INSTANCE.toDto(processCategory));


        OcResponse<EditDataResponse> response = new OcResponse<>();
        response.setStatus("0");
        response.setData(editDataResponse);
        return response;
    }

    @Transactional
    public OcResponse<EditDataResponse> update(SaveRequestDTO saveRequestDTO, String id) {
        saveRequestDTO.getData().put(LambdaUtil.getFieldName(ProcessCategoryDTO::getId), id);

        return save(saveRequestDTO);
    }

    @Transactional
    public OcResponse<Void> delete(DeleteParamRequest deleteParamRequest) {
        OcResponse<Void> response = new OcResponse<>();
        List<String> entityInstIds = deleteParamRequest.getEntityInstIds();
        List<OCProcessCategory> categories = processCategoryRepository.findAllById(entityInstIds);
        Set<String> hasChildCates = processCategoryRepository.findByParentCategoryIn(categories)
                .stream()
                .map(OCProcessCategory::getParentCategory)
                .map(OCProcessCategory::getCategoryName)
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(hasChildCates)) {
            response.setStatus("-1");
            // 中文字符分隔 分类名称
            response.setMsg(StrUtil.format("{} 存在子分类，无法删除", StrUtil.join(CHINESE_DOT, hasChildCates)));
            response.setError(StrUtil.format("{} 存在子分类，无法删除", StrUtil.join(CHINESE_DOT, hasChildCates)));
            return response;
        }

        processCategoryRepository.deleteAll(categories);

        response.setStatus("0");
        response.setMsg("删除成功");

        return response;
    }
}
