package com.open_care.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.open_care.bpm.core.ProcessVariableUtil;
import com.open_care.bpm.dto.ProcessDefinitionMigrationRequestDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationResultDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.dto.ProcessInstQueryRequestDTO;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstanceQuery;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.migration.MigrationPlan;
import org.camunda.bpm.engine.migration.MigrationPlanBuilder;
import org.camunda.bpm.engine.migration.MigrationPlanExecutionBuilder;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程实例服务
 *
 * <AUTHOR>
 * @date 2025/5/15
 */
@Log4j2
@Service
public class ProcessInstanceService {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private TaskService taskService;

    /**
     * 分页查询流程实例
     *
     * @param requestDTO 查询请求
     * @return 流程实例列表和总数
     */
    public Pair<Long, List<ProcessInstDTO>> findProcessInstances(ProcessInstQueryRequestDTO requestDTO) {
        HistoricProcessInstanceQuery query = buildHistoricProcessInstanceQuery(requestDTO);

        // 获取总数
        long count = query.count();

        // 如果数量为0，直接返回空列表
        if (count == 0) {
            return Pair.of(0L, new ArrayList<>());
        }

        // 设置分页
        int firstResult = 0;
        int maxResults = Integer.MAX_VALUE;

        if (Objects.nonNull(requestDTO.getPagination())) {
            firstResult = (requestDTO.getPagination().getCurrent() - 1) * requestDTO.getPagination().getPageSize();
            maxResults = requestDTO.getPagination().getPageSize();
        }

        // 执行查询
        List<HistoricProcessInstance> instances = query
                .orderByProcessInstanceStartTime()
                .desc()
                .listPage(firstResult, maxResults);

        // 转换为DTO
        List<ProcessInstDTO> dtoList = convertToProcessInstDTOList(instances);

        return Pair.of(count, dtoList);
    }

    /**
     * 根据查询条件构建查询对象
     */
    private HistoricProcessInstanceQuery buildHistoricProcessInstanceQuery(ProcessInstQueryRequestDTO requestDTO) {
        HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery();

        // 按流程定义Key查询
        if (CollUtil.isNotEmpty(requestDTO.getProcessDefKeys())) {
            query.processDefinitionKeyIn(requestDTO.getProcessDefKeys().toArray(new String[0]));
        }

        // 按流程定义ID查询
        if (CollUtil.isNotEmpty(requestDTO.getProcessDefIds())) {
            query = query.or();
            for (String processDefId : requestDTO.getProcessDefIds()) {
                query.processDefinitionId(processDefId);
            }
            query = query.endOr();
        }

        // 按流程实例ID查询
        if (CollUtil.isNotEmpty(requestDTO.getProcessInstIds())) {
            query = query.processInstanceIds(CollUtil.newHashSet(false, requestDTO.getProcessInstIds()));
        }

        if (CollUtil.isNotEmpty(requestDTO.getCommitters())) {
            query = query.or();
            for (String committer : requestDTO.getCommitters()) {
                query.variableValueEquals(ProcessVariableUtil.VARIABLE_COMMITTER, committer);
            }
            query = query.endOr();
        }

        // 根据流程是否结束进行筛选
        if (Objects.nonNull(requestDTO.getEnded())) {
            if (requestDTO.getEnded()) {
                // 查询已结束的流程
                query.finished();
            } else {
                // 查询未结束的流程
                query.unfinished();
            }
        }

        return query;
    }

    /**
     * 将Camunda流程实例转换为DTO对象
     */
    private List<ProcessInstDTO> convertToProcessInstDTOList(List<HistoricProcessInstance> instances) {
        if (CollUtil.isEmpty(instances)) {
            return new ArrayList<>();
        }

        return instances.stream()
                .map(this::convertToProcessInstDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将单个Camunda流程实例转换为DTO
     */
    private ProcessInstDTO convertToProcessInstDTO(HistoricProcessInstance instance) {
        ProcessInstDTO dto = new ProcessInstDTO();

        // 设置基本属性
        dto.setId(instance.getId());
        dto.setBusinessKey(instance.getBusinessKey());
        dto.setProcessTitle(instance.getProcessDefinitionName());

        dto.setEndTime(instance.getEndTime());
        dto.setEnded(instance.getEndTime() != null);

        // 获取流程变量
        Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(instance.getId())
                .list()
                .stream()
                .filter(variable -> ObjectUtil.isNotNull(variable.getName()))
                .filter(variable -> ObjectUtil.isNotNull(variable.getValue()))
                .collect(Collectors.toMap(
                        HistoricVariableInstance::getName,
                        HistoricVariableInstance::getValue,
                        (v1, v2) -> v2 // 如果有重复的key，取第二个值
                ));
        dto.setVariables(variables);

        // 使用ProcessVariableUtil从变量中提取属性
        dto.setRemark(ProcessVariableUtil.getRemarkFromVariables(variables));
        dto.setClassName(ProcessVariableUtil.getEntityNameFromVariables(variables));
        dto.setEntityInstId(ProcessVariableUtil.getEntityInstIdFromVariables(variables));
        dto.setCommitterName(ProcessVariableUtil.getCommitterNameFromVariables(variables));
        dto.setLastCommitterName(ProcessVariableUtil.getCommitterNameFromVariables(variables));
        dto.setCommitter(ProcessVariableUtil.getCommitterFromVariables(variables));
        dto.setCommitTime(ProcessVariableUtil.getCommitTimeFromVariables(variables));

        // 提取附件信息
        dto.setAttachments(ProcessVariableUtil.getAttachmentsFromVariables(variables));

        // 检查流程是否挂起
        try {
            ProcessInstance runningInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(instance.getId())
                    .singleResult();
            dto.setSuspended(runningInstance != null && runningInstance.isSuspended());
        } catch (Exception e) {
            log.warn("检查流程是否挂起时出错: {}", instance.getId(), e);
            dto.setSuspended(false);
        }

        // 设置流程信息
        ProcessInfoDTO processInfo = createProcessInfoDTO(instance);
        dto.setProcessInfo(processInfo);

        return dto;
    }

    /**
     * 创建流程信息DTO
     */
    private ProcessInfoDTO createProcessInfoDTO(HistoricProcessInstance instance) {
        ProcessInfoDTO processInfo = new ProcessInfoDTO();
        processInfo.setId(instance.getProcessDefinitionId());

        // 查询流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(instance.getProcessDefinitionId())
                .singleResult();

        if (processDefinition != null) {
            processInfo.setName(processDefinition.getName());
            processInfo.setProcessDefinitionName(processDefinition.getName());
            processInfo.setProcessDefinitionKey(processDefinition.getKey());
            processInfo.setProcessDefinitionId(processDefinition.getId());
            processInfo.setExtensionProperties(ExtensionPropertyUtils.getAllPropertiesAsMap(processDefinition, repositoryService));

            // 可以在这里添加更多流程定义相关信息
            // 例如：从BPMN解析用户任务节点等
        }

        return processInfo;
    }

    /**
     * 迁移流程定义实例
     * 支持将某一个流程key的某个版本或者这个版本之前的所有版本迁移到最新版本或者某个具体的版本
     *
     * @param requestDTO 迁移请求参数
     * @return 迁移结果
     */
    @Transactional
    public ProcessDefinitionMigrationResultDTO migrateProcessDefinitionInstances(ProcessDefinitionMigrationRequestDTO requestDTO) {
        Set<String> processInstIds = requestDTO.getProcessInstIds();

        String processDefinitionKey = requestDTO.getProcessDefinitionKey();
        Integer sourceVersion = requestDTO.getSourceVersion();
        Boolean migrateAllPreviousVersions = requestDTO.getMigrateAllPreviousVersions();
        Integer targetVersion = requestDTO.getTargetVersion();


        // 构建结果对象
        ProcessDefinitionMigrationResultDTO resultDTO = ProcessDefinitionMigrationResultDTO.builder()
                .processDefinitionKey(processDefinitionKey)
                .successCount(0)
                .failureCount(0)
                .build();
        if (ObjectUtil.isNotNull(targetVersion) && targetVersion <= 0) {
            log.error("目标流程定义版本 <=0，直接返回");
            return resultDTO;
        }


        if (CollUtil.isNotEmpty(processInstIds)) {
            migrateProcessFromProcessInst(resultDTO, processInstIds, processDefinitionKey, targetVersion);

        }

        if (ObjectUtil.isNotNull(sourceVersion) && sourceVersion > 0 && (ObjectUtil.isNull(targetVersion) || targetVersion > 0)) {
            migrateProcessBySameDefinitionKey(resultDTO, processDefinitionKey, targetVersion, sourceVersion, migrateAllPreviousVersions);
        }

        return resultDTO;
    }

    private void migrateProcessFromProcessInst(ProcessDefinitionMigrationResultDTO resultDTO, Set<String> processInstIds, String targetProcessDefinitionKey, Integer targetVersion) {
        List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstIds).list();
        Map<String, List<ProcessInstance>> processDefIdToInstances = processInstances.stream().collect(Collectors.groupingBy(ProcessInstance::getProcessDefinitionId));

        Map<String, ProcessDefinition> processDefIdMap = repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionIdIn(processDefIdToInstances.keySet().toArray(new String[0]))
                .list()
                .stream()
                .collect(Collectors.toMap(ProcessDefinition::getId, Function.identity()));

        for (Map.Entry<String, List<ProcessInstance>> processDefIdToInstancesEntry : processDefIdToInstances.entrySet()) {
            String processDefId = processDefIdToInstancesEntry.getKey();
            ProcessDefinition sourcesProcessDefinition = processDefIdMap.get(processDefId);
            ProcessDefinition targetProcessDefinition = findTargetProcessDefinition(targetProcessDefinitionKey, targetVersion);

            migrateFromProcessInstances(resultDTO, sourcesProcessDefinition, processDefIdToInstancesEntry.getValue(), targetProcessDefinition);
        }

    }


    private ProcessDefinitionMigrationResultDTO migrateProcessBySameDefinitionKey(
            ProcessDefinitionMigrationResultDTO resultDTO,
            String targetProcessDefinitionKye,
            Integer targetVersion,
            Integer sourceVersion,
            Boolean migrateAllPreviousVersions) {
        try {
            // 1. 获取目标流程定义
            ProcessDefinition targetProcessDefinition = findTargetProcessDefinition(targetProcessDefinitionKye, targetVersion);
            resultDTO.setTargetProcessDefinitionId(targetProcessDefinition.getId());

            // 2. 获取源流程定义列表
            List<ProcessDefinition> sourceProcessDefinitions = findSourceProcessDefinitions(
                    targetProcessDefinitionKye, sourceVersion, migrateAllPreviousVersions, targetProcessDefinition);

            if (sourceProcessDefinitions.isEmpty()) {
                log.info("没有找到需要迁移的流程定义实例: {}", targetProcessDefinitionKye);
                return resultDTO;
            }

            // 记录源流程定义ID
            List<String> sourceProcessDefinitionIds = sourceProcessDefinitions.stream()
                    .map(ProcessDefinition::getId)
                    .collect(Collectors.toList());
            resultDTO.setSourceProcessDefinitionIds(sourceProcessDefinitionIds);

            // 3. 对每个源流程定义执行迁移
            migrateProcessDefinitions(sourceProcessDefinitions, targetProcessDefinition, resultDTO);

            return resultDTO;

        } catch (Exception e) {
            log.error("迁移流程定义实例时发生错误: {}", targetProcessDefinitionKye, e);
            resultDTO.setFailureCount(1);
            resultDTO.getFailureReasons().add("迁移过程中发生错误: " + e.getMessage());
            return resultDTO;
        }
    }

    /**
     * 查找目标流程定义
     *
     * @param targetVersion 目标版本，如果为null则使用最新版本
     * @return 目标流程定义
     */
    private ProcessDefinition findTargetProcessDefinition(String targetrocessDefinitionKey, Integer targetVersion) {
        ProcessDefinition targetProcessDefinition;
        if (targetVersion == null) {
            // 如果未指定目标版本，则使用最新版本
            targetProcessDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(targetrocessDefinitionKey)
                    .latestVersion()
                    .singleResult();
        } else {
            // 使用指定的目标版本
            targetProcessDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(targetrocessDefinitionKey)
                    .processDefinitionVersion(targetVersion)
                    .singleResult();
        }

        if (targetProcessDefinition == null) {
            throw new IllegalArgumentException("目标流程定义不存在: " + targetrocessDefinitionKey +
                    (targetVersion != null ? ", 版本: " + targetVersion : ""));
        }

        return targetProcessDefinition;
    }

    /**
     * 查找源流程定义列表
     *
     * @param sourceVersion              源版本
     * @param migrateAllPreviousVersions 是否迁移所有之前的版本
     * @param targetProcessDefinition    目标流程定义
     * @return 源流程定义列表
     */
    private List<ProcessDefinition> findSourceProcessDefinitions(
            String tprocessDefinitionKey,
            Integer sourceVersion,
            Boolean migrateAllPreviousVersions,
            ProcessDefinition targetProcessDefinition) {

        List<ProcessDefinition> sourceProcessDefinitions = new ArrayList<>();

        if (sourceVersion != null) {
            if (Boolean.TRUE.equals(migrateAllPreviousVersions)) {
                // 迁移指定版本及之前的所有版本
                List<ProcessDefinition> allVersions = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionKey(tprocessDefinitionKey)
                        .orderByProcessDefinitionVersion().asc()
                        .list();

                for (ProcessDefinition pd : allVersions) {
                    if (pd.getVersion() <= sourceVersion && pd.getVersion() != targetProcessDefinition.getVersion()) {
                        sourceProcessDefinitions.add(pd);
                    }
                }
            } else {
                // 只迁移指定版本
                ProcessDefinition sourcePd = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionKey(tprocessDefinitionKey)
                        .processDefinitionVersion(sourceVersion)
                        .singleResult();

                if (sourcePd != null && !sourcePd.getId().equals(targetProcessDefinition.getId())) {
                    sourceProcessDefinitions.add(sourcePd);
                }
            }
        } else {
            // 如果未指定源版本，则迁移除目标版本外的所有版本
            List<ProcessDefinition> allVersions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(tprocessDefinitionKey)
                    .list();

            for (ProcessDefinition pd : allVersions) {
                if (!pd.getId().equals(targetProcessDefinition.getId())) {
                    sourceProcessDefinitions.add(pd);
                }
            }
        }

        return sourceProcessDefinitions;
    }

    /**
     * 迁移流程定义
     *
     * @param sourceProcessDefinitions 源流程定义列表
     * @param targetProcessDefinition  目标流程定义
     * @param resultDTO                结果对象
     */
    private void migrateProcessDefinitions(
            List<ProcessDefinition> sourceProcessDefinitions,
            ProcessDefinition targetProcessDefinition,
            ProcessDefinitionMigrationResultDTO resultDTO) {

        for (ProcessDefinition sourceProcessDefinition : sourceProcessDefinitions) {
            try {
                migrateProcessDefinition(sourceProcessDefinition, targetProcessDefinition, resultDTO);
            } catch (Exception e) {
                log.error("迁移流程定义 {} 版本 {} 的实例时发生错误",
                        sourceProcessDefinition.getKey(), sourceProcessDefinition.getVersion(), e);

                // 记录失败信息
                resultDTO.setFailureCount(resultDTO.getFailureCount() + 1);
                resultDTO.getFailureReasons().add(String.format(
                        "迁移流程定义 %s 版本 %s 失败: %s",
                        sourceProcessDefinition.getKey(),
                        sourceProcessDefinition.getVersion(),
                        e.getMessage()));
            }
        }
    }

    /**
     * 迁移单个流程定义
     *
     * @param sourceProcessDefinition 源流程定义
     * @param targetProcessDefinition 目标流程定义
     * @param resultDTO               结果对象
     */
    private void migrateProcessDefinition(
            ProcessDefinition sourceProcessDefinition,
            ProcessDefinition targetProcessDefinition,
            ProcessDefinitionMigrationResultDTO resultDTO) {

        // 查询该流程定义下的活动实例
        List<ProcessInstance> activeInstances = runtimeService.createProcessInstanceQuery()
                .processDefinitionId(sourceProcessDefinition.getId())
                .active()
                .list();

        if (activeInstances.isEmpty()) {
            log.info("流程定义 {} 版本 {} 没有活动的流程实例",
                    sourceProcessDefinition.getKey(), sourceProcessDefinition.getVersion());
            return;
        }

        migrateFromProcessInstances(resultDTO, sourceProcessDefinition, activeInstances, targetProcessDefinition);
    }

    private void migrateFromProcessInstances(
            ProcessDefinitionMigrationResultDTO resultDTO,
            ProcessDefinition sourceProcessDefinition,
            List<ProcessInstance> activeInstances,
            ProcessDefinition targetProcessDefinition
    ) {
        // 创建迁移计划
        MigrationPlanBuilder migrationPlanBuilder = runtimeService
                .createMigrationPlan(sourceProcessDefinition.getId(), targetProcessDefinition.getId())
                .mapEqualActivities(); // 自动映射相同名称的活动

        MigrationPlan migrationPlan = migrationPlanBuilder.build();

        // 获取实例ID列表
        List<String> processInstanceIds = activeInstances.stream()
                .map(ProcessInstance::getId)
                .collect(Collectors.toList());

        // 执行迁移
        MigrationPlanExecutionBuilder executionBuilder = runtimeService
                .newMigration(migrationPlan)
                .processInstanceIds(processInstanceIds);

        executionBuilder.execute();

        // 更新成功计数
        resultDTO.setSuccessCount(resultDTO.getSuccessCount() + processInstanceIds.size());

        log.info("成功迁移流程定义 {} 版本 {} 的 {} 个实例到版本 {}",
                sourceProcessDefinition.getKey(),
                sourceProcessDefinition.getVersion(),
                processInstanceIds.size(),
                targetProcessDefinition.getVersion());
    }
}