package com.open_care.bpm.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.api.common.ServiceException;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.bpm.dto.ProcessDefinitionMigrationRequestDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationResultDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.SaveProcessDefBodyDto;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessRequestDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessResponseDTO;
import com.open_care.bpm.entity.OCProcessdefinition;
import com.open_care.bpm.repository.ProcessDefRepository;
import com.open_care.bpm.repository.ProcessDefSaveLogRepository;
import com.open_care.bpm.service.ProcessDefinitionService;
import com.open_care.bpm.service.ProcessEngineService;
import com.open_care.bpm.service.ProcessInstanceService;
import com.open_care.bpm.service.ProcessService;
import com.open_care.bpm.service.bo.ProcessInfoAndPathName;
import com.open_care.util.json.JsonConverter;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 流程定义服务实现类
 *
 * <AUTHOR> by Claude
 * @date 2025/5/22
 */
@Service
@Log4j2
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {

    private static final String STATUS_SUCCESS = "0";
    private static final String DATE_PATTERN = "yyyy_MM_dd_HH_mm_SSS";
    private static final String TIME_SUFFIX_PATTERN = "yyyyMMddHHmmss";
    private static final int MAX_CODE_ATTEMPTS = 99;
    private static final String ID_SEPARATOR = "_";

    @Autowired
    private ProcessService processService;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private ProcessEngineService processEngineService;

    @Autowired
    private ProcessDefRepository processDefRepository;

    @Autowired
    private ProcessDefSaveLogRepository saveLogRepository;

    @Autowired
    private JsonConverter jsonConverter;

    @Autowired
    private RepositoryService repositoryService;

    /**
     * 保存、部署并迁移流程
     *
     * @param requestDTO 保存部署迁移请求DTO
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveAndDeployThenMigrateProcessResponseDTO saveAndDeployThenMigrateProcess(SaveAndDeployThenMigrateProcessRequestDTO requestDTO) {
        SaveAndDeployThenMigrateProcessResponseDTO responseDTO = new SaveAndDeployThenMigrateProcessResponseDTO();

        try {
            // 1. 校验请求参数
            if (!validateRequestParams(requestDTO, responseDTO)) {
                return responseDTO;
            }

            // 2. 保存流程定义
            ProcessSaveResult saveResult = saveProcessDefinition(requestDTO, responseDTO);
            if (!saveResult.isSuccess()) {
                return responseDTO;
            }

            ProcessInfoDTO processInfo = saveResult.getProcessInfo();
            String newProcessDefId = saveResult.getNewProcessDefId();
            String version = saveResult.getVersion();
            responseDTO.setProcessInfo(processInfo);

            // 3. 部署流程
            deployProcess(newProcessDefId, version);


            // 4. 查找部署的流程定义
            ProcessDefinition processDefinition = findLatestProcessDefinition(processInfo.getId());
            if (ObjectUtil.isNull(processDefinition)) {
                return handleDeploymentNoDefinitionFound(processInfo.getId(), responseDTO);
            }

            // 5. 迁移流程实例到新版本
            return migrateProcessInstances(requestDTO, processDefinition, processInfo, newProcessDefId, responseDTO);

        } catch (Exception e) {
            return handleGenericException(e, responseDTO);
        }
    }

    /**
     * 校验请求参数
     *
     * @param requestDTO  请求DTO
     * @param responseDTO 响应DTO
     * @return 校验是否通过
     */
    private boolean validateRequestParams(SaveAndDeployThenMigrateProcessRequestDTO requestDTO,
                                          SaveAndDeployThenMigrateProcessResponseDTO responseDTO) {
        String data = requestDTO.getData();
        String type = requestDTO.getType();

        if (ObjectUtil.isNull(data) || ObjectUtil.isNull(type)) {
            log.error("请求参数错误：数据或类型为空");
            responseDTO.setStatus("-1");
            responseDTO.setError("请求参数错误：数据或类型为空");
            return false;
        }
        return true;
    }

    /**
     * 保存流程定义
     *
     * @param requestDTO  请求DTO
     * @param responseDTO 响应DTO
     * @return 保存结果
     */
    private ProcessSaveResult saveProcessDefinition(SaveAndDeployThenMigrateProcessRequestDTO requestDTO,
                                                    SaveAndDeployThenMigrateProcessResponseDTO responseDTO) {
        ProcessSaveResult result = new ProcessSaveResult();
        String ocProcessDefId = UUID.randomUUID().toString();

        // 1. 修改XML中的流程名称和流程key，添加时间戳和序号后缀
        modifyProcessXml(requestDTO);

        // 2. 创建保存请求体
        SaveProcessDefBodyDto saveDto = new SaveProcessDefBodyDto();
        saveDto.setData(requestDTO.getData());
        saveDto.setType(requestDTO.getType());
        saveDto.setProcessName(requestDTO.getProcessName());

        // 3. 生成带时间后缀的流程定义ID

        // 4. 保存流程定义
        ProcessInfoAndPathName processInfoPathName = processService.doSaveOCProcessDefInfo(ocProcessDefId, jsonConverter.toJson(saveDto));


        ProcessInfoDTO processInfo = processInfoPathName.processInfo();
        result.setProcessInfo(processInfo);

        // 5. 获取保存的最新版本号
        String version = saveLogRepository.getMaxVersionByOcprocessdefinitionId(ocProcessDefId);
        result.setVersion(version);

        result.setSuccess(true);
        result.setNewProcessDefId(ocProcessDefId);
        return result;
    }

    /**
     * 部署流程
     *
     * @param newProcessId 流程ID
     * @param version      版本号
     * @return 部署是否成功
     */
    private void deployProcess(String newProcessId, String version) throws Exception {
        processService.doDeployProcessDefByOcProcessDefId(newProcessId, version);
    }

    /**
     * 处理部署后无法找到流程定义的情况
     *
     * @param processId   流程ID
     * @param responseDTO 响应DTO
     * @return 响应DTO
     */
    private SaveAndDeployThenMigrateProcessResponseDTO handleDeploymentNoDefinitionFound(
            String processId, SaveAndDeployThenMigrateProcessResponseDTO responseDTO) {
        log.warn("部署成功但无法获取流程定义信息: {}", processId);
        responseDTO.setStatus("1");
        responseDTO.setError("部署成功但无法获取流程定义信息");
        return responseDTO;
    }

    /**
     * 迁移流程实例到新版本
     *
     * @param requestDTO        请求DTO
     * @param processDefinition 流程定义
     * @param processInfo       流程信息
     * @param newProcessId      新流程ID
     * @param responseDTO       响应DTO
     * @return 响应DTO
     */
    private SaveAndDeployThenMigrateProcessResponseDTO migrateProcessInstances(
            SaveAndDeployThenMigrateProcessRequestDTO requestDTO,
            ProcessDefinition processDefinition,
            ProcessInfoDTO processInfo,
            String newProcessId,
            SaveAndDeployThenMigrateProcessResponseDTO responseDTO) {


        // 判断是否有需要迁移的流程实例
        if (ObjectUtil.isNotEmpty(requestDTO.getMigrateProcessInstIds())) {
            ProcessDefinitionMigrationResultDTO migrationResult = performMigration(
                    requestDTO.getMigrateProcessInstIds(),
                    processDefinition.getKey(),
                    processDefinition.getVersion());

            processInfo.setMigrationResult(migrationResult);

            log.info("流程 {} 迁移完成，成功: {}，失败: {}",
                    newProcessId,
                    migrationResult.getSuccessCount(),
                    migrationResult.getFailureCount());
        }

        responseDTO.setStatus("0");
        responseDTO.setProcessInfo(processInfo);
        responseDTO.setMsg(StrUtil.format("流程操作成功完成，流程ID: {}", newProcessId));
        return responseDTO;


    }

    /**
     * 执行流程实例迁移
     *
     * @param processInstIds       需要迁移的流程实例ID列表
     * @param processDefinitionKey 流程定义Key
     * @param targetVersion        目标版本
     * @return 迁移结果
     */
    private ProcessDefinitionMigrationResultDTO performMigration(
            Set<String> processInstIds, String processDefinitionKey, Integer targetVersion) {

        ProcessDefinitionMigrationRequestDTO migrationRequest = ProcessDefinitionMigrationRequestDTO.builder()
                .processDefinitionKey(processDefinitionKey)
                .targetVersion(targetVersion)
                .processInstIds(processInstIds)
                .build();

        return processInstanceService.migrateProcessDefinitionInstances(migrationRequest);
    }

    /**
     * 处理通用异常
     *
     * @param e           异常
     * @param responseDTO 响应DTO
     * @return 响应DTO
     */
    private SaveAndDeployThenMigrateProcessResponseDTO handleGenericException(
            Exception e, SaveAndDeployThenMigrateProcessResponseDTO responseDTO) {
        log.error("流程保存、部署、迁移操作异常", e);
        responseDTO.setStatus("1");
        responseDTO.setError("操作异常: " + e.getMessage());
        return responseDTO;
    }

    /**
     * 流程保存结果
     */
    private static class ProcessSaveResult {
        private boolean success;
        private ProcessInfoDTO processInfo;
        private String newProcessDefId;
        private String version;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public ProcessInfoDTO getProcessInfo() {
            return processInfo;
        }

        public void setProcessInfo(ProcessInfoDTO processInfo) {
            this.processInfo = processInfo;
        }

        public String getNewProcessDefId() {
            return newProcessDefId;
        }

        public void setNewProcessDefId(String newProcessDefId) {
            this.newProcessDefId = newProcessDefId;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }

    /**
     * 修改流程XML中的流程名称和流程key，添加时间戳和序号后缀
     * 如果已经在数据库中存在相同前缀的流程，则序号在已有最大序号的基础上+1
     *
     * @return 修改后的XML数据
     */
    private void modifyProcessXml(SaveAndDeployThenMigrateProcessRequestDTO requestDTO) {
        if (!"bpmn".equals(requestDTO.getType()) && !"dmn".equals(requestDTO.getType())) {
            // 默认不修改
            return;
        }

        if ("bpmn".equals(requestDTO.getType())) {
            modifyBpmnXml(requestDTO);
        }
    }

    /**
     * 修改BPMN XML数据
     *
     * @return 修改后的XML数据
     */
    private void modifyBpmnXml(SaveAndDeployThenMigrateProcessRequestDTO requestDTO) {
        try (
                ByteArrayInputStream inputStream = new ByteArrayInputStream(requestDTO.getData().getBytes(StandardCharsets.UTF_8));
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ) {
            BpmnModelInstance modelInstance = Bpmn.readModelFromStream(inputStream);

            // 生成时间后缀
            String timeSuffix = LocalDateTime.now().format(DateTimeFormatter.ofPattern(TIME_SUFFIX_PATTERN));

            // 修改流程ID和名称
            Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
            if (processes.isEmpty()) {
                log.warn("未找到流程定义，保持原XML不变");
                return;
            }

            Process process = processes.iterator().next();

            // 保存原始值用于日志
            String originalId = process.getId();
            String originalName = process.getName();

            // 查找数据库中相同前缀的流程，获取最大序号
            int sequenceNumber = findMaxSequenceNumber(originalId, timeSuffix);
            String sequence = String.format("%02d", sequenceNumber);
            String suffix = timeSuffix + "_" + sequence;

            // 修改流程ID和名称
            String newId = originalId + "_" + suffix;
            String newName = originalName + "_" + suffix;

            process.setId(newId);
            process.setName(newName);

            log.info("流程ID从 '{}' 修改为 '{}'", originalId, newId);
            log.info("流程名称从 '{}' 修改为 '{}'", originalName, newName);

            // 将修改后的模型写回到字符串

            Bpmn.writeModelToStream(outputStream, modelInstance);

            requestDTO.setData(outputStream.toString(StandardCharsets.UTF_8.name()));
            requestDTO.setProcessName(newName);
        } catch (Exception e) {
            log.error("修改流程XML失败", e);
            throw new ServiceException("修改流程XML失败: " + e.getMessage());
        }
    }


    /**
     * 查找数据库中与指定前缀匹配的最大序号
     *
     * @param originalId 原始流程ID
     * @param timeSuffix 时间后缀
     * @return 最大序号+1，如果不存在则返回1
     */
    private int findMaxSequenceNumber(String originalId, String timeSuffix) {
        int maxSequence = 0;
        String prefixPattern = originalId + "_" + timeSuffix + "_";

        try {
            maxSequence = Math.max(maxSequence, findMaxSequenceFromProcessDefinition(originalId, prefixPattern));
            maxSequence = Math.max(maxSequence, findMaxSequenceFromCustomDefinition(prefixPattern));
        } catch (Exception e) {
            log.warn("查询流程定义序号时发生错误，将使用默认序号1", e);
        }

        return maxSequence + 1;
    }

    /**
     * 从Camunda引擎的流程定义中查找最大序号
     *
     * @param originalId    原始流程ID
     * @param prefixPattern 前缀模式
     * @return 找到的最大序号，如果没找到则返回0
     */
    private int findMaxSequenceFromProcessDefinition(String originalId, String prefixPattern) {
        int maxSequence = 0;
        // 查询Camunda引擎的流程定义
        List<ProcessDefinition> processDefinitions = repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionKeyLike(originalId + "\\_%")
                .list();

        // 查找特定前缀的ID并提取序号
        for (ProcessDefinition processDefinition : processDefinitions) {
            String processId = processDefinition.getKey();
            if (ObjectUtil.isNull(processId) || !processId.startsWith(prefixPattern)) {
                continue;
            }

            try {
                String sequencePart = processId.substring(prefixPattern.length());
                int sequence = Integer.parseInt(sequencePart);
                maxSequence = Math.max(maxSequence, sequence);
            } catch (NumberFormatException | IndexOutOfBoundsException e) {
                // 跳过无法解析为数字的序号部分
                log.debug("无法从流程ID提取序号: {}", processId);
            }
        }

        return maxSequence;
    }

    /**
     * 从自定义流程定义表中查找最大序号
     *
     * @param prefixPattern 前缀模式
     * @return 找到的最大序号，如果没找到则返回0
     */
    private int findMaxSequenceFromCustomDefinition(String prefixPattern) {
        int maxSequence = 0;
        // 查询自定义流程定义表
        List<OCProcessdefinition> customProcessDefinitions = processDefRepository.findAll();

        for (OCProcessdefinition processDefinition : customProcessDefinitions) {
            String procDefId = processDefinition.getProcDefId();
            if (ObjectUtil.isNull(procDefId) || !procDefId.contains(prefixPattern)) {
                continue;
            }

            try {
                String sequencePart = procDefId.substring(procDefId.lastIndexOf("_") + 1);
                int sequence = Integer.parseInt(sequencePart);
                maxSequence = Math.max(maxSequence, sequence);
            } catch (NumberFormatException | IndexOutOfBoundsException e) {
                // 跳过无法解析为数字的序号部分
                log.debug("无法从流程ID提取序号: {}", procDefId);
            }
        }

        return maxSequence;
    }

    /**
     * 生成带时间后缀的流程定义ID
     *
     * @param originalId 原始流程ID
     * @return 带时间后缀的流程ID
     */
    private String generateProcessIdWithTimeSuffix(String originalId) {
        String timeStr = generateTimeString();
        return findAvailableProcessId(originalId, timeStr);
    }

    /**
     * 生成时间字符串
     *
     * @return 格式化的时间字符串
     */
    private String generateTimeString() {
        LocalDateTime now = LocalDateTime.now();
        return now.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
    }

    /**
     * 寻找可用的流程ID
     *
     * @param originalId 原始流程ID
     * @param timeStr    时间字符串
     * @return 可用的流程ID
     */
    private String findAvailableProcessId(String originalId, String timeStr) {
        AtomicInteger counter = new AtomicInteger(1);
        String newId;

        do {
            String code = String.format("%02d", counter.getAndIncrement());
            newId = buildProcessId(originalId, timeStr, code);

            if (counter.get() > MAX_CODE_ATTEMPTS) {
                // 如果编码都用完了，改用毫秒级时间戳加随机数
                newId = buildFallbackProcessId(originalId);
                break;
            }
        } while (isProcessIdExists(newId));

        return newId;
    }

    /**
     * 构建流程ID
     *
     * @param originalId 原始流程ID
     * @param timeStr    时间字符串
     * @param code       序号代码
     * @return 构建的流程ID
     */
    private String buildProcessId(String originalId, String timeStr, String code) {
        return originalId + ID_SEPARATOR + timeStr + ID_SEPARATOR + code;
    }

    /**
     * 当常规ID生成方式达到上限时，构建备用的流程ID
     *
     * @param originalId 原始流程ID
     * @return 备用流程ID
     */
    private String buildFallbackProcessId(String originalId) {
        return originalId + ID_SEPARATOR + System.currentTimeMillis() + ID_SEPARATOR + new Random().nextInt(100);
    }

    /**
     * 检查流程ID是否已存在
     *
     * @param processId 流程ID
     * @return 是否存在
     */
    private boolean isProcessIdExists(String processId) {
        return processDefRepository.findById(processId).isPresent();
    }

    /**
     * 查找最新版本的流程定义
     *
     * @param processDefinitionKey 流程定义Key
     * @return 流程定义对象
     */
    private ProcessDefinition findLatestProcessDefinition(String processDefinitionKey) {
        return repositoryService
                .createProcessDefinitionQuery()
                .processDefinitionKey(processDefinitionKey)
                .latestVersion()
                .singleResult();
    }
} 