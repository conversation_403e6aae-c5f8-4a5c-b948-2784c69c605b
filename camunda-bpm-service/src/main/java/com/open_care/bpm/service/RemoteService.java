/**
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.service;

import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.client.MessageRemoteService;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.service.CamundaExtensionService;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.camunda.properties.TaskEventProperties;
import com.open_care.util.json.JsonConverter;
import jakarta.annotation.PostConstruct;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClientFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.Objects;

@Service
@Log4j2
public class RemoteService {

    public static RemoteService INSTANCE;

    @Value("${messageServer.url}")
    public String urlMessageServer;

    @Autowired
    TaskService taskService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    ConfigurationRemoteService configurationRemoteService;

    @Autowired
    MessageRemoteService messageRemoteService;

    @Autowired
    private JsonConverter jsonConverter;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private FeignClientFactory factoryClientFactory;

    @Autowired
    private TaskEventProperties taskEventProperties;

    /**
     * 构造完成后设置 INSTANCE 值
     */
    @PostConstruct
    public void init() {
        INSTANCE = this;
    }

    /**
     * 创建通用的HTTP头信息
     *
     * @param preferReturn 是否设置prefer: return=representation头
     * @return HTTP头信息
     */
    private HttpHeaders createHeaders(boolean preferReturn) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        if (preferReturn) {
            headers.set("Prefer", "return=representation");
        }

        return headers;
    }

    /**
     * 发送消息模板
     *
     * @param messageTemplateId 消息模板ID
     * @return 响应结果
     */
//    public String messageSend(String messageTemplateId) {
//        try {
//            Map<String, String> uriVariables = new HashMap<>();
//            uriVariables.put("id", messageTemplateId);
//
//            HttpHeaders headers = createHeaders(true);
//            HttpEntity<String> entity = new HttpEntity<>(headers);
//
//            ResponseEntity<String> response = restTemplate.exchange(
//                    urlMessageServer + "/sendMessageByConfigId/{id}",
//                    HttpMethod.GET,
//                    entity,
//                    String.class,
//                    uriVariables
//            );
//
//            return response.getBody();
//        } catch (RestClientException e) {
//            log.error("发送消息模板失败: {}", messageTemplateId, e);
//            return null;
//        }
//    }

//    /**
//     * 发送消息模板给指定用户
//     *
//     * @param messageTemplateId 消息模板ID
//     * @param users             用户信息JSON
//     * @return 响应结果
//     */
//    public String messageSend(String messageTemplateId, String users) {
//        try {
//            return messageRemoteService.sendMessageByConfigIdAndUsers(messageTemplateId, users).toString();
//        } catch (Exception e) {
//            log.error("message send error:{}users({}){}", messageTemplateId, users, e.getMessage());
//            return null;
//        }
//    }



//    /**
//     * 异步执行任务监听
//     *
//     * @param taskDTO  任务DTO
//     * @param userInfo 用户信息
//     * @param appInfo  应用信息
//     */
//    @Async
//    public void taskListener(TaskDTO taskDTO, UserInfo userInfo, AppInfo appInfo) {
//        try {
//            AppInfoContext.setApp(appInfo);
//            UserInfoContext.setUser(userInfo);
//
//            // 获取任务所属的租户ID
//            String tenantId = taskDTO.getTenantId();
//
//            // 从配置中获取对应的服务名称
//            String serviceName = openCareBpmProperties.getCallBackListener(tenantId);
//            if (serviceName == null) {
//                log.error("未找到对应租户ID的回调服务名称，tenantId: {}", tenantId);
//                return;
//            }
//            log.debug("根据租户ID[{}]获取到服务名称[{}]，准备创建Feign客户端", tenantId, serviceName);
//
//            BaseService baseService = factoryClientFactory.getInstance(serviceName, BaseService.class);
//
//            // 调用远程服务的taskListener方法
//            baseService.taskListener(taskDTO);
//
//            log.info("成功调用[{}]服务的taskListener接口", serviceName);
//        } catch (Exception e) {
//            log.error("远程调用taskListener失败: ", e);
//        }
//    }

    /**
     * 根据条件获取用户
     *
     * @param condition 条件
     * @return 用户列表JSON
     * @deprecated 请使用getUsersByConditionDTO
     */
    @Deprecated
    public String getUsersByCondition(String condition) {
        try {
            String tenantId = AppInfoContext.getApp().getAppInstId();
            String serviceName = taskEventProperties.getServiceName(tenantId);
            
            if (serviceName == null) {
                log.error("未找到对应租户ID的回调服务名称，tenantId: {}", tenantId);
                throw new IllegalStateException("获取任务节点对应的用户列表失败，未找到服务名称");
            }
            
            // 创建BaseService类型的Feign客户端
            CamundaExtensionService camundaExtensionService = factoryClientFactory.getInstance(serviceName, CamundaExtensionService.class);
            
            // 调用远程服务
            ResponseEntity<String> responseEntity = camundaExtensionService.getUsersByCondition(condition);
            String responseString = responseEntity.getBody();
            OcResponse response;
            try {
                response = jsonConverter.fromJson(responseString, OcResponse.class);
            } catch (Exception e) {
                response = null;
            }
            
            if (Objects.nonNull(response) && "-1".equals(response.getStatus())) {
                log.error("getUsersByCondition condition : {} error:{}", condition, responseEntity);
                throw new IllegalStateException("获取任务节点对应的用户列表失败，condition：" + condition);
            }
            
            return responseString;
        } catch (Exception e) {
            log.error("getUsersByCondition error, condition: {}", condition, e);
            throw new IllegalStateException("获取任务节点对应的用户列表失败: " + e.getMessage());
        }
    }

//    /**
//     * 根据条件获取用户DTO对象列表
//     *
//     * @param condition 条件
//     * @return 用户DTO对象列表
//     */
//    public List<UserDTO> getUsersByConditionDTO(String condition) {
//        try {
//            String tenantId = AppInfoContext.getApp().getAppInstId();
//            String serviceName = openCareBpmProperties.getCallBackListener(tenantId);
//
//            if (serviceName == null) {
//                log.error("未找到对应租户ID的回调服务名称，tenantId: {}", tenantId);
//                throw new IllegalStateException("获取任务节点对应的用户列表失败，未找到服务名称");
//            }
//
//            // 创建BaseService类型的Feign客户端
//            BaseService baseService = factoryClientFactory.getInstance(serviceName, BaseService.class);
//
//            // 调用远程服务
//            ResponseEntity<String> responseEntity = baseService.getUsersByCondition(condition);
//
//            if (HttpStatus.OK == responseEntity.getStatusCode()) {
//                String responseString = responseEntity.getBody();
//                log.info("getUsersByConditionDTO users: {}", responseString);
//                return jsonConverter.fromJson(responseString, new TypeToken<List<UserDTO>>() {
//                }.getType());
//            }
//
//            throw new IllegalStateException("获取任务节点对应的用户列表失败，condition：" + condition);
//        } catch (Exception e) {
//            log.error("getUsersByConditionDTO error, condition: {}", condition, e);
//            throw new IllegalStateException("获取任务节点对应的用户列表失败: " + e.getMessage());
//        }
//    }

//    /**
//     * 获取访问令牌
//     *
//     * @return 访问令牌
//     */
//    public String getAccessToken() {
//        try {
//            HttpHeaders headers = createHeaders(false);
//            HttpEntity<String> entity = new HttpEntity<>(headers);
//
//            ResponseEntity<String> response = restTemplate.exchange(
//                    urlGatewayServer + "/getAccessToken",
//                    HttpMethod.GET,
//                    entity,
//                    String.class
//            );
//
//            return response.getBody();
//        } catch (RestClientException e) {
//            log.error("获取访问令牌失败", e);
//            return null;
//        }
//    }
}
