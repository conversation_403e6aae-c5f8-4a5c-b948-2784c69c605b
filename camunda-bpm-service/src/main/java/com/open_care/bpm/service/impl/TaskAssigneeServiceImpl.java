package com.open_care.bpm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.api.common.ServiceException;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.export.Page;
import com.open_care.api.common.query.QueryDataResponse;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.bpm.context.ProcessDataContext;
import com.open_care.bpm.core.ProcessVariableUtil;
import com.open_care.bpm.dto.TaskAssignLogDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.dto.TaskTransferDTO;
import com.open_care.bpm.dto.TaskTransferLogDTO;
import com.open_care.bpm.dto.TaskTransferLogQueryRequestDTO;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.entity.TaskAssigneeLog;
import com.open_care.bpm.entity.CandidateUser;
import com.open_care.bpm.entity.CandidateGroup;
import com.open_care.bpm.enums.TaskCategoryEnum;
import com.open_care.bpm.mapper.TaskAssigneeMapper;
import com.open_care.bpm.mapper.TaskMapper;
import com.open_care.bpm.repository.TaskAssigneeLogRepository;
import com.open_care.bpm.repository.CandidateUserRepository;
import com.open_care.bpm.repository.CandidateGroupRepository;
import com.open_care.bpm.service.TaskAssigneeService;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import com.open_care.camunda.util.ProcessUtil;
import com.open_care.camunda.util.PropertyJsonWrapper;
import com.open_care.camunda.util.TaskConst;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.IdentityLinkType;
import org.camunda.bpm.engine.task.Task;
import org.camunda.spin.json.SpinJsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务转办服务实现类
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class TaskAssigneeServiceImpl implements TaskAssigneeService {

    @Autowired
    private TaskAssigneeLogRepository taskAssigneeLogRepository;

    @Autowired
    private CandidateUserRepository candidateUserRepository;

    @Autowired
    private CandidateGroupRepository candidateGroupRepository;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    @Lazy
    private TaskMapper taskMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public OcResponse<List<TaskTransferLogDTO>> taskTransfer(TaskTransferDTO taskTransferDTO) {
        try {
            List<String> taskIds = taskTransferDTO.getTaskIds();
            if (CollUtil.isEmpty(taskIds)) {
                return CommonUtils.getErrorOcResponse("任务转办失败: 任务ID列表不能为空", null);
            }

            List<TaskTransferLogDTO> resultDTOs = processTaskTransfers(taskTransferDTO, taskIds);
            return CommonUtils.getOcResponse("任务转办成功", resultDTOs);
        } catch (Exception e) {
            log.error("任务转办失败: {}", e.getMessage(), e);
            return CommonUtils.getErrorOcResponse(StrUtil.format("任务转办失败: {}", e.getMessage()), null);
        }
    }

    /**
     * 处理多个任务的转办
     *
     * @param taskTransferDTO 任务转办参数
     * @param taskIds         任务ID列表
     * @return 转办结果列表
     */
    private List<TaskTransferLogDTO> processTaskTransfers(TaskTransferDTO taskTransferDTO, List<String> taskIds) {
        List<TaskTransferLogDTO> resultDTOs = new ArrayList<>();
        for (String taskId : taskIds) {
            TaskTransferLogDTO taskTransferLogDTO = processTaskTransfer(taskTransferDTO, taskId);
            resultDTOs.add(taskTransferLogDTO);
        }
        return resultDTOs;
    }

    /**
     * 处理单个任务的转办
     *
     * @param taskTransferDTO 任务转办参数
     * @param taskId          任务ID
     * @return 转办结果
     */
    private TaskTransferLogDTO processTaskTransfer(TaskTransferDTO taskTransferDTO, String taskId) {
        // 获取任务和校验转办权限
        Task task = getTaskAndValidateTransferPermission(taskId);

        // 获取任务原始信息
        TaskOriginalInfo originalInfo = getTaskOriginalInfo(task);

        // 更新任务受让人
        updateTaskAssignee(taskId, taskTransferDTO.getAssignee());

        // 更新候选人和候选组
        updateTaskCandidates(taskId, originalInfo.getIdentityLinks(),
                taskTransferDTO.getCandidateUsers(),
                taskTransferDTO.getCandidateGroups());

        // 保存转办日志到数据库
        TaskAssigneeLog savedLog = saveTaskAssigneeLogToDatabase(task, originalInfo, taskTransferDTO);

        // 保存转办日志到本地变量
        saveAssigneeLogToTaskVariable(
                task,
                savedLog.getId(),
                originalInfo.getOwner(),
                originalInfo.getAssignee(),
                originalInfo.getCandidateUsers(),
                originalInfo.getCandidateGroups(),
                task.getOwner(),
                taskTransferDTO.getAssignee(),
                taskTransferDTO.getCandidateUsers(),
                taskTransferDTO.getCandidateGroups(),
                taskTransferDTO.getReason(),
                taskTransferDTO.getOperator(),
                savedLog.getAssignTime()
        );

        // 构建并返回结果
        return buildTaskTransferLogDTO(task, savedLog, taskTransferDTO);
    }

    /**
     * 获取任务并校验转办权限
     */
    private Task getTaskAndValidateTransferPermission(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        PropertyJsonWrapper properties = ExtensionPropertyUtils.getAllPropertiesAsJsonWrapper(task, taskService, repositoryService);

        // 判断任务是否允许转办
        if (!isTaskAssigneeEnabled(properties)) {
            throw new IllegalStateException("任务转办失败: 此任务不允许转办");
        }

        // 如果任务已经被转办过，且不允许多次转办，则返回错误
        List<TaskAssigneeLog> existingLogs = findTaskAssigneeLogsByTaskId(taskId);
        if (CollUtil.isNotEmpty(existingLogs) && !isTaskMultiAssigneeEnabled(properties)) {
            throw new IllegalStateException("任务转办失败: 此任务不允许多次转办");
        }

        return task;
    }

    /**
     * 获取任务原始信息
     *
     * @param task 任务对象
     * @return 任务原始信息
     */
    private TaskOriginalInfo getTaskOriginalInfo(Task task) {
        if (task == null) {
            throw new ServiceException("无法获取任务原始信息: 任务对象为空");
        }

        String originalOwner = task.getOwner();
        String originalAssignee = task.getAssignee();
        List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());

        // 提取候选用户
        List<String> originalCandidateUsers = extractCandidateUsers(identityLinks);

        // 提取候选组
        List<String> originalCandidateGroups = extractCandidateGroups(identityLinks);

        return new TaskOriginalInfo(originalOwner, originalAssignee, originalCandidateUsers,
                originalCandidateGroups, identityLinks);
    }

    /**
     * 从身份链接中提取候选用户
     *
     * @param identityLinks 身份链接列表
     * @return 候选用户ID列表
     */
    private List<String> extractCandidateUsers(List<IdentityLink> identityLinks) {
        if (CollUtil.isEmpty(identityLinks)) {
            return new ArrayList<>();
        }

        return identityLinks.stream()
                .filter(link -> IdentityLinkType.CANDIDATE.equals(link.getType()) && link.getUserId() != null)
                .map(IdentityLink::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 从身份链接中提取候选组
     *
     * @param identityLinks 身份链接列表
     * @return 候选组ID列表
     */
    private List<String> extractCandidateGroups(List<IdentityLink> identityLinks) {
        if (CollUtil.isEmpty(identityLinks)) {
            return new ArrayList<>();
        }

        return identityLinks.stream()
                .filter(link -> IdentityLinkType.CANDIDATE.equals(link.getType()) && link.getGroupId() != null)
                .map(IdentityLink::getGroupId)
                .collect(Collectors.toList());
    }

    /**
     * 更新任务受让人
     *
     * @param taskId   任务ID
     * @param assignee 受让人ID
     */
    private void updateTaskAssignee(String taskId, String assignee) {
        if (StrUtil.isNotBlank(assignee)) {
            taskService.setAssignee(taskId, assignee);
            log.debug("已更新任务[{}]的受让人为: {}", taskId, assignee);
        }
    }

    /**
     * 更新任务候选人和候选组
     *
     * @param taskId             任务ID
     * @param identityLinks      原始身份链接列表
     * @param newCandidateUsers  新候选用户列表
     * @param newCandidateGroups 新候选组列表
     */
    private void updateTaskCandidates(String taskId, List<IdentityLink> identityLinks,
                                      List<String> newCandidateUsers, List<String> newCandidateGroups) {
        // 更新候选用户
        updateTaskCandidateUsers(taskId, identityLinks, newCandidateUsers);

        // 更新候选组
        updateTaskCandidateGroups(taskId, identityLinks, newCandidateGroups);
    }

    /**
     * 更新任务候选用户
     *
     * @param taskId            任务ID
     * @param identityLinks     原始身份链接列表
     * @param newCandidateUsers 新候选用户列表
     */
    private void updateTaskCandidateUsers(String taskId, List<IdentityLink> identityLinks, List<String> newCandidateUsers) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("更新候选用户失败: 任务ID为空");
            return;
        }

        if (CollUtil.isNotEmpty(newCandidateUsers)) {
            // 删除原候选人
            removeCandidateUsers(taskId, identityLinks);

            // 添加新候选人
            addCandidateUsers(taskId, newCandidateUsers);

            log.debug("已更新任务[{}]的候选用户: {}", taskId, newCandidateUsers);
        }
    }

    /**
     * 删除原候选用户
     *
     * @param taskId        任务ID
     * @param identityLinks 身份链接列表
     */
    private void removeCandidateUsers(String taskId, List<IdentityLink> identityLinks) {
        if (CollUtil.isEmpty(identityLinks)) {
            return;
        }

        identityLinks.stream()
                .filter(link -> IdentityLinkType.CANDIDATE.equals(link.getType()) && link.getUserId() != null)
                .forEach(link -> taskService.deleteCandidateUser(taskId, link.getUserId()));
    }

    /**
     * 添加新候选用户
     *
     * @param taskId         任务ID
     * @param candidateUsers 候选用户列表
     */
    private void addCandidateUsers(String taskId, List<String> candidateUsers) {
        if (CollUtil.isEmpty(candidateUsers)) {
            return;
        }

        candidateUsers.forEach(userId -> taskService.addCandidateUser(taskId, userId));
    }

    /**
     * 更新任务候选组
     *
     * @param taskId             任务ID
     * @param identityLinks      原始身份链接列表
     * @param newCandidateGroups 新候选组列表
     */
    private void updateTaskCandidateGroups(String taskId, List<IdentityLink> identityLinks, List<String> newCandidateGroups) {
        if (StrUtil.isBlank(taskId)) {
            log.warn("更新候选组失败: 任务ID为空");
            return;
        }

        if (CollUtil.isNotEmpty(newCandidateGroups)) {
            // 删除原候选组
            removeCandidateGroups(taskId, identityLinks);

            // 添加新候选组
            addCandidateGroups(taskId, newCandidateGroups);

            log.debug("已更新任务[{}]的候选组: {}", taskId, newCandidateGroups);
        }
    }

    /**
     * 删除原候选组
     *
     * @param taskId        任务ID
     * @param identityLinks 身份链接列表
     */
    private void removeCandidateGroups(String taskId, List<IdentityLink> identityLinks) {
        if (CollUtil.isEmpty(identityLinks)) {
            return;
        }

        identityLinks.stream()
                .filter(link -> IdentityLinkType.CANDIDATE.equals(link.getType()) && link.getGroupId() != null)
                .forEach(link -> taskService.deleteCandidateGroup(taskId, link.getGroupId()));
    }

    /**
     * 添加新候选组
     *
     * @param taskId          任务ID
     * @param candidateGroups 候选组列表
     */
    private void addCandidateGroups(String taskId, List<String> candidateGroups) {
        if (CollUtil.isEmpty(candidateGroups)) {
            return;
        }

        candidateGroups.forEach(groupId -> taskService.addCandidateGroup(taskId, groupId));
    }

    /**
     * 保存转办日志到数据库
     *
     * @param task            任务对象
     * @param originalInfo    任务原始信息
     * @param taskTransferDTO 任务转办参数
     * @return 保存后的转办日志实体
     */
    private TaskAssigneeLog saveTaskAssigneeLogToDatabase(Task task, TaskOriginalInfo originalInfo, TaskTransferDTO taskTransferDTO) {
        if (task == null) {
            throw new ServiceException("保存转办日志失败: 任务对象为空");
        }

        if (originalInfo == null) {
            throw new ServiceException("保存转办日志失败: 任务原始信息为空");
        }

        if (taskTransferDTO == null) {
            throw new ServiceException("保存转办日志失败: 转办参数为空");
        }

        // 创建转办日志实体
        TaskAssigneeLog taskAssigneeLog = createTaskAssigneeLog(
                task,
                originalInfo.getOwner(),
                originalInfo.getAssignee(),
                originalInfo.getCandidateUsers(),
                originalInfo.getCandidateGroups(),
                task.getOwner(),
                taskTransferDTO.getAssignee(),
                taskTransferDTO.getCandidateUsers(),
                taskTransferDTO.getCandidateGroups(),
                taskTransferDTO.getReason(),
                taskTransferDTO.getReason(),
                taskTransferDTO.getAssignTime()
        );

        // 设置转办原因和操作人
        taskAssigneeLog.setAssignReason(taskTransferDTO.getReason());
        taskAssigneeLog.setOperator(taskTransferDTO.getOperator());

        // 保存到数据库
        TaskAssigneeLog savedLog = saveTaskAssigneeLog(taskAssigneeLog);
        log.debug("已保存任务[{}]的转办日志: {}", task.getId(), savedLog.getId());

        return savedLog;
    }

    /**
     * 构建任务转办日志DTO
     *
     * @param task            任务对象
     * @param savedLog        保存的转办日志
     * @param taskTransferDTO 任务转办参数
     * @return 转办日志DTO
     */
    private TaskTransferLogDTO buildTaskTransferLogDTO(Task task, TaskAssigneeLog savedLog, TaskTransferDTO taskTransferDTO) {
        // 转换任务信息
        TaskInfoDTO taskInfoDTO = taskMapper.convertToDTO((TaskEntity) task);

        // 使用MapStruct进行转换
        TaskTransferLogDTO resultDTO = TaskAssigneeMapper.INSTANCE.toTaskTransferLogDTO(savedLog);

        // 复制TaskInfoDTO的额外属性到TaskTransferLogDTO
        TaskAssigneeMapper.INSTANCE.copyTaskInfoToTransferLogDTO(taskInfoDTO, resultDTO);

        // 从任务变量中获取最新的转办记录
        updateAssignLogFromTaskVariables(task, savedLog, resultDTO);

        return resultDTO;
    }

    /**
     * 从任务变量中更新转办记录
     *
     * @param task      任务对象
     * @param savedLog  保存的转办日志
     * @param resultDTO 结果DTO
     */
    private void updateAssignLogFromTaskVariables(Task task, TaskAssigneeLog savedLog, TaskTransferLogDTO resultDTO) {
        try {
            Map<String, Object> taskLocalVariables = taskService.getVariablesLocal(task.getId());
            List<TaskAssignLogDTO> assignLogs = ProcessVariableUtil.getAssignLogsFromVariables(taskLocalVariables);

            if (CollUtil.isEmpty(assignLogs)) {
                return;
            }

            // 获取最新的转办记录
            TaskAssignLogDTO latestLog = assignLogs.get(assignLogs.size() - 1);

            // 如果已有assignLog，则补充信息；否则直接设置
            if (resultDTO.getAssignLog() != null) {
                // 确保ID一致时再更新
                if (savedLog.getId() != null && latestLog.getId() != null &&
                        savedLog.getId().equals(latestLog.getId())) {
                    resultDTO.setAssignLog(latestLog);
                }
            } else {
                resultDTO.setAssignLog(latestLog);
            }
        } catch (Exception e) {
            log.warn("获取任务[{}]变量中的转办记录失败: {}", task.getId(), e.getMessage());
        }
    }

    /**
     * 任务原始信息类
     */
    private static class TaskOriginalInfo {
        private final String owner;
        private final String assignee;
        private final List<String> candidateUsers;
        private final List<String> candidateGroups;
        private final List<IdentityLink> identityLinks;

        public TaskOriginalInfo(String owner, String assignee, List<String> candidateUsers,
                                List<String> candidateGroups, List<IdentityLink> identityLinks) {
            this.owner = owner;
            this.assignee = assignee;
            this.candidateUsers = candidateUsers;
            this.candidateGroups = candidateGroups;
            this.identityLinks = identityLinks;
        }

        public String getOwner() {
            return owner;
        }

        public String getAssignee() {
            return assignee;
        }

        public List<String> getCandidateUsers() {
            return candidateUsers;
        }

        public List<String> getCandidateGroups() {
            return candidateGroups;
        }

        public List<IdentityLink> getIdentityLinks() {
            return identityLinks;
        }
    }

    @Override
    public OcResponse<QueryDataResponse<List<TaskTransferLogDTO>>> findTaskTransferLog(TaskTransferLogQueryRequestDTO queryRequestDTO) {
        if (queryRequestDTO == null) {
            return CommonUtils.getErrorOcResponse("查询任务转办日志失败: 查询参数为空", null);
        }

        try {
            // 执行查询并获取分页结果
            org.springframework.data.domain.Page<TaskAssigneeLog> page = queryTaskAssigneeLogs(queryRequestDTO);

            // 如果没有数据，直接返回空结果
            if (page.isEmpty()) {
                return buildEmptyQueryResponse(queryRequestDTO.getPagination(), page.getTotalElements());
            }

            // 批量获取流程相关数据
            ProcessDataContext processDataContext = batchFetchProcessData(page.getContent());

            // 转换结果
            List<TaskTransferLogDTO> resultList = convertToTaskTransferLogDTOList(
                    page.getContent(),
                    processDataContext
            );

            // 构建并返回分页响应
            return buildQueryResponse(resultList, queryRequestDTO.getPagination(), page.getTotalElements());
        } catch (Exception e) {
            String errorMsg = StrUtil.format("查询任务转办日志失败: {}", e.getMessage());
            log.error(errorMsg, e);
            return CommonUtils.getErrorOcResponse(errorMsg, null);
        }
    }

    /**
     * 构建空的查询响应
     *
     * @param pagination    分页参数
     * @param totalElements 总条数
     * @return 空的查询响应
     */
    private OcResponse<QueryDataResponse<List<TaskTransferLogDTO>>> buildEmptyQueryResponse(Page pagination, long totalElements) {
        QueryDataResponse<List<TaskTransferLogDTO>> response = new QueryDataResponse<>();
        response.setData(new ArrayList<>());
        response.setPagination(pagination);
        response.setAuthority(new ArrayList<>());

        if (pagination != null) {
            pagination.setTotal((int) totalElements);
        }

        return CommonUtils.getOcResponse("查询任务转办日志成功", response);
    }

    /**
     * 根据查询条件查询任务转办日志
     *
     * @param queryRequestDTO 查询参数
     * @return 分页查询结果
     */
    private org.springframework.data.domain.Page<TaskAssigneeLog> queryTaskAssigneeLogs(TaskTransferLogQueryRequestDTO queryRequestDTO) {
        // 构建查询条件
        Specification<TaskAssigneeLog> spec = buildSpecification(queryRequestDTO);

        // 构建分页参数
        Page pagination = queryRequestDTO.getPagination();
        if (pagination == null) {
            pagination = new Page();
            pagination.setPageSize(10);
            pagination.setCurrent(1);
        }

        // 创建分页和排序参数
        Pageable pageable = createPageable(pagination);

        // 执行查询
        return taskAssigneeLogRepository.findAll(spec, pageable);
    }

    /**
     * 创建分页和排序参数
     *
     * @param pagination 分页参数
     * @return Spring分页对象
     */
    private Pageable createPageable(Page pagination) {
        return PageRequest.of(
                Math.max(0, pagination.getCurrent() - 1),  // 确保页码不为负数
                Math.max(1, pagination.getPageSize()),     // 确保每页条数至少为1
                Sort.by(Sort.Direction.DESC, "assignTime") // 按转办时间降序排序
        );
    }

    /**
     * 批量获取流程相关数据
     */
    private ProcessDataContext batchFetchProcessData(List<TaskAssigneeLog> assigneeLogs) {
        ProcessDataContext context = new ProcessDataContext();

        // 1. 收集所有流程实例ID
        Set<String> processInstanceIds = assigneeLogs.stream()
                .map(TaskAssigneeLog::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 2. 批量查询活动的流程实例
        if (!processInstanceIds.isEmpty()) {
            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .list();
            context.addProcessInstances(processInstances);
        }

        // 3. 查询历史流程实例（针对已结束的流程）
        Map<String, ProcessInstance> processInstanceMap = new HashMap<>();
        assigneeLogs.stream()
                .map(TaskAssigneeLog::getProcessInstanceId)
                .filter(Objects::nonNull)
                .forEach(id -> {
                    ProcessInstance instance = context.getProcessInstance(id);
                    if (instance != null) {
                        processInstanceMap.put(id, instance);
                    }
                });

        Set<String> missingProcessInstanceIds = processInstanceIds.stream()
                .filter(id -> !processInstanceMap.containsKey(id))
                .collect(Collectors.toSet());

        if (!missingProcessInstanceIds.isEmpty()) {
            List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceIds(missingProcessInstanceIds)
                    .list();
            context.addHistoricProcessInstances(historicProcessInstances);
        }

        // 4. 收集所有流程定义ID
        Set<String> processDefinitionIds = new HashSet<>();
        assigneeLogs.stream()
                .filter(log -> ObjectUtil.isNotNull(log.getProcessInstanceId()))
                .forEach(log -> {
                    ProcessInstance processInstance = context.getProcessInstance(log.getProcessInstanceId());
                    if (processInstance != null) {
                        processDefinitionIds.add(processInstance.getProcessDefinitionId());
                    } else {
                        HistoricProcessInstance historicProcessInstance = context.getHistoricProcessInstance(log.getProcessInstanceId());
                        if (historicProcessInstance != null) {
                            processDefinitionIds.add(historicProcessInstance.getProcessDefinitionId());
                        }
                    }
                });
        // 添加TaskAssigneeLog中的流程定义ID，确保完整性
        assigneeLogs.stream()
                .map(TaskAssigneeLog::getProcessDefinitionId)
                .filter(Objects::nonNull)
                .forEach(processDefinitionIds::add);

        // 5. 批量查询流程定义
        if (!processDefinitionIds.isEmpty()) {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionIdIn(processDefinitionIds.toArray(new String[0]))
                    .list();
            context.addProcessDefinitions(processDefinitions);
        }

        // 6. 预获取历史流程变量，避免每次创建DTO时都查询
        if (!missingProcessInstanceIds.isEmpty()) {
            // 批量查询历史流程变量
            List<org.camunda.bpm.engine.history.HistoricVariableInstance> historicVariables =
                    historyService.createHistoricVariableInstanceQuery()
                            .processInstanceIdIn(missingProcessInstanceIds.toArray(new String[0]))
                            .list();
            context.addHistoricVariables(historicVariables);
        }

        return context;
    }

    /**
     * 批量转换日志为DTO
     */
    private List<TaskTransferLogDTO> convertToTaskTransferLogDTOList(
            List<TaskAssigneeLog> assigneeLogs,
            ProcessDataContext processDataContext) {

        return assigneeLogs.stream()
                .map(log -> convertToTaskTransferLogDTO(log, processDataContext))
                .collect(Collectors.toList());
    }

    /**
     * 构建查询响应
     */
    private OcResponse<QueryDataResponse<List<TaskTransferLogDTO>>> buildQueryResponse(
            List<TaskTransferLogDTO> resultList, Page pagination, long totalElements) {

        QueryDataResponse<List<TaskTransferLogDTO>> response = new QueryDataResponse<>();
        response.setData(resultList);
        response.setPagination(pagination);
        response.setAuthority(new ArrayList<>());
        pagination.setTotal((int) totalElements);

        return CommonUtils.getOcResponse("查询任务转办日志成功", response);
    }

    @Override
    public TaskAssigneeLog saveTaskAssigneeLog(TaskAssigneeLog taskAssigneeLog) {
        return taskAssigneeLogRepository.save(taskAssigneeLog);
    }

    @Override
    public List<TaskAssigneeLog> findTaskAssigneeLogsByTaskId(String taskId) {
        return taskAssigneeLogRepository.findByTaskIdOrderByAssignTimeDesc(taskId);
    }

    @Override
    public boolean isTaskAssigneeEnabled(PropertyJsonWrapper properties) {

        return properties.getBooleanByPrefixPathSuffix(
                compose(TaskConst.TASK, TaskConst.ASSIGN),
                TaskConst.EMPTY,
                false,
                TaskConst.ENABLE);
    }

    @Override
    public boolean isTaskMultiAssigneeEnabled(PropertyJsonWrapper properties) {

        return properties.getBooleanByPrefixPathSuffix(
                compose(TaskConst.TASK, TaskConst.ASSIGN, TaskConst.MULTI),
                TaskConst.EMPTY,
                false,
                TaskConst.ENABLE);
    }

    /**
     * 将转办日志保存到任务变量中
     *
     * @param task                    任务对象
     * @param transferId              转办日志ID
     * @param originalOwner           原始所有者
     * @param originalAssignee        原始受让人
     * @param originalCandidateUsers  原始候选用户
     * @param originalCandidateGroups 原始候选组
     * @param newOwner                新所有者
     * @param newAssignee             新受让人
     * @param newCandidateUsers       新候选用户
     * @param newCandidateGroups      新候选组
     * @param reason                  转办原因
     * @param operator                操作人
     */
    private TaskAssignLogDTO saveAssigneeLogToTaskVariable(Task task, String transferId, String originalOwner, String originalAssignee,
                                                           List<String> originalCandidateUsers, List<String> originalCandidateGroups,
                                                           String newOwner, String newAssignee,
                                                           List<String> newCandidateUsers, List<String> newCandidateGroups,
                                                           String reason, String operator, Date assignTime) {
        if (task == null || StrUtil.isBlank(task.getId())) {
            throw new ServiceException("保存任务转办日志到任务变量失败: 任务对象为空或ID为空");
        }

        try {
            // 获取现有的转办日志
            Map<String, Object> taskLocalVariables = taskService.getVariablesLocal(task.getId());
            List<TaskAssignLogDTO> assignLogs = ProcessVariableUtil.getAssignLogsFromVariables(taskLocalVariables);

            // 创建新的日志列表，如果原列表为空
            assignLogs = CollUtil.isEmpty(assignLogs) ? new ArrayList<>() : new ArrayList<>(assignLogs);

            // 创建新的日志记录
            TaskAssignLogDTO logEntry = createTaskAssignLogDTO(
                    transferId,
                    originalOwner,
                    originalAssignee,
                    originalCandidateUsers,
                    originalCandidateGroups,
                    newOwner,
                    newAssignee,
                    newCandidateUsers,
                    newCandidateGroups,
                    reason,
                    operator,
                    assignTime
            );

            // 添加到日志列表
            assignLogs.add(logEntry);

            // 保存更新后的日志
            SpinJsonNode jsonNode = SpinJsonNode.JSON(assignLogs);
            taskService.setVariableLocal(
                    task.getId(),
                    ProcessVariableUtil.VARIABLE_TASK_ASSIGN_LOGS,
                    ProcessUtil.createTypedVariable(jsonNode)
            );

            log.debug("已保存任务[{}]的转办日志到任务变量", task.getId());
            return logEntry;
        } catch (Exception e) {
            String errorMsg = StrUtil.format("保存任务[{}]转办日志到任务变量失败: {}", task.getId(), e.getMessage());
            log.error(errorMsg, e);
            throw new ServiceException(errorMsg, e);
        }
    }

    /**
     * 创建任务转办日志DTO
     */
    private TaskAssignLogDTO createTaskAssignLogDTO(String transferId, String originalOwner, String originalAssignee,
                                                    List<String> originalCandidateUsers, List<String> originalCandidateGroups,
                                                    String newOwner, String newAssignee,
                                                    List<String> newCandidateUsers, List<String> newCandidateGroups,
                                                    String reason, String operator, Date assignTime) {
        return TaskAssignLogDTO.builder()
                .id(transferId)
                .owner(originalOwner)
                .assignee(newAssignee)
                .candidateUsers(newCandidateUsers)
                .candidateGroups(newCandidateGroups)
                .assignTime(new Date())
                .originalAssignee(originalAssignee)
                .originalCandidateUsers(originalCandidateUsers)
                .originalCandidateGroups(originalCandidateGroups)
                .operator(operator)
                .assignReason(reason)
                .assignTime(assignTime)
                .build();
    }

    /**
     * 创建任务转办日志实体
     */
    private TaskAssigneeLog createTaskAssigneeLog(Task task, String originalOwner, String originalAssignee,
                                                  List<String> originalCandidateUsers, List<String> originalCandidateGroups,
                                                  String newOwner, String newAssignee,
                                                  List<String> newCandidateUsers, List<String> newCandidateGroups,
                                                  String reason, String operator, Date assignTime) {
        try {

            // 创建TaskAssigneeLog实例
            TaskAssigneeLog log = TaskAssigneeLog.builder()
                    .taskId(task.getId())
                    .processInstanceId(task.getProcessInstanceId())
                    .processDefinitionId(task.getProcessDefinitionId())
                    .taskDefinitionKey(task.getTaskDefinitionKey())
                    .taskName(task.getName())
                    .originalOwner(originalOwner)
                    .originalAssignee(originalAssignee)
                    .newOwner(newOwner)
                    .newAssignee(newAssignee)
                    .assignTime(new Date())
                    .tenantId(Optional.ofNullable(AppInfoContext.getApp()).map(app -> app.getAppInstId()).orElse(null))
                    .operator(operator)
                    .assignReason(reason)
                    .assignTime(assignTime)
                    .build();

            // 保存日志以获取ID
            log = taskAssigneeLogRepository.save(log);

            TaskAssigneeLog finalLog = log;

            // 创建原始候选用户
            log.setOriginalCandidateUsers(createCandidateUsers(originalCandidateUsers, finalLog, CandidateUser.CandidateType.ORIGINAL));

            // 创建原始候选组
            log.setOriginalCandidateGroups(createCandidateGroups(originalCandidateGroups, finalLog, CandidateGroup.CandidateType.ORIGINAL));

            // 创建新候选用户
            log.setNewCandidateUsers(createCandidateUsers(newCandidateUsers, finalLog, CandidateUser.CandidateType.NEW));

            // 创建新候选组
            log.setNewCandidateGroups(createCandidateGroups(newCandidateGroups, finalLog, CandidateGroup.CandidateType.NEW));

            // 更新日志实体
            return taskAssigneeLogRepository.save(log);
        } catch (Exception e) {
            log.error("创建任务转办日志失败", e);
            throw new ServiceException("创建任务转办日志失败", e);
        }
    }

    /**
     * 创建候选用户列表
     *
     * @param userIds     用户ID列表
     * @param assigneeLog 关联的转办日志
     * @param type        候选人类型（原始/新）
     * @return 候选用户列表
     */
    private List<CandidateUser> createCandidateUsers(List<String> userIds, TaskAssigneeLog assigneeLog, CandidateUser.CandidateType type) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<CandidateUser> candidateUsers = userIds.stream()
                .map(userId -> CandidateUser.builder()
                        .identifier(userId)
                        .taskAssigneeLog(assigneeLog)
                        .type(type)
                        .isAssignee(false)
                        .build())
                .collect(Collectors.toList());

        return candidateUserRepository.saveAll(candidateUsers);
    }

    /**
     * 创建候选组列表
     *
     * @param groupIds    组ID列表
     * @param assigneeLog 关联的转办日志
     * @param type        候选组类型（原始/新）
     * @return 候选组列表
     */
    private List<CandidateGroup> createCandidateGroups(List<String> groupIds, TaskAssigneeLog assigneeLog, CandidateGroup.CandidateType type) {
        if (groupIds == null || groupIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<CandidateGroup> candidateGroups = groupIds.stream()
                .map(groupId -> CandidateGroup.builder()
                        .identifier(groupId)
                        .taskAssigneeLog(assigneeLog)
                        .type(type)
                        .build())
                .collect(Collectors.toList());

        return candidateGroupRepository.saveAll(candidateGroups);
    }

    /**
     * 构建查询条件
     */
    private Specification<TaskAssigneeLog> buildSpecification(TaskTransferLogQueryRequestDTO queryRequestDTO) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 根据任务名称模糊查询
            if (StrUtil.isNotBlank(queryRequestDTO.getTaskName())) {
                predicates.add(cb.like(root.get("taskName"), "%" + queryRequestDTO.getTaskName() + "%"));
            }

            // 根据提交人查询
            if (StrUtil.isNotBlank(queryRequestDTO.getCommitter())) {
                predicates.add(cb.equal(root.get("operator"), queryRequestDTO.getCommitter()));
            }

            // 根据受让人查询
            if (StrUtil.isNotBlank(queryRequestDTO.getOperator())) {
                // 直接查询newAssignee字段
                predicates.add(cb.equal(root.get("newAssignee"), queryRequestDTO.getOperator()));
            }

            // 根据提交时间范围查询
            if (queryRequestDTO.getCommitterTimeStart() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("assignTime"), queryRequestDTO.getCommitterTimeStart()));
            }

            if (queryRequestDTO.getCommitterTimeEnd() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("assignTime"), queryRequestDTO.getCommitterTimeEnd()));
            }

            // 如果有租户ID，则添加租户条件
            if (AppInfoContext.getApp() != null && StrUtil.isNotBlank(AppInfoContext.getApp().getAppInstId())) {
                predicates.add(cb.equal(root.get("tenantId"), AppInfoContext.getApp().getAppInstId()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 将单个 TaskAssigneeLog 转换为 TaskTransferLogDTO
     * 用于单个日志转换场景，会进行数据库查询
     */
    private TaskTransferLogDTO convertToTaskTransferLogDTO(TaskAssigneeLog assigneeLog) {
        try {
            // 使用MapStruct进行转换
            TaskTransferLogDTO dto = TaskAssigneeMapper.INSTANCE.toTaskTransferLogDTO(assigneeLog);

            // 获取任务信息以设置任务类别和从任务变量中获取转办记录
            if (dto.getTaskId() != null) {
                try {
                    // 设置任务类别
                    if (StrUtil.isBlank(dto.getAssignee())) {
                        dto.setTaskCategory(TaskCategoryEnum.SHARED_POOL_TASKS);
                    } else {
                        dto.setTaskCategory(TaskCategoryEnum.PERSONAL_TASKS);
                    }

                    // 从任务变量中获取转办记录
                    Task task = taskService.createTaskQuery().taskId(dto.getTaskId()).singleResult();
                    if (task != null) {
                        Map<String, Object> taskLocalVariables = taskService.getVariablesLocal(dto.getTaskId());
                        List<TaskAssignLogDTO> assignLogs = ProcessVariableUtil.getAssignLogsFromVariables(taskLocalVariables);

                        if (CollUtil.isNotEmpty(assignLogs)) {
                            // 根据ID匹配相应的转办记录
                            if (dto.getAssignLog() != null && dto.getAssignLog().getId() != null) {
                                // 在变量中查找匹配的转办记录
                                for (TaskAssignLogDTO logDTO : assignLogs) {
                                    if (dto.getAssignLog().getId() != null &&
                                            dto.getAssignLog().getId().equals(logDTO.getId())) {
                                        // 合并变量中的转办记录信息到DTO
                                        dto.setAssignLog(logDTO);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取任务变量中的转办记录失败: {}", e.getMessage());
                }
            }

            return dto;
        } catch (Exception e) {
            log.error("转换TaskAssigneeLog为TaskTransferLogDTO失败", e);
            throw new ServiceException("转换TaskAssigneeLog为TaskTransferLogDTO失败", e);
        }
    }

    /**
     * 将TaskAssigneeLog转换为TaskTransferLogDTO
     */
    private TaskTransferLogDTO convertToTaskTransferLogDTO(TaskAssigneeLog assigneeLog,
                                                           ProcessDataContext processDataContext) {
        try {

            // 使用MapStruct进行基本转换
            TaskTransferLogDTO dto = TaskAssigneeMapper.INSTANCE.toTaskTransferLogDTO(assigneeLog);

            // 设置任务类别
            if (StrUtil.isBlank(dto.getAssignee())) {
                dto.setTaskCategory(TaskCategoryEnum.SHARED_POOL_TASKS);
            } else {
                dto.setTaskCategory(TaskCategoryEnum.PERSONAL_TASKS);
            }

            // 从任务变量中获取转办记录和任务变量
            if (dto.getTaskId() != null) {
                try {
                    Map<String, Object> taskLocalVariables = taskService.getVariablesLocal(dto.getTaskId());
                    List<TaskAssignLogDTO> assignLogs = ProcessVariableUtil.getAssignLogsFromVariables(taskLocalVariables);

                    // 保存所有可能的转办日志到DTO
                    if (CollUtil.isNotEmpty(assignLogs)) {
                        dto.setAssignLogs(assignLogs);

                        // 根据ID匹配相应的转办记录
                        if (dto.getAssignLog() != null && assigneeLog.getId() != null) {
                            for (TaskAssignLogDTO logDTO : assignLogs) {
                                if (assigneeLog.getId() != null &&
                                        assigneeLog.getId().equals(logDTO.getId())) {
                                    // 合并变量中的转办记录信息到DTO
                                    dto.setAssignLog(logDTO);
                                    break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取任务变量中的转办记录失败: {}", e.getMessage());
                }
            }

            // 获取流程实例和流程定义
            String processInstanceId = assigneeLog.getProcessInstanceId();
            if (StrUtil.isBlank(processInstanceId)) {
                return dto;
            }

            // 设置流程实例信息
            ProcessInstance processInstance = processDataContext.getProcessInstance(processInstanceId);
            if (processInstance != null) {
                ProcessDefinition processDefinition = processDataContext.getProcessDefinition(processInstance.getProcessDefinitionId());

                // 如果缓存中没有但是有processDefinitionId，尝试直接获取
                if (processDefinition == null && StrUtil.isNotBlank(processInstance.getProcessDefinitionId())) {
                    processDefinition = getProcessDefinitionById(processInstance.getProcessDefinitionId());
                }

                if (processDefinition != null) {
                    // 创建流程实例DTO
                    ProcessInstDTO processInstDTO = createProcessInstDTO(processInstance, processDefinition);


                    dto.setProcessInst(processInstDTO);

                    // 设置业务键
                    dto.setBusinessKey(processInstance.getBusinessKey());

                    // 设置流程名称和定义键
                    dto.setProcessName(processDefinition.getName());
                    dto.setProcessDefinitionKey(processDefinition.getKey());

                }
            } else {
                HistoricProcessInstance historicProcessInstance = processDataContext.getHistoricProcessInstance(processInstanceId);
                if (historicProcessInstance != null) {
                    ProcessDefinition processDefinition = processDataContext.getProcessDefinition(historicProcessInstance.getProcessDefinitionId());

                    // 如果缓存中没有但是有processDefinitionId，尝试直接获取
                    if (processDefinition == null && StrUtil.isNotBlank(historicProcessInstance.getProcessDefinitionId())) {
                        processDefinition = getProcessDefinitionById(historicProcessInstance.getProcessDefinitionId());
                    }

                    Map<String, Object> historicVariables = processDataContext.getHistoricVariables(processInstanceId);

                    if (processDefinition != null) {
                        // 创建流程实例DTO
                        ProcessInstDTO processInstDTO = createProcessInstDTO(historicProcessInstance, processDefinition, historicVariables);

                        dto.setProcessInst(processInstDTO);

                        // 设置业务键
                        dto.setBusinessKey(historicProcessInstance.getBusinessKey());

                        // 设置流程名称和定义键
                        dto.setProcessName(processDefinition.getName());
                        dto.setProcessDefinitionKey(processDefinition.getKey());
                    }
                } else if (StrUtil.isNotBlank(assigneeLog.getProcessDefinitionId())) {
                    // 如果没有流程实例但有流程定义ID
                    ProcessDefinition processDefinition = processDataContext.getProcessDefinition(assigneeLog.getProcessDefinitionId());
                    if (processDefinition == null) {
                        processDefinition = getProcessDefinitionById(assigneeLog.getProcessDefinitionId());
                    }

                    if (processDefinition != null) {
                        dto.setProcessName(processDefinition.getName());
                        dto.setProcessDefinitionKey(processDefinition.getKey());
                    }
                }
            }

            return dto;
        } catch (Exception e) {
            log.error("转换TaskAssigneeLog为TaskTransferLogDTO失败", e);
            throw new ServiceException("转换TaskAssigneeLog为TaskTransferLogDTO失败", e);
        }
    }

    /**
     * 从历史流程实例创建ProcessInstDTO
     */
    private ProcessInstDTO createProcessInstDTO(HistoricProcessInstance historicProcessInstance, ProcessDefinition processDefinition, Map<String, Object> historicVariables) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();
        processInstDTO.setId(historicProcessInstance.getId());
        processInstDTO.setBusinessKey(historicProcessInstance.getBusinessKey());

        // 设置历史流程实例的更多字段
        Map<String, Object> variables = null;

        // 尝试获取历史变量
        if (historicVariables != null && !historicVariables.isEmpty()) {
            // 使用预获取的变量
            variables = historicVariables;
        } else {
            // 通过historyService获取变量
            variables = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(historicProcessInstance.getId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(
                            org.camunda.bpm.engine.history.HistoricVariableInstance::getName,
                            org.camunda.bpm.engine.history.HistoricVariableInstance::getValue,
                            (v1, v2) -> v2 // 如果有重复键，保留最后一个值
                    ));
        }

        processInstDTO.setStartingVariables(variables);
        processInstDTO.setCommitTime(historicProcessInstance.getStartTime());
        processInstDTO.setLastCommitTime(historicProcessInstance.getEndTime());
        processInstDTO.setCommitTime(ProcessVariableUtil.getCommitTimeFromVariables(processInstDTO.getVariables()));


        // 设置流程定义信息
        if (processDefinition != null) {
            ProcessInfoDTO processInfoDTO = ProcessInfoDTO.builder()
                    .processDefinitionId(processDefinition.getId())
                    .processDefinitionKey(processDefinition.getKey())
                    .processDefinitionName(processDefinition.getName())
                    .name(processDefinition.getName())
                    // 添加更多流程定义信息
                    .id(processDefinition.getId())
                    .extensionProperties(ExtensionPropertyUtils.getAllPropertiesAsMap(processDefinition, repositoryService))
                    .build();

            processInstDTO.setProcessInfo(processInfoDTO);
            processInstDTO.setProcessTitle(processDefinition.getName());
        }
        return processInstDTO;
    }

    /**
     * 从流程实例创建ProcessInstDTO
     */
    private ProcessInstDTO createProcessInstDTO(ProcessInstance processInstance, ProcessDefinition processDefinition) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();
        processInstDTO.setId(processInstance.getId());
        processInstDTO.setBusinessKey(processInstance.getBusinessKey());

        // 设置流程实例的更多字段
        processInstDTO.setVariables(runtimeService.getVariables(processInstance.getId()));
        processInstDTO.setCommitter(ProcessVariableUtil.getCommitterFromVariables(processInstDTO.getVariables()));
        processInstDTO.setCommitTime(ProcessVariableUtil.getCommitTimeFromVariables(processInstDTO.getVariables()));

        // 设置流程定义信息
        if (processDefinition != null) {
            ProcessInfoDTO processInfoDTO = ProcessInfoDTO.builder()
                    .processDefinitionId(processDefinition.getId())
                    .processDefinitionKey(processDefinition.getKey())
                    .processDefinitionName(processDefinition.getName())
                    .name(processDefinition.getName())
                    // 添加更多流程定义信息
                    .id(processDefinition.getId())
                    .extensionProperties(ExtensionPropertyUtils.getExtensionProperties(processDefinition, repositoryService))
                    .build();

            processInstDTO.setProcessInfo(processInfoDTO);
            processInstDTO.setProcessTitle(processDefinition.getName());
        }

        return processInstDTO;
    }

    /**
     * 根据流程定义ID获取流程定义
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程定义对象
     */
    private ProcessDefinition getProcessDefinitionById(String processDefinitionId) {
        if (StrUtil.isBlank(processDefinitionId)) {
            return null;
        }

        try {
            return repositoryService.getProcessDefinition(processDefinitionId);
        } catch (Exception e) {
            log.warn("获取流程定义失败: processDefinitionId={}, error={}", processDefinitionId, e.getMessage());
            return null;
        }
    }

    private String compose(String... paths) {
        return ExtensionPropertyUtils.compose(paths);
    }
}