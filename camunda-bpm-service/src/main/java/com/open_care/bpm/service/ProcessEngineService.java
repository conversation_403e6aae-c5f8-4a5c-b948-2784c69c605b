/**
 * Copyright 2018-2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.reflect.TypeToken;
import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.common.ServiceException;
import com.open_care.api.common.dto.ActivityNodeDTO;
import com.open_care.api.common.dto.NodeSourceDTO;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.ProcessHistoryDTO;
import com.open_care.api.common.export.Page;
import com.open_care.api.dto.process.CompleteProcessTaskDTO;
import com.open_care.api.enums.SortType;
import com.open_care.bpm.constants.ProcessConst;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.bpm.entity.OCProcessdefinition;
import com.open_care.bpm.dto.*;
import com.open_care.bpm.enums.PriorityEnum;
import com.open_care.bpm.mapper.TaskMapper;
import com.open_care.bpm.repository.ProcessDefRepository;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.bpm.utils.PageUtils;
import com.open_care.bpm.utils.TaskQueryDTOUtils;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import com.open_care.util.json.JsonConverter;
import jakarta.ws.rs.core.Response;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.OptimisticLockingException;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.impl.util.IoUtil;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.rest.exception.RestException;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.runtime.VariableInstance;
import org.camunda.bpm.engine.task.Comment;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.task.TaskQuery;
import org.camunda.bpm.engine.variable.value.BooleanValue;
import org.camunda.bpm.engine.variable.value.StringValue;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaInputOutputImpl;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaPropertiesImpl;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaPropertyImpl;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.persistence.EntityManager;

import java.io.InputStream;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Log4j2
public class ProcessEngineService implements InitializingBean {

    /**
     * 查询任务候选人的本地 sql 的任务id参数名称
     */
    public static final String TASK_CANDIDATE_TASK_ID_PARAM_NAME = "taskIds";

    /**
     * 查询任务候选人的本地 sql
     */
    public static final String TASK_CANDIDATE_SQL = "SELECT \"TASK_ID_\",\"USER_ID_\",\"GROUP_ID_\" FROM \"ACT_RU_IDENTITYLINK\" WHERE \"TASK_ID_\" IN :taskIds AND (\"USER_ID_\" IS NOT NULL or \"GROUP_ID_\" IS NOT NULL) AND \"TYPE_\" = 'candidate';";


    @Autowired
    RuntimeService runtimeService;

    @Autowired
    TaskService taskService;

    @Autowired
    HistoryService historyService;

    @Autowired
    RemoteService remoteService;

    @Autowired
    RepositoryService repositoryService;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    ProcessDefRepository processDefRepository;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    EntityManager entityManager;

    @Autowired
    ConfigurationRemoteService configurationRemoteService;

    @Autowired
    private com.open_care.bpm.service.BpmnProcessUtils bpmnProcessUtils;

    @Autowired
    private TaskMapper taskMapper;

    public OcResponse startProcessInstanceByKey(String processKey, String body) {
        try {
            ProcessInstance processInstance;
            Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);

            if (variables.containsKey("businessKey") && StrUtil.isNotBlank(MapUtil.getStr(variables, "businessKey"))) {
                // 为例兼容之前的代码添加一个if else
                String businessKey = MapUtil.getStr(variables, "businessKey");
                variables.remove("businessKey");
                processInstance = runtimeService.startProcessInstanceByKey(processKey, businessKey, variables);
            } else if (variables.containsKey("entities")) {
                //取出变量中的实例ID
                // TODO：这里需要review 是否可以删除，改成统一的businessKey的字段实现
                List<Map> entities = (List) variables.get("entities");
                String entityInstId = entities.get(0).get("entityInstId").toString();
                processInstance = runtimeService.startProcessInstanceByKey(processKey, entityInstId, variables);
            } else {
                processInstance = runtimeService.startProcessInstanceByKey(processKey, variables);
            }
            ProcessInstanceDTO processInstanceDTO = new ProcessInstanceDTO(processInstance);
            // 代码优化，不确定是否会影响之前的代码，减少一个json转换，直接返回string
//            Map map = jsonConverter.fromJson(jsonConverter.toJson(processInstanceDTO), Map.class);
            return getSuccessStartedOcResponse(processInstanceDTO);
        } catch (Exception e) {
            log.error("提交失败", e);
            return getErrorStartedOcResponse(e.getMessage());
        }
    }

    public OcResponse startProcessInstanceById(String processDefinitionId, String body) {
        try {
            Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, variables);
            ProcessInstanceDTO processInstanceDTO = new ProcessInstanceDTO(processInstance);
            Map map = jsonConverter.fromJson(jsonConverter.toJson(processInstanceDTO), Map.class);
            return getSuccessStartedOcResponse(map);
        } catch (Exception e) {
            return getErrorStartedOcResponse(e.getMessage());
        }
    }

    @Transactional
    public OcResponse startProcessInstanceByKeyAndRunFirstTask(String processDefinitionKey, String body) {
        try {
            Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, variables);
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
            if (CollUtil.isNotEmpty(tasks)) {
                Task firstTask = tasks.get(0);
                // 使用TaskMapper
                TaskInfoDTO taskDTO = taskMapper.convertToDTO((TaskEntity) firstTask);
                return getSuccessStartedOcResponse(taskDTO);
            }
            return getSuccessStartedOcResponse(processInstance.getId());
        } catch (Exception e) {
            return getErrorStartedOcResponse(e.getMessage());
        }
    }

    public OcResponse startProcessInstanceByIdAndRunFirstTask(String processDefinitionId, String body) {
        try {
            Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, variables);
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
            if (CollUtil.isNotEmpty(tasks)) {
                Task firstTask = tasks.get(0);
                // 使用TaskMapper
                TaskInfoDTO taskDTO = taskMapper.convertToDTO((TaskEntity) firstTask);
                return getSuccessStartedOcResponse(taskDTO);
            }
            return getSuccessStartedOcResponse(processInstance.getId());
        } catch (Exception e) {
            return getErrorStartedOcResponse(e.getMessage());
        }
    }

    @Transactional
    public OcResponse claimTask(String taskId, String userId) {
        return claimTask(CollUtil.set(false, taskId), userId);
    }

    @Transactional
    public OcResponse claimTask(Set<String> taskIds, String userId) {
        try {
            doClaimTask(taskIds, userId);
            return CommonUtils.getOcResponse("申领任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("申领任务失败！", e.getMessage());
        }
    }

    public void doClaimTask(Set<String> taskIds, String userId) {
        for (String taskId : taskIds) {
            if (StringUtils.isEmpty(userId)) {
                throw new IllegalArgumentException("申领任务失败，任务申领人不能为空！");
            }
            taskService.claim(taskId, userId);
        }
    }

    @Transactional
    public OcResponse<Object> unclaimTask(String taskId) {
        return unclaimTask(CollUtil.set(false, taskId));
    }

    @Transactional
    public OcResponse<Object> unclaimTask(Set<String> taskIds) {
        try {
            doUnclaimTask(taskIds);
            return CommonUtils.getOcResponse("取消申领任务成功！", null);
        } catch (Exception e) {
            throw new IllegalStateException("取消申领任务失败!", e);
        }
    }

    @Transactional
    public void doUnclaimTask(Set<String> taskIds) {
        for (String taskId : taskIds) {
            taskService.setAssignee(taskId, null);

            BooleanValue isDelegatedValue = taskService.getVariableTyped(taskId, ProcessConst.TASK_VAR_IS_DELEGATED);
            String delegator = null;
            if (ObjectUtil.isNotNull(isDelegatedValue) && isDelegatedValue.getValue()) {
                StringValue delegatorValue = taskService.getVariableTyped(taskId, ProcessConst.TASK_VAR_DELEGATOR);
                delegator = ObjectUtil.isNotNull(delegatorValue) ? delegatorValue.getValue() : null;
            }
            if (StrUtil.isNotBlank(delegator)) {
                taskService.setAssignee(taskId, delegator);
                taskService.removeVariables(taskId, makeDelegatorVariableNames());
            }

        }
    }


    /**
     * 根据taskId获取任务相关的用户
     */
    @Transactional
    public OcResponse<List<IdentityEntityDTO>> queryIdentityEntityByTaskId(String taskId) {
        List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(taskId);
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        String businessKey = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).list().get(0).getBusinessKey();
        return CommonUtils.getOcResponse(identityLinksForTask.stream()
                .map(i -> taskMapper.convertToDTO(i, businessKey, task))
                .collect(Collectors.toList()));
    }

    private static List<String> makeDelegatorVariableNames() {
        return ListUtil.of(ProcessConst.TASK_VAR_IS_DELEGATED, ProcessConst.TASK_VAR_DELEGATOR);
    }

    private static Map<String, ? extends Serializable> makeDelegatorVariables(Task task) {
        return MapUtil.ofEntries(
                MapUtil.entry(ProcessConst.TASK_VAR_IS_DELEGATED, true),
                MapUtil.entry(ProcessConst.TASK_VAR_DELEGATOR, task.getAssignee())
        );
    }

    @Transactional
    public OcResponse candidateTask(String taskId, Map body) {
        try {
            List<String> userIds = jsonConverter.fromJson(jsonConverter.toJson(body.get("userIds")), new TypeToken<List<IdentityEntityDTO>>() {
            }.getType());
            if (CollUtil.isEmpty(userIds)) {
                return CommonUtils.getErrorOcResponse("指派任务失败，被指派人不能为空！");
            }
            taskService.setAssignee(taskId, null);
            userIds.stream().forEach(iter -> taskService.addCandidateUser(taskId, iter));
            return CommonUtils.getOcResponse("指派任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("指派任务失败！", e.getMessage());
        }
    }

    //    @Transactional
    public OcResponse delegateTask(Set<String> taskIds, String userId) {
        try {
            doDelegateTask(taskIds, userId);
            return CommonUtils.getOcResponse("委派任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("委派任务失败！", e.getMessage());
        }
    }

    @Transactional
    public OcResponse delegateTask(String taskId, String userId) {
        return delegateTask(CollUtil.set(false, taskId), userId);
    }

    public void doDelegateTask(Set<String> taskIds, String userId) {
        for (String taskId : taskIds) {
            if (StringUtils.isEmpty(userId)) {
                throw new IllegalArgumentException("委派任务失败，任务委派人不能为空！");
            }

            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

            if (ObjectUtil.isNull(task)) {
                throw new IllegalArgumentException(StrUtil.format("委派任务失败，任务 id {}未找到对应任务！", taskId));
            }

            if (ObjectUtil.isNotNull(task.getAssignee())) {
                taskService.setVariablesLocal(taskId, makeDelegatorVariables(task));
            }

            taskService.delegateTask(taskId, userId);
//            commonUtils.sendMessageByTaskId(taskId);
        }
    }

    @Transactional
    public OcResponse assigneeTask(Set<String> taskIds, String userId) {
        try {
            doAssigneeTask(taskIds, userId);
            return CommonUtils.getOcResponse("代理任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("代理任务失败！", e.getMessage());
        }
    }

    public void doAssigneeTask(Set<String> taskIds, String userId) {
        for (String taskId : taskIds) {
            if (StringUtils.isEmpty(userId)) {
                throw new IllegalArgumentException("代理任务失败，任务代理人不能为空！");
            }
            taskService.setAssignee(taskId, null);
            taskService.setAssignee(taskId, userId);
//            commonUtils.sendMessageByTaskId(taskId);
        }
    }

    @Transactional
    public OcResponse assigneeTask(String taskId, String userId) {
        return assigneeTask(CollUtil.set(false, taskId), userId);
    }

    @Transactional
    public OcResponse resolveTask(String taskId, @RequestBody String body) {
        try {
            Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);
            taskService.resolveTask(taskId, variables);
            return CommonUtils.getOcResponse("解决任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("解决任务失败！", e.getMessage());
        }
    }

    @Transactional
    public OcResponse completeTask(String taskId, @RequestBody Map<String, Object> variables) {
        try {
            doCompleteTask(taskId, variables);
            return CommonUtils.getOcResponse("完成任务！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("完成任务失败！", e.getMessage());
        }
    }

    public void doCompleteTask(String taskId, @RequestBody Map<String, Object> variables) {
        try {

            // 设置任务本地变量
            if (!variables.isEmpty()) {
                taskService.setVariablesLocal(taskId, variables);
            }
            
            if (variables.containsKey("comment")) {
                String processInstanceId = taskService.createTaskQuery().taskId(taskId).list().get(0).getProcessInstanceId();
                taskService.createComment(taskId, processInstanceId, variables.get("comment").toString());
            }

            taskService.complete(taskId);
        } catch (OptimisticLockingException e) {
            throw e;
        } catch (Exception e) {
            log.error("completeTaskByCompleteTaskDTO error", e);
            throw new IllegalStateException("complete task:" + taskId + " error", e);
        }
    }

    @Retryable(value = {OptimisticLockingException.class}, maxAttempts = 5)
    public OcResponse batchCompleteTasks(final List<CompleteProcessTaskDTO> completeTaskDTOList) {
        try {
            log.info("batchCompleteTasks completeTaskDTOList:{} ", () -> jsonConverter.toJson(completeTaskDTOList));
            completeTaskDTOList.forEach(completeProcessTaskDTO -> this.doCompleteTask(completeProcessTaskDTO.getTaskId(), completeProcessTaskDTO.getVariables()));
            return CommonUtils.getOcResponse("完成任务！", null);
        } catch (OptimisticLockingException e) { //发生乐观锁错误时重试
            log.error("batchCompleteTasks OptimisticLockingException", e);
            throw e;
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("完成任务失败！", e);
        }
    }

    public ResponseEntity<String> setTaskVariables(String taskId, @RequestBody String body) {
        log.error("set task variables:" + taskId + body);
        Map<String, Object> variables = jsonConverter.fromJson(body, Map.class);
        //taskService.setVariablesLocal(taskId, variables);
        taskService.setVariablesLocal(taskId, variables);

        List<Task> tasks = taskService.createTaskQuery().taskId(taskId).list();
        runtimeService.setVariables(tasks.get(0).getExecutionId(), variables);
        return new ResponseEntity<>("success", HttpStatus.OK);
    }

    @Recover
    public void recover(Exception e) {
        log.error("重试之后还是出现了异常，异常原因为", e);
    }

    public ResponseEntity<String> getCurrentTasksByProcDef(String processDefinitionId) {
        List<Task> activeTasks = taskService.createTaskQuery().processDefinitionId(processDefinitionId).list();

        return new ResponseEntity<>(jsonConverter.toJson(activeTasks), HttpStatus.OK);
    }

    //    @Transactional
    public ResponseEntity<String> getCurrentTasksByProcInst(String processInstanceId) {
        List<Task> activeTasks = taskService
                .createTaskQuery()

                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .desc()
                .list();

        List<TaskInfoDTO> taskInfoDTOS = getTasks(activeTasks);
        return new ResponseEntity<>(jsonConverter.toJson(taskInfoDTOS), HttpStatus.OK);
    }

    public List<Task> getCurrentTasksByUser(Collection<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        ArrayList<Task> allActiveTasks = new ArrayList<>();

        List<Task> activeTasks = userIds.stream()
                .map(iter -> taskService.createTaskQuery().taskCandidateUser(iter).list())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        allActiveTasks.addAll(activeTasks);
        allActiveTasks.addAll(taskService.createTaskQuery().taskAssigneeIn(userIds.toArray(new String[0])).list());
        return allActiveTasks;
    }

    //    @Transactional
    public List<TaskInfoDTO> findUserTasksByUserIds(Collection<String> userIds, SortType createTimeSortType) {
        if (CollUtil.isEmpty(userIds)) {
            return ListUtil.empty();
        }
        TaskQuery taskQuery = getTaskQueryFromUserIds(userIds);
        if (ObjUtil.isNotNull(createTimeSortType)) {
            taskQuery = ObjUtil.equals(createTimeSortType, SortType.ASC) ? taskQuery.orderByTaskCreateTime().asc() : taskQuery.orderByTaskCreateTime().desc();
        }
        return findTaskQueryPageResult(taskQuery, null);
    }

    public long findUserTasksCountByUserId(Collection<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return 0L;
        }

        return ObjUtil.defaultIfNull(getTaskQueryFromUserIds(userIds).count(), 0L);
    }

    private TaskQuery getTaskQueryFromUserIds(Collection<String> userIds) {
        TaskQuery taskQuery = createTaskQuery();
        if (CollUtil.isEmpty(userIds)) {
            return taskQuery;
        }
        taskQuery = taskQuery.or().taskAssigneeIn(userIds.toArray(new String[0]));
        for (String userId : userIds) {
            taskQuery = taskQuery.taskCandidateUser(userId);
        }
        return taskQuery.endOr();
    }

    @Retryable(include = {OptimisticLockingException.class, NullPointerException.class}, maxAttempts = 5)
//    @Transactional
    public List<TaskInfoDTO> getCurrentTaskInfoDTOByUser(Collection<String> userIds) {
        List<Task> activeTasks = getCurrentTasksByUser(userIds);
        List<TaskInfoDTO> tasks = getTasks(activeTasks);
        Collections.sort(tasks);
        return tasks;
    }

    public ResponseEntity<String> getCurrentTasksByUsers(Collection<String> userIds) {
        List<TaskInfoDTO> tasks = getCurrentTaskInfoDTOByUser(userIds);
        return new ResponseEntity<>(jsonConverter.toJson(tasks), HttpStatus.OK);
    }

    @Transactional
    public Pair<Long, List<TaskInfoDTO>> findUserTasks(TaskQueryRequestDTO queryDTO) {
        List<TaskInfoDTO> taskInfoDTOList = findTaskQueryPageResult(TaskQueryDTOUtils.getUserTasksQuery(queryDTO, createTaskQuery()), queryDTO.getPage());
        Long totalCount = TaskQueryDTOUtils.getUserTasksQuery(queryDTO, createTaskQuery()).count();
        return Pair.of(totalCount, taskInfoDTOList);
    }

    @Transactional
    public Pair<Long, List<TaskInfoDTO>> findUserTasksHistory(TaskHistoryQueryRequestDTO queryDTO) {
        // 创建历史任务查询
        org.camunda.bpm.engine.history.HistoricTaskInstanceQuery historicTaskQuery = historyService.createHistoricTaskInstanceQuery();

        // 根据查询条件设置查询参数
        if (StrUtil.isNotBlank(queryDTO.getProcessDefinitionId())) {
            historicTaskQuery.processDefinitionId(queryDTO.getProcessDefinitionId());
        }

        if (StrUtil.isNotBlank(queryDTO.getProcessDefinitionKey())) {
            historicTaskQuery.processDefinitionKey(queryDTO.getProcessDefinitionKey());
        }

        if (StrUtil.isNotBlank(queryDTO.getProcessInstanceId())) {
            historicTaskQuery.processInstanceId(queryDTO.getProcessInstanceId());
        }

        if (StrUtil.isNotBlank(queryDTO.getTaskId())) {
            historicTaskQuery.taskId(queryDTO.getTaskId());
        }

        if (StrUtil.isNotBlank(queryDTO.getAssignee())) {
            historicTaskQuery.taskAssignee(queryDTO.getAssignee());
        }

        if (ObjUtil.isNotNull(queryDTO.getBusinessKeys()) && CollUtil.isNotEmpty(queryDTO.getBusinessKeys())) {
            org.camunda.bpm.engine.history.HistoricTaskInstanceQuery orQuery = historicTaskQuery.or();
            for (String businessKey : queryDTO.getBusinessKeys()) {
                orQuery.processInstanceBusinessKeyLike(businessKey);
            }
            historicTaskQuery = orQuery.endOr();
        }

        // 设置排序
        if (ObjUtil.isNotNull(queryDTO.getCreateTimeSortType())) {
            historicTaskQuery.orderByHistoricActivityInstanceStartTime();
            if (ObjUtil.equals(queryDTO.getCreateTimeSortType(), SortType.ASC)) {
                historicTaskQuery.asc();
            } else {
                historicTaskQuery.desc();
            }
        } else {
            // 默认按开始时间倒序排列
            historicTaskQuery.orderByHistoricActivityInstanceStartTime().desc();
        }

        // 添加租户ID限制
//        historicTaskQuery.tenantIdIn(AppInfoContext.getApp().getAppInstId());

        // 计算总数
        Long totalCount = historicTaskQuery.count();

        // 分页查询
        List<HistoricTaskInstance> historicTaskInstances;
        if (PageUtils.isSupportedPagination(queryDTO.getPage())) {
            historicTaskInstances = historicTaskQuery.listPage(
                    PageUtil.getStart(queryDTO.getPage().getCurrent() - 1, queryDTO.getPage().getPageSize()),
                    queryDTO.getPage().getPageSize()
            );
        } else {
            historicTaskInstances = historicTaskQuery.list();
        }

        // 转换为TaskInfoDTO
        List<TaskInfoDTO> taskInfoDTOList = getTasksByHistoryTask(historicTaskInstances);

        return Pair.of(totalCount, taskInfoDTOList);
    }

    private List<TaskInfoDTO> findTaskQueryPageResult(TaskQuery taskQuery, Page page) {

        if (!PageUtils.isSupportedPagination(page)) {
            return getTasks(taskQuery.list());
        }

        return getTasks(taskQuery.listPage(PageUtil.getStart(page.getCurrent() - 1, page.getPageSize()), page.getPageSize()));
    }

    private TaskQuery createTaskQuery() {
        return taskService.createTaskQuery();
    }

    public ResponseEntity<String> getHistoryTasksByUserAndProcDef(String userId, String processDefinitionId) {
        List<HistoricTaskInstance> historyTasks = historyService
                .createHistoricTaskInstanceQuery()
                .processDefinitionId(processDefinitionId)
                .taskAssignee(userId)
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();

        List<TaskInfoDTO> tasks = getTasksByHistoryTask(historyTasks);
        return new ResponseEntity<>(jsonConverter.toJson(tasks), HttpStatus.OK);
    }

    /**
     * 获取流程实例图及执行历史
     *
     * @param processInstanceId 流程实例ID
     * @return 流程历史信息
     */
    public OcResponse<ProcessHistoryDTO> getHistoryTasksByProcInst(String processInstanceId) {
        try {
            // 查找流程定义ID
            String processDefinitionId = findProcessDefinitionIdByProcessInstanceId(processInstanceId);
            if (StrUtil.isBlank(processDefinitionId)) {
                return CommonUtils.getErrorOcResponse("根据传入的流程实例id未找到流程实例", null);
            }

            // 创建并填充结果DTO
            ProcessHistoryDTO result = new ProcessHistoryDTO();

            // 获取历史任务
            List<HistoricTaskInstance> historyTasks = findHistoricTasksByProcessInstanceId(processInstanceId);
            result.setTasks(getTasksByHistoryTask(historyTasks));

            // 加载流程XML
            loadProcessModelXml(processDefinitionId, result);

            // 提取已完成任务列表
            extractCompletedTasks(historyTasks, result);

            // 获取当前任务列表
            extractCurrentTasks(processInstanceId, result);

            // 提取流程活动节点信息，使用审计日志API来确定节点关系
            extractActivityNodes(processInstanceId, processDefinitionId, result);

            return new OcResponse(result);
        } catch (Exception e) {
            log.error("获取流程实例历史信息失败", e);
            return CommonUtils.getErrorOcResponse("获取流程实例历史信息失败: " + e.getMessage(), null);
        }
    }

    /**
     * 根据流程实例ID查找流程定义ID
     *
     * @param processInstanceId 流程实例ID
     * @return 流程定义ID
     */
    private String findProcessDefinitionIdByProcessInstanceId(String processInstanceId) {
        return historyService
                .createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .list()
                .stream()
                .map(HistoricProcessInstance::getProcessDefinitionId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据流程实例ID查找历史任务
     *
     * @param processInstanceId 流程实例ID
     * @return 历史任务列表
     */
    private List<HistoricTaskInstance> findHistoricTasksByProcessInstanceId(String processInstanceId) {
        return historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();
    }

    /**
     * 加载流程模型XML
     *
     * @param processDefinitionId 流程定义ID
     * @param result              结果DTO
     */
    private void loadProcessModelXml(String processDefinitionId, ProcessHistoryDTO result) {
        try {
            InputStream processModelIn = repositoryService.getProcessModel(processDefinitionId);
            byte[] processModel = IoUtil.readInputStream(processModelIn, "processModelBpmn20Xml");
            result.setXml(new String(processModel, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException("读取流程模型XML失败", e);
        }
    }

    /**
     * 提取已完成任务列表
     *
     * @param historyTasks 历史任务列表
     * @param result       结果DTO
     */
    private void extractCompletedTasks(List<HistoricTaskInstance> historyTasks, ProcessHistoryDTO result) {
        historyTasks.stream()
                .filter(historyTask -> ObjectUtil.isNotNull(historyTask.getEndTime()))
                .forEach(task -> result.getCompleteTasks().add(task.getTaskDefinitionKey()));
    }

    /**
     * 提取当前任务列表
     *
     * @param processInstanceId 流程实例ID
     * @param result            结果DTO
     */
    private void extractCurrentTasks(String processInstanceId, ProcessHistoryDTO result) {
        List<Task> activeTasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        activeTasks.forEach(task -> result.getCurrentTasks().add(task.getTaskDefinitionKey()));
    }

    /**
     * 提取流程活动节点信息，使用审计日志API来确定节点关系
     *
     * @param processInstanceId   流程实例ID
     * @param processDefinitionId 流程定义ID
     * @param result              结果DTO
     */
    private void extractActivityNodes(String processInstanceId, String processDefinitionId, ProcessHistoryDTO result) {
        // 获取流程模型实例以获取真实的流程结构信息
        BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(processDefinitionId);

        // 从模型中获取所有连接线ID
        List<String> sequenceFlows = extractSequenceFlowIds(modelInstance);
        result.setSequenceFlows(sequenceFlows);

        // 使用审计日志API获取所有历史活动实例，按执行顺序排序
        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();

        // 将历史活动实例转换为DTO并获取执行ID映射
        Pair<List<ActivityNodeDTO>, Map<String, String>> conversionResult = convertToActivityNodeDTOs(historicActivityInstances);
        List<ActivityNodeDTO> activityNodes = conversionResult.getKey();
        Map<String, String> nodeExecutionMap = conversionResult.getValue();

        // 通过实例过程直接构建节点关系，利用执行ID
        buildNodeRelationshipsWithExecutionId(activityNodes, nodeExecutionMap, modelInstance);

        // 设置结果
        result.setActivities(activityNodes);
    }

    /**
     * 基于活动序列和执行ID构建节点关系
     *
     * @param activityNodes    活动节点列表，已按时间排序
     * @param nodeExecutionMap 节点ID到执行ID的映射
     * @param modelInstance    BPMN模型实例
     */
    private void buildNodeRelationshipsWithExecutionId(
            List<ActivityNodeDTO> activityNodes,
            Map<String, String> nodeExecutionMap,
            BpmnModelInstance modelInstance) {

        // 首先创建节点ID到节点的映射
        Map<String, ActivityNodeDTO> nodeMap = activityNodes.stream()
                .collect(Collectors.toMap(
                        ActivityNodeDTO::getActivityId,
                        node -> node,
                        (a, b) -> a.getStartTime().compareTo(b.getStartTime()) > 0 ? a : b));

        // 从模型获取静态连接关系
        Map<String, List<NodeSourceDTO>> staticConnectionsMap = buildStaticNodeConnectionsMap(modelInstance);

        // 构建执行ID到节点列表的映射，用于跟踪执行流
        Map<String, List<ActivityNodeDTO>> executionNodesMap = new HashMap<>();
        for (ActivityNodeDTO node : activityNodes) {
            String executionId = nodeExecutionMap.get(node.getActivityId());
            if (StrUtil.isNotBlank(executionId)) {
                if (!executionNodesMap.containsKey(executionId)) {
                    executionNodesMap.put(executionId, new ArrayList<>());
                }
                executionNodesMap.get(executionId).add(node);
            }
        }

        // 对每个执行流中的节点按时间排序
        for (List<ActivityNodeDTO> executionNodes : executionNodesMap.values()) {
            executionNodes.sort(Comparator.comparing(ActivityNodeDTO::getStartTime));
        }

        // 为每个节点构建来源关系
        for (ActivityNodeDTO node : activityNodes) {
            String activityId = node.getActivityId();
            String executionId = nodeExecutionMap.get(activityId);

            // 使用静态模型连接作为基础
            List<NodeSourceDTO> sources = new ArrayList<>();
            if (staticConnectionsMap.containsKey(activityId)) {
                sources.addAll(staticConnectionsMap.get(activityId));
            }

            // 添加来自相同执行流的前驱节点（基于执行ID）
            if (StrUtil.isNotBlank(executionId) && executionNodesMap.containsKey(executionId)) {
                List<ActivityNodeDTO> executionNodes = executionNodesMap.get(executionId);
                int currentIndex = executionNodes.indexOf(node);

                // 如果不是执行流中的第一个节点，则前一个节点是其来源
                if (currentIndex > 0) {
                    ActivityNodeDTO predecessor = executionNodes.get(currentIndex - 1);
                    String flowId = findFlowIdBetweenNodes(predecessor.getActivityId(), activityId, staticConnectionsMap);

                    if (StrUtil.isNotBlank(flowId)) {
                        // 添加到来源列表，确保不重复
                        boolean exists = sources.stream()
                                .anyMatch(s -> s.getSourceId().equals(predecessor.getActivityId()));

                        if (!exists) {
                            sources.add(new NodeSourceDTO(
                                    flowId,
                                    predecessor.getActivityId(),
                                    predecessor.getActivityName()));
                        }
                    }
                }
            }

            // 如果找不到执行流中的前驱，则尝试使用时间关系推断
            if (sources.isEmpty() || sources.stream().noneMatch(s -> nodeMap.containsKey(s.getSourceId()))) {
                addSourcesBasedOnTiming(node, activityNodes, staticConnectionsMap, sources, nodeMap);
            }

            // 如果找到了来源，设置到节点
            if (!sources.isEmpty()) {
                node.setIncomingSources(sources);

                // 选择最合适的来源作为主要来源（向后兼容）
                NodeSourceDTO primarySource = selectPrimarySource(node, sources, nodeMap);
                if (primarySource != null) {
                    node.setPreviousActivityId(primarySource.getSourceId());
                    node.setIncomingFlowId(primarySource.getFlowId());

                    ActivityNodeDTO sourceNode = nodeMap.get(primarySource.getSourceId());
                    if (sourceNode != null) {
                        node.setPreviousActivityName(sourceNode.getActivityName());
                    } else {
                        node.setPreviousActivityName(primarySource.getSourceName());
                    }
                }
            }
        }
    }

    /**
     * 基于时间关系添加可能的来源节点
     *
     * @param node                 当前节点
     * @param allNodes             所有节点
     * @param staticConnectionsMap 静态连接关系
     * @param sources              来源列表（将被修改）
     * @param nodeMap              节点ID到节点的映射
     */
    private void addSourcesBasedOnTiming(
            ActivityNodeDTO node,
            List<ActivityNodeDTO> allNodes,
            Map<String, List<NodeSourceDTO>> staticConnectionsMap,
            List<NodeSourceDTO> sources,
            Map<String, ActivityNodeDTO> nodeMap) {

        String activityId = node.getActivityId();

        // 找出时间上接近的前驱节点
        for (ActivityNodeDTO potentialPredecessor : allNodes) {
            if (potentialPredecessor == node) continue;

            String predecessorId = potentialPredecessor.getActivityId();

            // 如果前驱节点已结束，且结束时间接近当前节点的开始时间，认为有可能是其前驱
            if (potentialPredecessor.getEndTime() != null &&
                    node.getStartTime() != null &&
                    isTimingConsistentWithFlow(potentialPredecessor.getEndTime(), node.getStartTime())) {

                // 检查是否在模型中存在连接
                String flowId = findFlowIdBetweenNodes(predecessorId, activityId, staticConnectionsMap);
                if (StrUtil.isNotBlank(flowId)) {
                    // 检查是否已存在
                    boolean exists = sources.stream()
                            .anyMatch(s -> s.getSourceId().equals(predecessorId));

                    if (!exists) {
                        sources.add(new NodeSourceDTO(
                                flowId,
                                predecessorId,
                                potentialPredecessor.getActivityName()));
                    }
                }
            }
        }
    }

    /**
     * 判断时间关系是否合理（前驱节点结束后当前节点立即或很快开始）
     *
     * @param predecessorEndTime 前驱节点结束时间
     * @param currentStartTime   当前节点开始时间
     * @return 是否时间关系合理
     */
    private boolean isTimingConsistentWithFlow(Date predecessorEndTime, Date currentStartTime) {
        if (predecessorEndTime == null || currentStartTime == null) {
            return false;
        }

        // 计算时间差（毫秒）
        long timeDiff = currentStartTime.getTime() - predecessorEndTime.getTime();

        // 设置合理的时间窗口（例如，5秒以内认为是合理的流转）
        return timeDiff >= 0 && timeDiff <= 5000; // 5秒的时间窗口
    }

    /**
     * 提取连接线ID列表
     *
     * @param modelInstance BPMN模型实例
     * @return 连接线ID列表
     */
    private List<String> extractSequenceFlowIds(BpmnModelInstance modelInstance) {
        return modelInstance.getModelElementsByType(SequenceFlow.class)
                .stream()
                .map(SequenceFlow::getId)
                .collect(Collectors.toList());
    }

    private List<TaskInfoDTO> getTasksByHistoryTask(List<HistoricTaskInstance> historicTaskInstances) {
        if (CollUtil.isEmpty(historicTaskInstances)) {
            return CollUtil.newArrayList();
        }

        // 收集任务ID和流程实例ID
        List<String> taskIds = historicTaskInstances.stream().map(HistoricTaskInstance::getId).collect(Collectors.toList());
        List<String> processInstIds = historicTaskInstances.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toList());

        // 获取任务候选人信息
        Map<String, TaskCandidates> taskIdCandidateUserMap = queryTaskCandidateUsersByTaskIds(taskIds);

        // 获取流程实例信息
        Map<String, HistoricProcessInstance> processInstanceMap = getHistoryProcessInstanceIdToEntityMap(processInstIds);

        // 一次性查询所有任务的变量，减少数据库查询次数
        Map<String, Map<String, Object>> taskVariablesMap = new HashMap<>();
        List<HistoricVariableInstance> taskVariables = historyService
                .createHistoricVariableInstanceQuery()
                .taskIdIn(taskIds.toArray(new String[0]))
                .list();

        // 通过stream流构造任务ID到变量的映射
        if (CollUtil.isNotEmpty(taskVariables)) {
            taskVariablesMap = taskVariables.stream()
                    .filter(variable -> StrUtil.isNotBlank(variable.getTaskId()))
                    .filter(variable -> StrUtil.isNotBlank(variable.getName()))
                    .filter(variable -> ObjectUtil.isNotNull(variable.getValue()))
                    .collect(Collectors.groupingBy(
                            HistoricVariableInstance::getTaskId,
                            Collectors.toMap(
                                    HistoricVariableInstance::getName,
                                    HistoricVariableInstance::getValue,
                                    (v1, v2) -> v1 // 如果出现重复的键，保留第一个值
                            )
                    ));
        }

        // 一次性查询所有流程的变量，减少数据库查询次数
        Map<String, Map<String, Object>> processVariablesMap = new HashMap<>();
        List<HistoricVariableInstance> processVariables = historyService
                .createHistoricVariableInstanceQuery()
                .processInstanceIdIn(processInstIds.toArray(new String[0]))
                .list();

        // 通过stream流构造流程实例ID到变量的映射
        if (CollUtil.isNotEmpty(processVariables)) {
            processVariablesMap = processVariables.stream()
                    .filter(variable -> StrUtil.isNotBlank(variable.getProcessInstanceId()) && StrUtil.isBlank(variable.getTaskId()))
                    .filter(variable -> StrUtil.isNotBlank(variable.getName()))
                    .filter(variable -> ObjectUtil.isNotNull(variable.getValue()))
                    .collect(Collectors.groupingBy(
                            HistoricVariableInstance::getProcessInstanceId,
                            Collectors.toMap(
                                    HistoricVariableInstance::getName,
                                    HistoricVariableInstance::getValue,
                                    (v1, v2) -> v1 // 如果出现重复的键，保留第一个值
                            )
                    ));
        }

        // 预先获取所有流程定义信息
        Map<String, ProcessDefinition> processDefinitionMap = new HashMap<>();
        Set<String> processDefinitionIds = processInstanceMap.values().stream()
                .map(HistoricProcessInstance::getProcessDefinitionId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(processDefinitionIds)) {
            repositoryService.createProcessDefinitionQuery()
                    .processDefinitionIdIn(processDefinitionIds.toArray(new String[0]))
                    .list()
                    .forEach(definition -> processDefinitionMap.put(definition.getId(), definition));
        }

        // 使用TaskMapper批量转换
        return taskMapper.convertHistoricTasksToDTOList(
                historicTaskInstances,
                taskIdCandidateUserMap,
                processInstanceMap,
                processDefinitionMap,
                taskVariablesMap,
                processVariablesMap
        );
    }

    //获取任务委托人列表
    public ResponseEntity<String> getTaskDelegateUsersByTaskId(String taskId) {
        String users = "";
        if (Objects.nonNull(taskService.getVariables(taskId).get("taskDelegateUsers"))) {
            String delegateUsers = taskService.getVariables(taskId).get("taskDelegateUsers").toString();
            users = remoteService.getUsersByCondition(delegateUsers);
        }
        return new ResponseEntity<>(users, HttpStatus.OK);
    }

    //获取任务指派人列表
    public ResponseEntity<String> getTaskAssigneeUsersByTaskId(String taskId) {
        String users = "";
        if (Objects.nonNull(taskService.getVariables(taskId).get("taskAssigneeUsers"))) {
            String assigneeUsers = taskService.getVariables(taskId).get("taskAssigneeUsers").toString();
            users = remoteService.getUsersByCondition(assigneeUsers);
        }
        return new ResponseEntity<>(users, HttpStatus.OK);
    }

    public List<TaskDefinitionDTO> getTaskDefByProcessDefinition(String processDefinitionId) {
        List<TaskDefinitionDTO> taskDefinitionDTOS = new ArrayList<>();
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(processDefinitionId);
        Collection<UserTask> userTasks = bpmnModelInstance.getModelElementsByType(UserTask.class);
        userTasks.stream().forEach(iter -> {
            TaskDefinitionDTO taskDefinitionDTO = new TaskDefinitionDTO();
            taskDefinitionDTO.setTaskDefinitionId(iter.getId());
            taskDefinitionDTO.setTaskDefinitionName(iter.getName());
            iter.getExtensionElements().getElements().stream().forEach(element -> {
                if (element.getElementType().getInstanceType().equals(CamundaInputOutput.class)) {
                    ((CamundaInputOutputImpl) element).getCamundaInputParameters().stream().forEach(parameter -> {
                        ParameterDTO parameterDTO = new ParameterDTO();
                        parameterDTO.setName(parameter.getCamundaName());
                        taskDefinitionDTO.getParameters().add(parameterDTO);
                    });
                } else if (element.getElementType().getInstanceType().equals(CamundaProperties.class)) {
                    ((CamundaPropertiesImpl) element).getCamundaProperties().stream().forEach(property -> {
                        PropertyDTO propertyDTO = new PropertyDTO();
                        propertyDTO.setName(((CamundaPropertyImpl) property).getCamundaName());
                        taskDefinitionDTO.getProperties().add(propertyDTO);
                    });
                }
            });
            taskDefinitionDTOS.add(taskDefinitionDTO);
        });
        return taskDefinitionDTOS;
    }

    public String getProcessInstances() {
        List<ProcessInstance> processInstances = runtimeService
                .createProcessInstanceQuery()
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .list();

        List<ProcessInstanceDTO> processInstanceDTOS = new ArrayList<>();
        processInstances.stream().forEach(iter -> {
            ProcessInstanceDTO processInstanceDTO = new ProcessInstanceDTO(iter);
            processInstanceDTOS.add(processInstanceDTO);
        });
        return jsonConverter.toJson(processInstanceDTOS);
    }

    public String clearProcessInstances() {
        List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().list();
        processInstances.stream().forEach(iter -> {
            runtimeService.deleteProcessInstance(iter.getId(), "");
        });
        return "success";
    }

    public List<ProcessDefinitionDTO> getProcessDefinitions() {
        List<ProcessDefinitionDTO> processDefinitionDTOS = new ArrayList<>();
        List<ProcessDefinition> processDefinitions = repositoryService
                .createProcessDefinitionQuery()
                .latestVersion()
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .list();

        processDefinitions.stream().forEach(iter -> {
            ProcessDefinitionDTO processDefinitionDTO = new ProcessDefinitionDTO();
            processDefinitionDTO.setProcessDefinitionId(iter.getKey());
            List<OCProcessdefinition> processdefinitions = processDefRepository.findByprocDefId(iter.getDeploymentId());
            if (processdefinitions != null && processdefinitions.size() > 0) {
                processDefinitionDTO.setProcessDefinitionName(processdefinitions.get(0).getName() + ",版本：" + iter.getVersion());
            } else {
                processDefinitionDTO.setProcessDefinitionName(iter.getName() + ",版本：" + iter.getVersion());
            }
            BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(iter.getId());
            //Collection<CamundaPropertiesImpl> properties = bpmnModelInstance.getModelElementsByType(CamundaPropertiesImpl.class);
            Collection<ExtensionElements> extensionElements = bpmnModelInstance.getModelElementsByType(ExtensionElements.class);
            extensionElements.stream().forEach(extensionElements1 -> {
                extensionElements1.getElements().stream().forEach(element -> {
                    if (element.getElementType().getInstanceType().equals(CamundaProperties.class)) {
                        ((CamundaPropertiesImpl) element).getCamundaProperties().stream().forEach(property -> {
                            PropertyDTO propertyDTO = new PropertyDTO();
                            propertyDTO.setName(((CamundaPropertyImpl) property).getCamundaName());
                            processDefinitionDTO.getProperties().add(propertyDTO);
                        });

                    }
                });
            });

            processDefinitionDTOS.add(processDefinitionDTO);
        });

        return processDefinitionDTOS;
    }

    /**
     * 流程跳转到指定节点
     *
     * @return
     */
    public OcResponse<?> processModification(ProcessModificationDTO processModificationDTO) {
        String processInstanceId = processModificationDTO.getProcessInstanceId();
        String targetActivityId = processModificationDTO.getTargetActivityId();

        ProcessInstanceModificationBuilder modificationBuilder = runtimeService.createProcessInstanceModification(processInstanceId);
        if (StrUtil.isNotBlank(processModificationDTO.getCurrentActivityId())) {
            modificationBuilder.cancelAllForActivity(processModificationDTO.getCurrentActivityId());
        }
        modificationBuilder.startBeforeActivity(targetActivityId);
        modificationBuilder.execute();

        // 设置流程变量
        if (CollUtil.isNotEmpty(processModificationDTO.getVariables())) {
            runtimeService.setVariables(processInstanceId, processModificationDTO.getVariables());
        }
        return CommonUtils.getOcResponse("流程跳转节点成功！", null);
    }

    public OcResponse<TaskInfoDTO> getTaskByTaskId(String taskId) {
        Task task = taskService
                .createTaskQuery()
                .taskId(taskId)
                .singleResult();

        TaskInfoDTO taskDTO = CollUtil.getFirst(getTasks(ObjectUtil.isNull(task) ? ListUtil.empty() : ListUtil.toList(task)));

        // 获取任务的扩展属性
        if (ObjectUtil.isNotNull(task)) {
            Map<String, Object> extensionProperties = getTaskExtensionProperties(task);
            taskDTO.setExtensionProperties(extensionProperties);
            taskDTO.setProperties(ExtensionPropertyUtils.generateProperties(extensionProperties));
        }

        return new OcResponse<>(taskDTO, "0");
    }

    /**
     * 获取任务的扩展属性
     *
     * @param task 任务
     * @return 扩展属性Map
     */
    private Map<String, Object> getTaskExtensionProperties(Task task) {
        Map<String, Object> extensionProperties = new HashMap<>();

        try {
            String processDefinitionId = task.getProcessDefinitionId();
            String taskDefinitionKey = task.getTaskDefinitionKey();

            // 获取BPMN模型实例
            BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(processDefinitionId);
            if (ObjectUtil.isNull(modelInstance)) {
                return extensionProperties;
            }

            // 获取当前任务的UserTask元素
            UserTask userTask = modelInstance.getModelElementsByType(UserTask.class)
                    .stream()
                    .filter(ut -> StrUtil.equals(ut.getId(), taskDefinitionKey))
                    .findFirst()
                    .orElse(null);

            if (ObjectUtil.isNull(userTask) || ObjectUtil.isNull(userTask.getExtensionElements())) {
                return extensionProperties;
            }

            // 获取CamundaProperties类型的扩展属性
            userTask.getExtensionElements()
                    .getElementsQuery()
                    .filterByType(CamundaProperties.class)
                    .list()
                    .stream()
                    .map(props -> (CamundaPropertiesImpl) props)
                    .forEach(camundaProps -> camundaProps.getCamundaProperties()
                            .stream()
                            .map(prop -> (CamundaPropertyImpl) prop)
                            .forEach(camundaProp -> extensionProperties.put(
                                    camundaProp.getCamundaName(),
                                    camundaProp.getCamundaValue()))
                    );
        } catch (Exception e) {
            log.error("获取任务扩展属性失败", e);
        }

        return extensionProperties;
    }

    public String getUserTaskDefinitions() {
        List<TaskDefinitionDTO> taskDefinitionDTOS = new ArrayList<>();
        List<ProcessDefinitionDTO> processDefinitionDTOS = getProcessDefinitions();
        for (ProcessDefinitionDTO iter : processDefinitionDTOS) {
            taskDefinitionDTOS.addAll(getTaskDefByProcessDefinition(iter.getProcessDefinitionId()));
        }
        return jsonConverter.toJson(taskDefinitionDTOS);
    }

    public List<TaskInfoDTO> getTasks(List<Task> activeTasks) {
        if (CollUtil.isEmpty(activeTasks)) {
            return CollUtil.newArrayList();
        }

        // 获取任务候选人信息
        Map<String, TaskCandidates> taskIdCandidateUserMap = queryTaskCandidateUsersByTaskIds(activeTasks);

        // 收集所有流程实例ID
        Set<String> processInstanceIds = getProcessInstanceIds(activeTasks);

        // 预先获取所有流程实例信息
        Map<String, ProcessInstance> processInstanceMap = getProcessInstanceIdToEntityMap(processInstanceIds);

        // 获取流程定义信息
        Map<String, ProcessDefinition> processDefinitionMap = getProcessDefinitionIdToEntityMapByProcessInstanceMap(processInstanceMap);

        // 获取流程变量
        Map<String, Map<String, Object>> processVariablesMap = getVariablesMap(activeTasks.stream().map(Task::getProcessInstanceId).collect(Collectors.toSet()));

        // 获取流程变量

        Map<String, Map<String, Object>> taskVariablesMap = activeTasks.stream()
                .map(Task::getId)
                .distinct()
                .map(taskId -> Pair.of(taskId, taskService.getVariables(taskId)))
                .filter(pair -> ObjectUtil.isNotNull(pair.getValue()))
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (a, b) -> a));

        // 使用TaskMapper批量转换
        return taskMapper.convertToDTOList(
                activeTasks,
                taskIdCandidateUserMap,
                processInstanceMap,
                processDefinitionMap,
                processVariablesMap,
                taskVariablesMap
        );
    }

    private Map<String, Map<String, Object>> getVariablesMap(Set<String> processInstanceIds) {
        // 查询流程变量
        Map<String, Map<String, Object>> processVariablesMap = new HashMap<>();
        if (CollUtil.isNotEmpty(processInstanceIds)) {
            List<VariableInstance> processVariableInstances = runtimeService.createVariableInstanceQuery()
                    .processInstanceIdIn(processInstanceIds.toArray(new String[0]))
                    .list();

            // 按流程实例ID分组变量
            Map<String, List<VariableInstance>> processIdToVariables = processVariableInstances.stream()
                    .collect(Collectors.groupingBy(VariableInstance::getProcessInstanceId));

            // 转换为变量Map
            processIdToVariables.forEach((processId, variables) -> {
                Map<String, Object> varMap = variables.stream()
                        .filter(ObjectUtil::isNotNull)
                        .filter(variable -> ObjectUtil.isNotNull(variable.getName()))
                        .filter(variable -> ObjectUtil.isNotNull(variable.getValue()))
                        // Collectors.toMap 不允许 key 或者 value 为 null
                        .collect(Collectors.toMap(
                                VariableInstance::getName,
                                VariableInstance::getValue,
                                (v1, v2) -> v1
                        ));
                processVariablesMap.put(processId, varMap);
            });
        }
        return processVariablesMap;
    }

    private Map<String, ProcessDefinition> getProcessDefinitionIdToEntityMapByProcessInstanceMap(Map<String, ProcessInstance> processInstanceMap) {
        // 收集所有流程定义ID
        Set<String> processDefinitionIds = processInstanceMap.values().stream()
                .map(ProcessInstance::getProcessDefinitionId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 预先获取所有流程定义信息
        Map<String, ProcessDefinition> processDefinitionMap = new HashMap<>();
        if (CollUtil.isNotEmpty(processDefinitionIds)) {
            repositoryService.createProcessDefinitionQuery()
                    .processDefinitionIdIn(processDefinitionIds.toArray(new String[0]))
                    .list()
                    .forEach(definition -> processDefinitionMap.put(definition.getId(), definition));
        }
        return processDefinitionMap;
    }

    private Map<String, ProcessInstance> getProcessInstanceIdToEntityMap(Set<String> processInstanceIds) {
        Map<String, ProcessInstance> processInstanceMap = new HashMap<>();
        if (CollUtil.isNotEmpty(processInstanceIds)) {
            runtimeService.createProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .list()
                    .forEach(instance -> processInstanceMap.put(instance.getId(), instance));
        }
        return processInstanceMap;
    }

    private Map<String, HistoricProcessInstance> getHistoryProcessInstanceIdToEntityMap(Collection<String> processInstIds) {
        Map<String, HistoricProcessInstance> processInstanceMap = new HashMap<>();
        if (CollUtil.isNotEmpty(processInstIds)) {
            historyService.createHistoricProcessInstanceQuery()
                    .processInstanceIds(CollUtil.newHashSet(processInstIds))
                    .list()
                    .forEach(instance -> processInstanceMap.put(instance.getId(), instance));
        }
        return processInstanceMap;
    }

    private static Set<String> getProcessInstanceIds(List<Task> activeTasks) {
        return activeTasks.stream()
                .map(Task::getProcessInstanceId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
    }


    /**
     * 根据任务id 查询候选人，使用本地 sql 批量查询
     * 同时支持 USER_ID_ 和 GROUP_ID_
     *
     * @param taskIds
     * @return
     */
    private Map<String, TaskCandidates> queryTaskCandidateUsersByTaskIds(Collection<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }
        // 先强转成 List<Object[]>，不然后面 stream 流有问题
        List<Object[]> resultList = entityManager
                .createNativeQuery(TASK_CANDIDATE_SQL)
                .setParameter(TASK_CANDIDATE_TASK_ID_PARAM_NAME, taskIds)
                .getResultList();

        return resultList.stream()
                .filter(objects -> ObjectUtil.isNotNull(ArrayUtil.get(objects, 0)))
                .filter(objects -> ObjectUtil.isNotNull(ArrayUtil.get(objects, 1)) || ObjectUtil.isNotNull(ArrayUtil.get(objects, 2)))
                .map(TaskCandidates::ofSqlResult)
                .collect(Collectors.groupingBy(TaskCandidates::getTaskId, Collectors.toList()))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> taskCandidateMerge(e.getKey(), e.getValue())));

    }

    /**
     * 聚合查询到的 TaskCandedate
     *
     * @param taskId
     * @param candidatesList
     * @return
     */
    private static TaskCandidates taskCandidateMerge(String taskId, List<TaskCandidates> candidatesList) {
        TaskCandidates mergedCandidates = TaskCandidates.builder().taskId(taskId).build();
        List<String> allCandidateUsers = new ArrayList<>();
        List<String> allCandidateGroups = new ArrayList<>();

        for (TaskCandidates candidates : candidatesList) {
            if (candidates.getCandidateUsers() != null) {
                allCandidateUsers.addAll(candidates.getCandidateUsers());
            }
            if (candidates.getCandidateGroups() != null) {
                allCandidateGroups.addAll(candidates.getCandidateGroups());
            }
        }

        // 去重
        if (!allCandidateUsers.isEmpty()) {
            mergedCandidates.setCandidateUsers(allCandidateUsers.stream().distinct().collect(Collectors.toList()));
        }
        if (!allCandidateGroups.isEmpty()) {
            mergedCandidates.setCandidateGroups(allCandidateGroups.stream().distinct().collect(Collectors.toList()));
        }

        return mergedCandidates;
    }

    public Map<String, TaskCandidates> queryTaskCandidateUsersByTaskIds(List<Task> activeTasks) {
        List<String> taskIds = activeTasks.stream().map(Task::getId).collect(Collectors.toList());

        return queryTaskCandidateUsersByTaskIds(taskIds);
    }

    private Map<String, Object> getTaskVariables(Task task, List<VariableInstance> variableInstances) {
        return variableInstances.stream()
                .filter(iter -> task.getId().equals(iter.getTaskId())
                        || (task.getExecutionId().equals(iter.getExecutionId()) && StringUtils.isEmpty(iter.getTaskId()))
                        || (task.getProcessInstanceId().equals(iter.getExecutionId())))
                .filter(iter -> StringUtils.isNotEmpty(iter.getName()) && Objects.nonNull(iter.getValue()))
                .collect(Collectors.toMap(VariableInstance::getName, VariableInstance::getValue));
    }

    public String getProcessDefinitionXml(String processDefinitionId) {
        InputStream processModelIn = null;
        try {
            processModelIn = repositoryService.getProcessModel(processDefinitionId);
            byte[] processModel = IoUtil.readInputStream(processModelIn, "processModelBpmn20Xml");
            return new String(processModel, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RestException(Response.Status.INTERNAL_SERVER_ERROR, e);
        }
    }

    public OcResponse deleteProcessInstance(String processInstanceId, String body) {
        try {
            runtimeService.deleteProcessInstance(processInstanceId, null);
            return CommonUtils.getOcResponse("删除流程实例成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("删除流程实例失败！", e.getMessage());
        }
    }

    public OcResponse deleteTask(String taskId, String reason) {
        try {
            taskService.deleteTask(taskId, reason);
            return CommonUtils.getOcResponse("删除任务成功！", null);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("删除任务失败！", e.getMessage());
        }
    }

    public OcResponse getTaskAuditHistory(String processInstanceId) {
        try {
            List<TaskInfoDTO> tasks;
            List<HistoricTaskInstance> historyTasks = historyService
                    .createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .finished()
                    .orderByHistoricActivityInstanceStartTime()
                    .desc()
                    .list();

            tasks = getTasksByHistoryTask(historyTasks);
            return CommonUtils.getOcResponse("获取历史任务成功！", tasks);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("获取历史任务失败！", e.getMessage());
        }
    }

    private OcResponse getSuccessStartedOcResponse(Object obj) {
        return CommonUtils.getOcResponse("提交成功！", obj);
    }

    private OcResponse getErrorStartedOcResponse(Object obj) {
        return CommonUtils.getErrorOcResponse("提交失败！", obj);
    }

    //    @Transactional
    public ResponseEntity<String> getCurrentTasks() {
        List<Task> activeTasks = taskService
                .createTaskQuery()

                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByTaskCreateTime()
                .desc()
                .list();

        String assigneeListString = jsonConverter.toJson(getTasks(activeTasks));
        return new ResponseEntity<>(assigneeListString, HttpStatus.OK);
    }

    public ResponseEntity<String> getCurrentTasksByTaskDefKey(String taskDefKey) {
        List<Task> activeTasks = taskService
                .createTaskQuery()
                .taskDefinitionKey(taskDefKey)
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByTaskCreateTime()
                .desc()
                .list();

        String assigneeListString = jsonConverter.toJson(getTasks(activeTasks));
        return new ResponseEntity<>(assigneeListString, HttpStatus.OK);
    }

    public ResponseEntity<String> getCurrentTasksByTaskDefKeys(List<String> taskDefKeys) {
        List<Task> activeTasks = taskService
                .createTaskQuery()
                .taskDefinitionKeyIn(taskDefKeys.toArray(new String[taskDefKeys.size()]))
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByTaskCreateTime()
                .desc()
                .list();

        String assigneeListString = jsonConverter.toJson(getTasks(activeTasks));
        return new ResponseEntity<>(assigneeListString, HttpStatus.OK);
    }

    //    @TransactionalProcessEngineService
    public ResponseEntity<String> getCurrentTasksByProcInstIds(List<String> procInstIds) {
        List<Task> activeTasks = new ArrayList<>();
        procInstIds.stream().forEach(iter -> {
            activeTasks.addAll(taskService.createTaskQuery().processInstanceId(iter).tenantIdIn(AppInfoContext.getApp().getAppInstId()).list());
        });
        return new ResponseEntity<>(jsonConverter.toJson(getTasks(activeTasks)), HttpStatus.OK);
    }

    public ResponseEntity<String> getHistoryTasksByTaskDefKey(String taskDefKey) {
        List<TaskInfoDTO> tasks;
        List<HistoricTaskInstance> historyTasks = historyService
                .createHistoricTaskInstanceQuery()
                .taskDefinitionKey(taskDefKey)
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();

        tasks = getTasksByHistoryTask(historyTasks);
        return new ResponseEntity<>(jsonConverter.toJson(tasks), HttpStatus.OK);
    }

    public ResponseEntity<String> getHistoryTasksByProcessInstId(String processInstId) {
        List<TaskInfoDTO> tasks;
        List<HistoricTaskInstance> historyTasks = historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstId)
                .finished()
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();

        tasks = getTasksByHistoryTask(historyTasks);
        return new ResponseEntity<>(jsonConverter.toJson(tasks), HttpStatus.OK);
    }

    public List<HistoricActivityInstance> getHistoricActivityInstance(String processInstId, String activityId) {
        List<HistoricTaskInstance> historicTaskInstances = historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstId)
                .taskDefinitionKey(activityId)
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();

        if (CollUtil.isEmpty(historicTaskInstances)) {
            return new ArrayList<>();
        }

        HistoricTaskInstance historicTaskInstance = historicTaskInstances.get(0);

        return historyService
                .createHistoricActivityInstanceQuery()
                .executionId(historicTaskInstance.getExecutionId())
                .processInstanceId(processInstId)
                .list();
    }

    public OcResponse restartProcessInstances(HistoricProcessInstance processInstance, String activityId) {
        try {
            runtimeService
                    .restartProcessInstances(processInstance.getProcessDefinitionId())
                    .startBeforeActivity(activityId)
                    .processInstanceIds(processInstance.getId())
                    .execute();

            //返回重启的processInstId
            HistoricProcessInstance historicProcessInstance = historyService
                    .createHistoricProcessInstanceQuery()
                    .processDefinitionId(processInstance.getProcessDefinitionId())
                    .active()
                    .orderByProcessInstanceStartTime()
                    .desc()
                    .list()
                    .get(0);

            return CommonUtils.getOcResponse("重启流程实例成功！", historicProcessInstance);
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("重启流程实例失败！", e.getMessage());
        }
    }

    public ResponseEntity<String> getHistoryTasksByUserId(String userId) {
        List<TaskInfoDTO> tasks;
        List<HistoricTaskInstance> historyTasks = historyService
                .createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .tenantIdIn(AppInfoContext.getApp().getAppInstId())
                .orderByHistoricActivityInstanceStartTime()
                .desc()
                .list();
        tasks = getTasksByHistoryTask(historyTasks);
        return new ResponseEntity<>(jsonConverter.toJson(tasks), HttpStatus.OK);
    }

    @Transactional
    public OcResponse<String> modifyTaskPriorityByTaskId(String taskId, PriorityEnum priorityEnum) {
        taskService.setPriority(taskId, Integer.parseInt(priorityEnum.getDbValue()));
        taskService.setVariable(taskId, ProcessConst.PROCESS_VAR_PRIORITY, priorityEnum.name());
        return new OcResponse<>("设置任务优先级成功", "0");
    }

    /**
     * Invoked by the containing {@code BeanFactory} after it has set all bean properties
     * and satisfied {@link BeanFactoryAware}, {@code ApplicationContextAware} etc.
     * <p>This method allows the bean instance to perform validation of its overall
     * configuration and final initialization when all bean properties have been set.
     *
     * @throws Exception in the event of misconfiguration (such as failure to set an
     *                   essential property) or if initialization fails for any other reason
     */
    @Override
    public void afterPropertiesSet() throws Exception {

    }

    /**
     * 从模型定义中构建节点的静态连接关系
     *
     * @param modelInstance BPMN模型实例
     * @return 节点连接关系映射
     */
    private Map<String, List<NodeSourceDTO>> buildStaticNodeConnectionsMap(BpmnModelInstance modelInstance) {
        Map<String, List<NodeSourceDTO>> nodeConnectionsMap = new HashMap<>();

        // 获取所有连接线并建立节点连接关系
        Collection<SequenceFlow> allFlows = modelInstance.getModelElementsByType(SequenceFlow.class);
        for (SequenceFlow flow : allFlows) {
            String flowId = flow.getId();
            String sourceId = flow.getSource().getId();
            String sourceName = flow.getSource().getName();
            String targetId = flow.getTarget().getId();

            // 为目标节点添加来源信息
            if (!nodeConnectionsMap.containsKey(targetId)) {
                nodeConnectionsMap.put(targetId, new ArrayList<>());
            }

            // 添加来源节点信息
            nodeConnectionsMap.get(targetId).add(new NodeSourceDTO(flowId, sourceId, sourceName));
        }

        return nodeConnectionsMap;
    }

    /**
     * 查找两个节点之间的连接线ID
     *
     * @param sourceId             源节点ID
     * @param targetId             目标节点ID
     * @param staticConnectionsMap 静态节点连接关系
     * @return 连接线ID，如果不存在则返回null
     */
    private String findFlowIdBetweenNodes(String sourceId, String targetId,
                                          Map<String, List<NodeSourceDTO>> staticConnectionsMap) {
        if (!staticConnectionsMap.containsKey(targetId)) {
            return null;
        }

        return staticConnectionsMap.get(targetId).stream()
                .filter(source -> source.getSourceId().equals(sourceId))
                .map(NodeSourceDTO::getFlowId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 将历史活动实例转换为活动节点DTO
     *
     * @param historicActivityInstances 历史活动实例列表
     * @return 活动节点DTO列表和节点ID到执行ID的映射
     */
    private Pair<List<ActivityNodeDTO>, Map<String, String>> convertToActivityNodeDTOs(List<HistoricActivityInstance> historicActivityInstances) {
        List<ActivityNodeDTO> activityNodes = new ArrayList<>();
        // 创建节点ID到执行ID的映射，因为ActivityNodeDTO可能没有executionId字段
        Map<String, String> nodeExecutionMap = new HashMap<>();

        for (HistoricActivityInstance activity : historicActivityInstances) {
            ActivityNodeDTO activityNode = new ActivityNodeDTO();
            activityNode.setActivityId(activity.getActivityId());
            activityNode.setActivityName(activity.getActivityName());
            activityNode.setStartTime(activity.getStartTime());
            activityNode.setEndTime(activity.getEndTime());
            // 保存执行ID到映射中，而不是直接设置到对象上
            nodeExecutionMap.put(activity.getActivityId(), activity.getExecutionId());

            // 添加执行人信息
            if (StrUtil.isNotBlank(activity.getAssignee())) {
                activityNode.setAssignee(activity.getAssignee());
            }

            // 添加备注信息（如果有的话）
            if (StrUtil.equals(activity.getActivityType(), "userTask")) {
                List<Comment> comments = taskService.getTaskComments(activity.getTaskId());
                if (CollUtil.isNotEmpty(comments)) {
                    activityNode.setComment(comments.get(0).getFullMessage());
                }
            }

            activityNodes.add(activityNode);
        }

        return Pair.of(activityNodes, nodeExecutionMap);
    }

    /**
     * 选择最合适的来源节点作为主要来源
     *
     * @param targetNode 目标节点
     * @param sources    所有来源节点
     * @param nodeMap    节点ID到节点的映射
     * @return 选择的主要来源节点
     */
    private NodeSourceDTO selectPrimarySource(ActivityNodeDTO targetNode, List<NodeSourceDTO> sources,
                                              Map<String, ActivityNodeDTO> nodeMap) {
        if (sources == null || sources.isEmpty()) {
            return null;
        }

        if (sources.size() == 1) {
            return sources.get(0);
        }

        // 优先选择有实际执行记录的来源节点
        List<NodeSourceDTO> sourcesWithActivity = sources.stream()
                .filter(source -> nodeMap.containsKey(source.getSourceId()))
                .collect(Collectors.toList());

        if (!sourcesWithActivity.isEmpty()) {
            // 如果有多个有记录的来源，按结束时间排序，选择最近完成的
            if (sourcesWithActivity.size() > 1) {
                return sourcesWithActivity.stream()
                        .sorted((s1, s2) -> {
                            ActivityNodeDTO a1 = nodeMap.get(s1.getSourceId());
                            ActivityNodeDTO a2 = nodeMap.get(s2.getSourceId());

                            // 优先考虑结束时间
                            if (a1.getEndTime() == null) return 1;
                            if (a2.getEndTime() == null) return -1;

                            // 结束时间接近目标节点开始时间的优先
                            long diff1 = Math.abs(targetNode.getStartTime().getTime() - a1.getEndTime().getTime());
                            long diff2 = Math.abs(targetNode.getStartTime().getTime() - a2.getEndTime().getTime());
                            return Long.compare(diff1, diff2);
                        })
                        .findFirst()
                        .orElse(sourcesWithActivity.get(0));
            } else {
                return sourcesWithActivity.get(0);
            }
        }

        // 如果没有实际执行记录的来源，返回第一个
        return sources.get(0);
    }

    /**
     * 将任务列表转换为TaskInfoDTO列表
     *
     * @param tasks 任务列表
     * @return TaskInfoDTO列表
     */
    public List<TaskInfoDTO> convertTasksToInfoDTOs(List<Task> tasks) {
        return getTasks(tasks);
    }

}