package com.open_care.bpm.service;

import com.open_care.api.common.ServiceException;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.UserTaskInfoDTO;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * BPMN流程工具类
 * 
 * <AUTHOR>
 * @date :2025/4/2
 */
@Component
@Log4j2
public class BpmnProcessUtils {

    /**
     * 从BPMN模型中提取流程信息
     *
     * @param bpmnModelInstance BPMN模型实例
     * @return 流程信息
     */
    public ProcessInfoDTO extractProcessInfo(BpmnModelInstance bpmnModelInstance) {
        if (bpmnModelInstance == null) {
            return null;
        }

        // 创建ProcessInfo对象
        ProcessInfoDTO processInfo = new ProcessInfoDTO();
        
        // 获取流程定义
        Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
        if (processes.isEmpty()) {
            return processInfo;
        }
        
        // 我们假设一个BPMN文件只有一个流程定义，取第一个
        Process process = processes.iterator().next();
        
        // 设置流程ID和名称
        processInfo.setId(process.getId());
        processInfo.setName(process.getName());
        
        // 提取所有用户任务
        Collection<UserTask> userTasks = bpmnModelInstance.getModelElementsByType(UserTask.class);
        List<UserTaskInfoDTO> userTaskInfos = new ArrayList<>();
        
        // 遍历所有用户任务，提取ID和名称
        userTasks.forEach(userTask -> {
            UserTaskInfoDTO userTaskInfo = new UserTaskInfoDTO();
            userTaskInfo.setId(userTask.getId());
            userTaskInfo.setName(userTask.getName());
            
            // 添加到流程信息中
            userTaskInfos.add(userTaskInfo);
        });
        
        processInfo.setUserTasks(userTaskInfos);
        return processInfo;
    }
    
    /**
     * 解析XML文件中的流程信息
     * 
     * @param data XML内容
     * @param elementName 元素名称（process或decision）
     * @return 包含key和name的Map
     * @throws Exception 解析异常
     */
    public Map<String, Object> parseProcessXml(String data, String elementName) throws Exception {
        Document document = DocumentHelper.parseText(data);
        Element node = document.getRootElement();
        Element processInfoElement = node.element(elementName);
        String key = processInfoElement.attribute("id").getValue();
        String name = processInfoElement.attribute("name").getValue();

        Map<String, Object> map = new HashMap<>();
        map.put("key", key);
        map.put("name", name);

        return map;
    }
    
    /**
     * 从XML内容中获取流程信息
     *
     * @param data BPMN或DMN XML内容
     * @param type 文件类型
     * @return 流程信息
     */
    public ProcessInfoDTO getProcessInfoFromData(String data, String type) {
        if (Objects.isNull(data) || Objects.isNull(type)) {
            return null;
        }

        ProcessInfoDTO processInfo = null;

        if ("bpmn".equals(type)) {
            try {
                BpmnModelInstance modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(data.getBytes(StandardCharsets.UTF_8)));
                processInfo = extractProcessInfo(modelInstance);
            } catch (Exception e) {
                log.error("解析BPMN文件失败", e);
                throw new ServiceException("解析BPMN文件失败: " + e.getMessage());
            }
        } else if ("dmn".equals(type)) {
            try {
                Map<String, Object> map = parseProcessXml(data, "decision");
                processInfo = new ProcessInfoDTO();
                processInfo.setId((String) map.get("key"));
                processInfo.setName((String) map.get("name"));
            } catch (Exception e) {
                log.error("解析DMN文件失败", e);
                throw new ServiceException("解析DMN文件失败: " + e.getMessage());
            }
        }

        return processInfo;
    }
} 