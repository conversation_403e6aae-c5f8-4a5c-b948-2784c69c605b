/**
 * Copyright 2020-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.open_care.api.common.dto.bpmn.CombineDTO;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.bpm.dto.OCMergeNodeDTO;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.bpm.utils.DateUtil;
import io.vavr.collection.Seq;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.builder.ParallelGatewayBuilder;
import org.camunda.bpm.model.bpmn.builder.StartEventBuilder;
import org.camunda.bpm.model.bpmn.impl.instance.ExclusiveGatewayImpl;
import org.camunda.bpm.model.bpmn.impl.instance.ExtensionElementsImpl;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaInputOutputImpl;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnEdge;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnShape;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.xml.ModelInstance;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

@Service
public class ProcessProductService {
    private static final Logger logger = LoggerFactory.getLogger(ProcessProductService.class);

    @Autowired
    RepositoryService repositoryService;

    @Value("${myconfig.bpmnFilePathPrefix}")
    String bpmnFilePathPrefix;

    public String createProductProcess(String productId, String processKey, Map<String, Object> body) {
        BpmnModelInstance bpmnModelInstance = getBpmnModelInstance(processKey);
        BpmnModelInstance ProductModelInstance = bpmnModelInstance.clone();

        String productName = MapUtil.getStr(body, "name");

        Process process = ProductModelInstance.getModelElementsByType(Process.class).iterator().next();
        String newProcessKey = process.getId() + "-" + productId;
        process.setId(newProcessKey);
        if (StringUtils.isNotEmpty(productName)) {
            process.setName(process.getName() + "(" + productName + ")");
        }

        ProductModelInstance.getModelElementsByType(FlowElement.class).stream().forEach(
                iter -> {
                    iter.setId(iter.getId() + "-" + productId);
                    if (StringUtils.isNotEmpty(productName) && iter instanceof Task) {
                        iter.setName(iter.getName() + "(" + productName + ")");
                        if (iter.getExtensionElements() != null) {
                            iter.getExtensionElements().getElements().stream().forEach(
                                    element -> {
                                        if (element instanceof CamundaInputOutputImpl) {
                                            ((CamundaInputOutputImpl) element).getCamundaInputParameters().stream().forEach(
                                                    param -> {
                                                        //TODO 灵活设置变量
                                                        if("productId".equals(param.getCamundaName())) {
                                                            param.setTextContent(productId);
                                                        }
                                                    }
                                            );
                                        }
                                    }
                            );
                        }
                    }
                }
        );

        Bpmn.validateModel(ProductModelInstance);

        //测试使用
        //保存bpmn文件
        try {
            String tenantId = AppInfoContext.getApp().getAppInstId();
            String path = bpmnFilePathPrefix + tenantId + "/merge/";
            CommonUtils.createDir(path);
            String fileName = path + newProcessKey + "-" +
                    DateFormatUtils.format(new Date(),"yyyyMMddHHmm") + ".bpmn";
            //File file = File.createTempFile("package3333", ".bpmn");
            File file = new File(fileName);
            Bpmn.writeModelToFile(file, ProductModelInstance);
        } catch (Exception e) {
            logger.error("write file error:", e);
            throw new IllegalStateException("生成失败");
        }

        repositoryService.createDeployment()
                .addModelInstance(newProcessKey + ".bpmn", ProductModelInstance)
                .tenantId(AppInfoContext.getApp().getAppInstId())
                .deploy();

        return newProcessKey;
    }

    public String createPackageProcess(String productId, String processKey, CombineDTO body) {
        BpmnModelInstance bpmnModelInstance = getBpmnModelInstance(processKey);
        BpmnModelInstance model= bpmnModelInstance.clone();
        Process process = model.getModelElementsByType(Process.class).iterator().next();
        String newProcessKey = process.getId() + "_" + productId + "_" + DateFormatUtils.format(new Date(),"yyyyMMddHHmm");
        process.setId(newProcessKey);
        //处理子流程
        body.getSubProcesses().stream().forEach(
                iter -> {
                    FlowNode flowNode = model.getModelElementById(iter.getMainProcessFlowNodeDefKey());
                    for (int i = 0; i < iter.getSubProcessDefKeys().size(); i++) {
                        String subProcessDefKey = iter.getSubProcessDefKeys().get(i);
                        handleNodeBuilder(flowNode.builder(), getStartEventNode(subProcessDefKey));
                        //替换节点
                        replaceFlowNode(model, subProcessDefKey, "Task");
                    }
                }
        );

        //ParallelGateway parallelGatewayEnd = (ParallelGateway) model.getModelElementById("parallelEnd");

        //节点连接
        body.getFlowNodeDefKeys().stream().forEach(
                iter -> {
                    ModelElementInstance node = model.getModelElementById(iter.getFlowNodeDefKey());
                    iter.getConnectFlowNodeKeys().stream().forEach(
                            each -> {
                                if (node instanceof FlowNode) {
                                    ModelElementInstance targetNode = model.getModelElementById(each.getFlowNodeKey());
                                    //removeFlowRelationNode((FlowNode) node, iter.getDisconnectFlowNodeKeys());

                                    if(StringUtils.isEmpty(each.getSequenceFlowKey())) {
                                        each.setSequenceFlowKey("SequenceFlow_" + UUID.randomUUID().toString());
                                    }

                                    if (Objects.isNull(model.getModelElementById(each.getSequenceFlowKey()))) {
                                        if (targetNode == null) { //如果为空，则默认为并行网关
                                            ((FlowNode) node).builder().sequenceFlowId(each.getSequenceFlowKey())
                                                    .parallelGateway(each.getFlowNodeKey());
                                        } else {
                                            ((FlowNode) node).builder().sequenceFlowId(each.getSequenceFlowKey())
                                                    .connectTo(each.getFlowNodeKey());
                                        }
                                    }
                                }
                            }
                    );

                    iter.getDisconnectFlowNodeKeys().stream().forEach(
                        each -> {
                            if (node instanceof FlowNode) {
                                removeFlowRelationNode((FlowNode) node, iter.getDisconnectFlowNodeKeys());
                            }
                        }
                    );
                }
        );

        replaceFlowNode(model, processKey, "Task,SequenceFlow,Gateway");
        //流程合并
        processMerge(body);

        //节点合并
        processNodeMerge(model, body.getMergeFlowNodeDefKeys());

        //替换sequenceFlow
        body.getSubProcesses().stream().forEach(
                iter -> {
                    for (int i = 0; i < iter.getSubProcessDefKeys().size(); i++) {
                        String subProcessDefKey = iter.getSubProcessDefKeys().get(i);
                        //替换sequenceFlow
                        replaceFlowNode(model, subProcessDefKey, "SequenceFlow");
                    }
                }
        );

        //最后处理所有异常的SequenceFlow
        model.getModelElementsByType(SequenceFlow.class).stream().forEach(
                iter -> {
                    if (iter.getSource() == null) {
                        System.out.println(iter.getId());
                        iter.getParentElement().removeChildElement(iter);
                    }
                }
        );

        Bpmn.validateModel(model);
        //保存bpmn文件
        try {
            String tenantId = AppInfoContext.getApp().getAppInstId();
            String path = bpmnFilePathPrefix + tenantId + "/merge/";
            CommonUtils.createDir(path);
            //String fileName = path + newProcessKey + "-" + DateFormatUtils.format(new Date(),"yyyyMMddHHmm") + ".bpmn";
            String fileName = path + newProcessKey + ".bpmn";
            File file = new File(fileName);
            Bpmn.writeModelToFile(file, model);
        } catch (Exception e) {
            logger.error("write file error:", e);
            throw new IllegalStateException("汇聚失败");
        }

        repositoryService.createDeployment()
                .addModelInstance(newProcessKey + ".bpmn", model)
                .tenantId(AppInfoContext.getApp().getAppInstId())
                .deploy();
        return newProcessKey;
    }

    public void removeFlowRelationNode(FlowNode flowNode, List<String> disconnectBpmnKeys) {
        flowNode.getIncoming().stream()
                .forEach(
                        x -> {
                            if (disconnectBpmnKeys.contains(x.getSource().getId())) {
                                removeFlowNodeBpmnDI(x);
                                x.getParentElement().removeChildElement(x);
                            }
                        }
                );

        flowNode.getOutgoing().stream()
                .forEach(
                        x -> {
                            if (disconnectBpmnKeys.contains(x.getTarget().getId())) {
                                removeFlowNodeBpmnDI(x);
                                x.getParentElement().removeChildElement(x);
                            }
                        }
                );
    }

    /*public void processCombine(BpmnModelInstance model) {
        //获取所有需合并的节点
        Map<String, List<OCMergeNodeDTO>> map = new HashMap();
        model.getModelElementsByType(Task.class).stream().forEach(
                iter -> {
                    getFlowNodeGroupByMergeFlag(iter, map);
                }
        );

        map.entrySet().forEach(
                iter -> {
                    mergeNode(iter.getValue());
                }
        );

        Bpmn.validateModel(model);
    }*/

    private void processNodeMerge(BpmnModelInstance model, List<String> mergeNodes) {
        logger.error("mergeNodes:" + mergeNodes.toString());
        mergeNodes.stream().forEach(
                iter -> {
                    //最后一个节点保留，其余的节点删除，将变量放置到最后一个节点上
                    List<String> nodes = Arrays.asList(iter.split(","));
                    String productIds = "";
                    for (int i = 0; i < nodes.size(); i++) {
                        String node = nodes.get(i);
                        FlowNode flowNode = model.getModelElementById(node);
                        if (flowNode != null) {
                            if (i == nodes.size() - 1) {
                                productIds = productIds + "," + getFlowNodeVariable(flowNode, "productId");
                                setFlowNodeVariable(flowNode, "", productIds);
                            } else {
                                productIds = productIds + "," + getFlowNodeVariable(flowNode, "productId");
                                removeFlowNode(flowNode);
                            }
                        }
                    }
                }
        );
    }

    //流程定义合并，必须是同一类型流程定义
    private void processMerge(CombineDTO body) {
        Map<String, String> nodes = new HashMap();
        body.getMergeProcessDefKeys().stream().forEach(
                iter -> {
                    iter.getMergeSubProcessDefKeys().stream().forEach(
                            each -> {
                                BpmnModelInstance bpmnModelInstance = getBpmnModelInstance(each);
                                bpmnModelInstance.getModelElementsByType(FlowElement.class).stream().forEach(
                                        element -> {
                                            if (element instanceof Task || element instanceof Gateway) {
                                                //取出原始的key TODO 使用更灵活的方式获取
                                                String originalNodeKey = element.getId().substring(0, element.getId().indexOf("-"));
                                                if (!iter.getExcludeNode().contains(originalNodeKey)) {
                                                    if (nodes.containsKey(originalNodeKey)) {
                                                        nodes.put(originalNodeKey, nodes.get(originalNodeKey) + "," + element.getId());
                                                    } else {
                                                        nodes.put(originalNodeKey, element.getId());
                                                    }
                                                }
                                            }
                                        }
                                );
                            }
                    );
                }
        );

        nodes.entrySet().forEach(
                iter -> {
                    body.getMergeFlowNodeDefKeys().add(iter.getValue());
                }
        );
    }

    /*private void mergeNode(List<OCMergeNodeDTO> mergeNodeDTOS) {
        //取出待合并节点中的第一个节点
        FlowNode firstFlowNode = mergeNodeDTOS.get(0).getFlowNode();
        ModelInstance model = firstFlowNode.getModelInstance();

        //找到待合并节点中第一个节点之前的节点
        FlowNode previousNode = firstFlowNode.getPreviousNodes().list().get(0);

        //删除待合并节点中第一个节点之前的sequenceFlow
        firstFlowNode.getIncoming().stream().forEach(
                iter -> {
                    removeFlowNodeBpmnDI(iter);
                    iter.getParentElement().removeChildElement(iter);
                }
        );

        //删除待合并节点中第一个节点之后的sequenceFlow
        firstFlowNode.getOutgoing().stream().forEach(
                iter -> {
                    removeFlowNodeBpmnDI(iter);
                    iter.getParentElement().removeChildElement(iter);
                }
        );

        //创建前面的并行网关
        String uuid = UUID.randomUUID().toString();
        previousNode.builder().parallelGateway("parallelBefore" + uuid);
        ParallelGateway parallelGatewayBefore = (ParallelGateway) model.getModelElementById("parallelBefore" + uuid);
        //并行网关关联待合并节点中的第一个节点
        parallelGatewayBefore.builder().connectTo(firstFlowNode.getId());

        //创建后面的并行网关
        firstFlowNode.builder().parallelGateway("parallelAfter" + uuid);
        ParallelGateway parallelGatewayAfter = (ParallelGateway) model.getModelElementById("parallelAfter" + uuid);

        mergeNodeDTOS.stream().forEach(
                mergeNodeDTO -> {
                    //前面的并行网关连接前面的节点
                    for (FlowNode node : mergeNodeDTO.getPreviousNodes()) {
                        node.builder().connectTo(parallelGatewayBefore.getId());
                    }

                    //后面的并行网关连接后面的节点
                    for (FlowNode node : mergeNodeDTO.getSucceedingNodes()) {
                        parallelGatewayAfter.builder().connectTo(node.getId());
                    }
                }
        );

        //删除需要被合并的节点
        for (int i = 1; i < mergeNodeDTOS.size(); i++) {
            removeFlowNode(mergeNodeDTOS.get(i).getFlowNode());
        }
        //removeFlowNode(mergeNodeDTOS.get(1).getFlowNode());
    }*/

    public String getProperties(Collection<CamundaInputParameter> camundaInputParameters, String key) {
        Optional<CamundaInputParameter> camundaInputParameterOptional =
                camundaInputParameters.stream().filter(iter -> iter.getCamundaName().equals(key)).findFirst();
        if (camundaInputParameterOptional.isPresent()) {
            return camundaInputParameterOptional.get().getRawTextContent();
        }

        return null;
    }

    //根据mergeFlag获取需待合并的节点
    public void getFlowNodeGroupByMergeFlag(FlowNode flowNode, Map<String, List<OCMergeNodeDTO>> map) {
        if (flowNode instanceof Task) {
            Task task = (Task) flowNode;
            if (task.getExtensionElements() != null) {
                CamundaInputOutputImpl camundaInputOutput = (CamundaInputOutputImpl) ((Task) flowNode).getExtensionElements().getElements().iterator().next();

                String mergeFlag = getProperties(camundaInputOutput.getCamundaInputParameters(), "taskExecutors");
                if (StringUtils.isNotEmpty(mergeFlag)) {
                    OCMergeNodeDTO mergeNodeDTO = new OCMergeNodeDTO();
                    if (map.containsKey(mergeFlag)) {
                        mergeNodeDTO.setFlowNode(flowNode);
                        mergeNodeDTO.setPreviousNodes(flowNode.getPreviousNodes().list());
                        mergeNodeDTO.setSucceedingNodes(flowNode.getSucceedingNodes().list());
                        map.get(mergeFlag).add(mergeNodeDTO);
                    } else {
                        List<OCMergeNodeDTO> flowNodes = new ArrayList<>();
                        mergeNodeDTO.setFlowNode(flowNode);
                        mergeNodeDTO.setPreviousNodes(flowNode.getPreviousNodes().list());
                        mergeNodeDTO.setSucceedingNodes(flowNode.getSucceedingNodes().list());
                        flowNodes.add(mergeNodeDTO);
                        map.put(mergeFlag, flowNodes);
                    }
                }
            }
        }
    }

    //替换节点
    public void replaceFlowNode(BpmnModelInstance bpmnModelInstance, String productProcessKey, String type) {
        BpmnModelInstance product = getBpmnModelInstance(productProcessKey);

        if (type.contains("Task")) {
            bpmnModelInstance.getModelElementsByType(Task.class).stream().forEach(
                    iter -> {
                        if (iter instanceof UserTask) {
                            System.out.println(iter.getId());
                            UserTask userTask = cloneUserTask(bpmnModelInstance, product.getModelElementById(iter.getId()));

                            if (userTask != null) {
                                iter.replaceWithElement(userTask);
                            }
                        } else if (iter instanceof ServiceTask) {
                            ServiceTask serviceTask = cloneServiceTask(bpmnModelInstance, product.getModelElementById(iter.getId()));

                            if (serviceTask != null) {
                                iter.replaceWithElement(serviceTask);
                            }
                        }
                    }
            );
        }

        if (type.contains("SequenceFlow")) {
            bpmnModelInstance.getModelElementsByType(SequenceFlow.class).stream().forEach(
                    iter -> {
                        SequenceFlow sequenceFlow = product.getModelElementById(iter.getId());
                        if (sequenceFlow != null && sequenceFlow.getConditionExpression() != null) {
                            iter.setConditionExpression(bpmnModelInstance.newInstance(ConditionExpression.class));
                            iter.getConditionExpression().setTextContent(sequenceFlow.getConditionExpression().getRawTextContent());
                        }
                    }
            );
        }

        if (type.contains("Gateway")) {
            bpmnModelInstance.getModelElementsByType(Gateway.class).stream().forEach(
                    iter -> {
                        iter.setName("");
                    }
            );
        }
    }

    public BpmnModelInstance getBpmnModelInstance(String processKey) {
        List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(processKey).orderByProcessDefinitionVersion().desc().list();

        if (CollUtil.isEmpty(processDefinitions)) {
            throw new IllegalStateException("Process key:" + processKey + " is not exists");
        }
        return repositoryService.getBpmnModelInstance(processDefinitions.get(0).getId());
    }

    //获取Activity之前的Builder
    public AbstractFlowNodeBuilder getBeforeActivityBuilder(StartEventBuilder startEventBuilder, String processKey) {
        BpmnModelInstance bpmnModelInstance = getBpmnModelInstance(processKey);

        StartEvent startEvent = bpmnModelInstance.getModelElementsByType(StartEvent.class).iterator().next();
        return handleNodeBuilder(startEventBuilder, startEvent);
    }

    public StartEvent getStartEventNode(String processKey) {
        BpmnModelInstance bpmnModelInstance = getBpmnModelInstance(processKey);

        return bpmnModelInstance.getModelElementsByType(StartEvent.class).iterator().next();
    }

    //处理节点builder
    public AbstractFlowNodeBuilder handleNodeBuilder(AbstractFlowNodeBuilder flowNodeBuilder, FlowNode flowNode) {
        String flowNodeId = flowNode.getId();
        AbstractFlowNodeBuilder nodeBuilder = flowNodeBuilder;

        for (SequenceFlow iter : flowNode.getOutgoing()) { //TODO 处理多个flow的情况
            SequenceFlow sequenceFlow = flowNodeBuilder.getElement().getModelInstance().getModelElementById(iter.getId());
            if (sequenceFlow == null && !(iter.getTarget() instanceof EndEvent)) {
                nodeBuilder = handleSequenceBuilder(flowNodeBuilder.sequenceFlowId(iter.getId()), iter);
            } else {
                nodeBuilder = flowNodeBuilder;
            }
        }
        return nodeBuilder;
    }

    //处理线条builder
    public AbstractFlowNodeBuilder handleSequenceBuilder(AbstractFlowNodeBuilder abstractFlowNodeBuilder, SequenceFlow sequenceFlow) {
        if (sequenceFlow.getTarget() instanceof UserTask) {
            UserTask userTask = abstractFlowNodeBuilder.getElement().getModelInstance().getModelElementById(sequenceFlow.getTarget().getId());
            if (userTask == null) {
                return handleNodeBuilder(abstractFlowNodeBuilder.userTask(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
            } else {
                return abstractFlowNodeBuilder.connectTo(userTask.getId());
            }
        }

        if (sequenceFlow.getTarget() instanceof ServiceTask) {
            //TODO 判断是否已存在
            return handleNodeBuilder(abstractFlowNodeBuilder.serviceTask(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
        }

        if (sequenceFlow.getTarget() instanceof ExclusiveGateway) {
            ExclusiveGateway exclusiveGateway = abstractFlowNodeBuilder.getElement().getModelInstance().getModelElementById(sequenceFlow.getTarget().getId());
            if (exclusiveGateway == null) {
                return handleNodeBuilder(abstractFlowNodeBuilder.exclusiveGateway(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
            } else {
                return abstractFlowNodeBuilder.connectTo(exclusiveGateway.getId());
            }
        }

        if (sequenceFlow.getTarget() instanceof ParallelGateway) {
            //TODO 判断是否已存在
            return handleNodeBuilder(abstractFlowNodeBuilder.parallelGateway(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
        }

        if (sequenceFlow.getTarget() instanceof InclusiveGateway) {
            InclusiveGateway inclusiveGateway = abstractFlowNodeBuilder.getElement().getModelInstance().getModelElementById(sequenceFlow.getTarget().getId());
            if (inclusiveGateway == null) {
                return handleNodeBuilder(abstractFlowNodeBuilder.inclusiveGateway(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
            } else {
                return abstractFlowNodeBuilder.connectTo(inclusiveGateway.getId());
            }
            //return handleNodeBuilder(abstractFlowNodeBuilder.inclusiveGateway(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
        }

        //TODO 汇聚时EndEvent可能需要保留
        if (sequenceFlow.getTarget() instanceof EndEvent) {
            //return handleNodeBuilder(abstractFlowNodeBuilder.endEvent(sequenceFlow.getTarget().getId()), sequenceFlow.getTarget());
            return abstractFlowNodeBuilder;
        }

        if (sequenceFlow.getTarget() instanceof Activity) {
            return abstractFlowNodeBuilder;
        }

        return null;
    }

    //克隆一个UserTask
    public UserTask cloneUserTask(BpmnModelInstance bpmnModelInstance, UserTask userTask) {
        UserTask newUserTask = bpmnModelInstance.newInstance(UserTask.class);
        if (userTask != null) {
            newUserTask.setId(userTask.getId());
            newUserTask.setName(userTask.getName());
            newUserTask.setCamundaAssignee(userTask.getCamundaAssignee());
            newUserTask.setCamundaCandidateGroups(userTask.getCamundaCandidateGroups());
            newUserTask.setCamundaCandidateGroupsList(userTask.getCamundaCandidateGroupsList());
            newUserTask.setCamundaCandidateUsers(userTask.getCamundaCandidateUsers());
            newUserTask.setCamundaCandidateUsersList(userTask.getCamundaCandidateUsersList());
            //newUserTask.setCamundaDueDate(userTask.getCamundaDueDate());
            //newUserTask.setCamundaFollowUpDate(userTask.getCamundaFollowUpDate());
            //newUserTask.setCamundaFormHandlerClass(userTask.getCamundaFormHandlerClass());
            //newUserTask.setCamundaFormKey(userTask.getCamundaFormKey());
            //newUserTask.setCamundaPriority(userTask.getCamundaPriority());
            //newUserTask.setImplementation(userTask.getImplementation());

            newUserTask.setExtensionElements(bpmnModelInstance.newInstance(ExtensionElements.class));
            if (userTask.getExtensionElements() != null) {
                userTask.getExtensionElements().getElements().stream().forEach(
                        iter -> {
                            if (iter instanceof CamundaInputOutputImpl) {
                                CamundaInputOutput camundaInputOutput = bpmnModelInstance.newInstance(CamundaInputOutput.class);
                                ((CamundaInputOutputImpl) iter).getCamundaInputParameters().stream().forEach(
                                        param -> {
                                            CamundaInputParameter camundaInputParameter = bpmnModelInstance.newInstance(CamundaInputParameter.class);
                                            camundaInputParameter.setCamundaName(param.getCamundaName());
                                            camundaInputParameter.setTextContent(param.getTextContent());
                                            camundaInputOutput.getCamundaInputParameters().add(camundaInputParameter);
                                        }
                                );
                                newUserTask.getExtensionElements().addChildElement(camundaInputOutput);
                            }
                        }
                );
            }
            return newUserTask;
        }

        return null;
    }

    //克隆一个ServiceTask
    public ServiceTask cloneServiceTask(BpmnModelInstance bpmnModelInstance, ServiceTask serviceTask) {
        ServiceTask newServiceTask = bpmnModelInstance.newInstance(ServiceTask.class);
        if (serviceTask != null) {
            newServiceTask.setId(serviceTask.getId());
            newServiceTask.setName(serviceTask.getName());
            //newServiceTask.setImplementation(serviceTask.getImplementation());
            //newServiceTask.setCamundaClass(serviceTask.getCamundaClass());
            //newServiceTask.setCamundaDelegateExpression(serviceTask.getCamundaDelegateExpression());
            newServiceTask.setCamundaExpression(serviceTask.getCamundaExpression());
            //newServiceTask.setCamundaResultVariable(serviceTask.getCamundaResultVariable());
            //newServiceTask.setCamundaTopic(serviceTask.getCamundaTopic());
            //newServiceTask.setCamundaType(serviceTask.getCamundaType());
            //newServiceTask.setCamundaTaskPriority(serviceTask.getCamundaTaskPriority());

            //newServiceTask.setOperation(serviceTask.getOperation());
            //newServiceTask.setExtensionElements(serviceTask.getExtensionElements());

            return newServiceTask;
        }

        return null;
    }

    //克隆一个ServiceTask
    public SequenceFlow cloneSequenceFlow(BpmnModelInstance bpmnModelInstance, SequenceFlow sequenceFlow) {
        if (sequenceFlow != null) {
            SequenceFlow newSequenceFlow = bpmnModelInstance.newInstance(SequenceFlow.class);
            newSequenceFlow.setId(sequenceFlow.getId());
            newSequenceFlow.setSource(sequenceFlow.getSource());
            newSequenceFlow.setTarget(sequenceFlow.getTarget());
            /*if(sequenceFlow.getConditionExpression() != null) {
                newSequenceFlow.setConditionExpression(sequenceFlow.getConditionExpression());
            }*/
            return newSequenceFlow;
        }

        return null;
    }

    //克隆一个ScriptTask
    public ScriptTask cloneScriptTask(BpmnModelInstance bpmnModelInstance, ScriptTask scriptTask) {
        ScriptTask newScriptTask = bpmnModelInstance.newInstance(ScriptTask.class);
        if (scriptTask != null) {
            newScriptTask.setId(scriptTask.getId());
            newScriptTask.setName(scriptTask.getName());
            newScriptTask.setCamundaResource(scriptTask.getCamundaResource());
            newScriptTask.setCamundaResultVariable(scriptTask.getCamundaResultVariable());
            newScriptTask.setScript(scriptTask.getScript());
            newScriptTask.setScriptFormat(scriptTask.getScriptFormat());
            newScriptTask.setExtensionElements(scriptTask.getExtensionElements());
            return newScriptTask;
        }

        return null;
    }

    //删除某个节点，该节点的输入和输出流同时删除,同时删除相应的BPMN DI元素
    public void removeFlowNode(FlowNode flowNode) {
        flowNode.getIncoming().stream().forEach(
                iter -> {
                    removeFlowNodeBpmnDI(iter);
                    iter.getParentElement().removeChildElement(iter);
                }
        );

        flowNode.getOutgoing().stream().forEach(
                iter -> {
                    removeFlowNodeBpmnDI(iter);
                    iter.getParentElement().removeChildElement(iter);
                }
        );
        removeFlowNodeBpmnDI(flowNode);
        flowNode.getParentElement().removeChildElement(flowNode);
    }

    //删除sequenceFlow，同时删除相应的BPMN DI元素
    public void removeSequenceFlow(SequenceFlow sequenceFlow) {
        removeFlowNodeBpmnDI(sequenceFlow);
        sequenceFlow.getParentElement().removeChildElement(sequenceFlow);
    }

    //删除某个节点的坐标元素信息
    public void removeFlowNodeBpmnDI(FlowElement flowElement) {
        flowElement.getModelInstance().getModelElementsByType(BpmnShape.class).stream().forEach(
                iter -> {
                    if (iter.getBpmnElement().equals(flowElement)) {
                        iter.getParentElement().removeChildElement(iter);
                    }
                }
        );

        flowElement.getModelInstance().getModelElementsByType(BpmnEdge.class).stream().forEach(
                iter -> {
                    if (iter.getBpmnElement() != null && iter.getBpmnElement().equals(flowElement)) {
                        iter.getParentElement().removeChildElement(iter);
                    }
                }
        );
    }

    private void setFlowNodeVariable(FlowNode flowNode, String variableName, String variableValue) {
        if (flowNode.getExtensionElements() != null) {
            flowNode.getExtensionElements().getElements().stream().forEach(
                    element -> {
                        if (element instanceof CamundaInputOutputImpl) {
                            ((CamundaInputOutputImpl) element).getCamundaInputParameters().stream().forEach(
                                    param -> {
                                        //TODO 灵活设置变量
                                        param.setTextContent(variableValue);
                                    }
                            );
                        }
                    }
            );
        }
    }

    private String getFlowNodeVariable(FlowNode flowNode, String variableName) {
        if (flowNode.getExtensionElements() != null) {
            Optional<ModelElementInstance> camundaInputOutput = flowNode.getExtensionElements().getElements().stream().filter(
                    element -> element instanceof CamundaInputOutputImpl).findFirst();
            if (camundaInputOutput.isPresent()) {
                Optional<CamundaInputParameter> camundaInputParameter = ((CamundaInputOutputImpl) camundaInputOutput.get()).getCamundaInputParameters().stream()
                        .filter(iter -> iter.getCamundaName().equals(variableName))
                        .findFirst();

                if (camundaInputParameter.isPresent()) {
                    return camundaInputParameter.get().getTextContent();
                }
            }
        }

        return null;
    }
}
