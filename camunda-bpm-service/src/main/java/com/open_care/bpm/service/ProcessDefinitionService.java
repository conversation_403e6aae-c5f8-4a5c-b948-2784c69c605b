package com.open_care.bpm.service;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.bpm.dto.ProcessDefinitionMigrationRequestDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessRequestDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessResponseDTO;

/**
 * 流程定义服务接口
 * 
 * <AUTHOR> by <PERSON>
 * @date 2025/5/22
 */
public interface ProcessDefinitionService {
    
    /**
     * 保存、部署并迁移流程
     * 
     * <p>
     * 该方法将完成以下操作：
     * 1. 保存前端传入的XML流程图
     * 2. 自动生成带时间格式后缀的流程ID
     * 3. 部署流程
     * 4. 迁移已有流程实例到新版本
     * </p>
     *
     * @return 操作结果，包含流程信息或错误消息
     */
    SaveAndDeployThenMigrateProcessResponseDTO saveAndDeployThenMigrateProcess(SaveAndDeployThenMigrateProcessRequestDTO requestDTO);
} 