package com.open_care.bpm.service.impl;

import com.open_care.api.common.ServiceException;
import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.mapper.ProcessComponentMapper;
import com.open_care.bpm.service.ProcessComponentService;
import com.open_care.camunda.entity.ProcessComponent;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import com.open_care.camunda.repository.ProcessComponentRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 流程组件服务实现类
 *
 * <AUTHOR>
 */
@Service
@Log4j2
public class ProcessComponentServiceImpl implements ProcessComponentService {

    @Autowired
    private ProcessComponentRepository processComponentRepository;

    @Override
    public List<ProcessComponentDTO> getAllComponents() {
        log.info("获取所有流程组件");
        List<ProcessComponent> components = processComponentRepository.findAll();
        return ProcessComponentMapper.INSTANCE.toDtoList(components);
    }
} 