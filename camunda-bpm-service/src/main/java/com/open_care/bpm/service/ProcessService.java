/**
 * Copyright 2019-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.service;

/**
 * @author:lee
 * @Description:
 * @Date:2019-01-15 14:26
 * @Modified By:
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.api.client.UIRemoteService;
import com.open_care.api.common.ServiceException;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.edit.FieldData;
import com.open_care.bpm.context.AppInfo;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.bpm.context.UserInfoContext;
import com.open_care.bpm.entity.OCProcessdefinition;
import com.open_care.bpm.entity.OCProcessdefinitionDeployDetail;
import com.open_care.bpm.entity.OCProcessdefinitionSaveLog;
import com.open_care.bpm.dto.PaginationDao;
import com.open_care.bpm.dto.ProcessDefListDto;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.SaveProcessDefBodyDto;
import com.open_care.bpm.dto.UserTaskInfoDTO;
import com.open_care.bpm.repository.ProcessDefRepository;
import com.open_care.bpm.repository.ProcessDefSaveLogRepository;
import com.open_care.bpm.repository.ProcessDefinitionDeployDetailRepository;
import com.open_care.bpm.repository.ProcessdefinitionRepository;
import com.open_care.bpm.service.bo.ProcessInfoAndPathName;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.bpm.utils.RestAPIUtil;
import com.open_care.util.json.JsonConverter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.repository.DeploymentBuilder;
import org.camunda.bpm.engine.repository.DeploymentWithDefinitions;
import org.camunda.bpm.engine.rest.dto.repository.DeploymentWithDefinitionsDto;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;

import jakarta.servlet.http.HttpServletRequest;

import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Log4j2
public class ProcessService {
    public static final String NAME = "name";
    public static final String STATUS = "status";

    @Autowired
    RepositoryService bpmRepositoryService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    TaskService taskService;

    @Autowired
    ProcessDefRepository processDefRepository;

    @Autowired
    ProcessDefSaveLogRepository saveLogRepository;

    @Autowired
    ProcessDefinitionDeployDetailRepository detailRepository;

    @Autowired
    CommonUtils commonUtils;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    ProcessdefinitionRepository processdefinitionRepository;

    @Autowired
    UIRemoteService uiRemoteService;

    @Value("${myconfig.bpmnFilePathPrefix}")
    String bpmnFilePathPrefix;

    @Value("${myconfig.bpmnFilePathPostfix}")
    String bpmnFilePathPostfix;

    @Autowired
    @Lazy
    private ProcessService self;

    @Autowired
    private BpmnProcessUtils bpmnProcessUtils;

    private static final String OPER_EQUAL = "equal";
    private static final String OPER_LIKE = "like";

    public final static Map<String, String> typeMap = new HashMap() {
        {
            put("bpmn", "流程定义");
            put("dmn", "DMN");
        }
    };

    public OcResponse queryProcessDefList(final QueryRequestDTO queryRequestDTO) {

        List<FieldData> fieldDataList = queryRequestDTO.getFilters();

        verifyQueryRequestDTOFiltersEqualOperatorOnly(fieldDataList);

        boolean queryAll = CollUtil.isEmpty(fieldDataList);

        String name = queryAll ? "" : CommonUtils.getFieldValueStringByFieldNameForQueryFilters(queryRequestDTO, NAME);
        String status = queryAll ? "" : CommonUtils.getFieldValueStringByFieldNameForQueryFilters(queryRequestDTO, STATUS);

        if (!queryAll && StringUtils.isEmpty(name) && StringUtils.isEmpty(status)) {
            queryAll = true;
        }

        String tenantId = AppInfoContext.getApp().getAppInstId();

        int page = queryRequestDTO.getPagination().getCurrent();
        int size = queryRequestDTO.getPagination().getPageSize();

        PageRequest pageable = PageRequest.of(page - 1, size, getCreatedTimeDescSort());
        List<OCProcessdefinition> ocProcessdefinitionList = queryAll ?
                processDefRepository.getTenantId(tenantId, pageable).getContent()
                : queryProcessdefinitionByNameOrStatus(tenantId, name, status, pageable);


        int total = queryAll ? processDefRepository.findByTenantId(tenantId).size() : ocProcessdefinitionList.size();

        ProcessDefListDto processDefListDto = new ProcessDefListDto();
        processDefListDto.pagination = new PaginationDao(page, total, size);
        processDefListDto.setData(ocProcessdefinitionList.stream().map(iter -> convertOCProcessdefinitionToMap(iter)).collect(Collectors.toList()));

        return CommonUtils.getOcResponse("查询成功", processDefListDto);
    }

    private List<OCProcessdefinition> queryProcessdefinitionByNameOrStatus(final String tenantId, final String name, final String status, final Pageable pageable) {

        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(status)) {
            return processdefinitionRepository.findByTenantIdAndStatusAndNameContaining(tenantId, name, status, pageable);
        }

        if (StringUtils.isBlank(name)) {
            return processdefinitionRepository.findByTenantIdAndStatus(tenantId, status, pageable);
        }

        return processdefinitionRepository.findByTenantIdAndNameContaining(tenantId, name, pageable);
    }

    private void verifyQueryRequestDTOFiltersEqualOperatorOnly(final List<FieldData> fieldDataList) {

        String notEqualOperatorFieldDataName = fieldDataList
                .stream()
                .filter(f -> !StringUtils.equalsAnyIgnoreCase(f.getOperator(), OPER_EQUAL, OPER_LIKE))
                .map(iter -> iter.getFieldName())
                .collect(Collectors.joining(","));

        StringBuilder errorMessage = new StringBuilder("");

        if (StringUtils.isNotBlank(notEqualOperatorFieldDataName)) {
            errorMessage.append(notEqualOperatorFieldDataName + "操作符不是" + OPER_EQUAL + "或" + OPER_LIKE);
        }

        String filterNames = fieldDataList.stream()
                .map(iter -> iter.getFieldName())
                .filter(f -> !StringUtils.endsWithAny(f, NAME, STATUS))
                .collect(Collectors.joining(","));

        if (StringUtils.isNotBlank(filterNames)) {
            errorMessage.append("不支持的查询条件：" + filterNames);
        }

        if (StringUtils.isNotBlank(errorMessage.toString())) {
            throw new IllegalStateException("查询失败：" + errorMessage.toString());
        }
    }

    public String getProcessDefList(int page, int size) {
        HttpServletRequest request = RestAPIUtil.getHttpServletRequest();
        String tenantId = AppInfoContext.getApp().getAppInstId();

        String name = CommonUtils.isEmpty(request.getParameter(NAME)) ? "" : request.getParameter(NAME);
        String status = CommonUtils.isEmpty(request.getParameter(STATUS)) ? "" : request.getParameter(STATUS);
        String created = CommonUtils.isEmpty(request.getParameter("created")) ? "" : request.getParameter("created");

        boolean listAll = false;
        if ("".equals(name) && "".equals(status) && "".equals(created)) {
            listAll = true;
        }

        List<Map<String, String>> mapList = new ArrayList<>();

        //-- 按照创建时间倒序
        Sort sort = getCreatedTimeDescSort();

        List<OCProcessdefinition> ocProcessdefinitionList = listAll ?
                processDefRepository.getTenantId(tenantId, PageRequest.of(page - 1, size, sort)).getContent()
                : processDefRepository.getListBySearchInfo(tenantId, name, status, created, PageRequest.of(page - 1, size, sort));
        ocProcessdefinitionList.forEach(ocProcessdefinition ->
                setMapList(ocProcessdefinition, mapList)
        );

        int total = listAll ? processDefRepository.findByTenantId(tenantId).size() : ocProcessdefinitionList.size();

        ProcessDefListDto processDefListDto = new ProcessDefListDto();
        processDefListDto.pagination = new PaginationDao(page, total, size);
        processDefListDto.setData(mapList);
        return jsonConverter.toJson(processDefListDto);
    }

    private Sort getCreatedTimeDescSort() {
        return Sort.by(Sort.Direction.DESC, "createdTime");
    }

    // 解析XML通用方法，只用于DMN格式
    private static Map<String, Object> parseProcessXml(String data, String elementName) throws Exception {
        Document document = DocumentHelper.parseText(data);
        Element node = document.getRootElement();
        Element processInfoElement = node.element(elementName);
        String key = processInfoElement.attribute("id").getValue();
        String name = processInfoElement.attribute("name").getValue();

        Map<String, Object> map = new HashMap<>();
        map.put("key", key);
        map.put("name", name);

        return map;
    }

    /**
     * 获取流程信息
     *
     * @param data BPMN或DMN XML内容
     * @param type 文件类型
     * @return 流程信息
     */
    public ProcessInfoDTO getProcessInfoFromData(String data, String type) {
        if (Objects.isNull(data) || Objects.isNull(type)) {
            return null;
        }

        ProcessInfoDTO processInfo = null;

        if ("bpmn".equals(type)) {
            try {
                BpmnModelInstance modelInstance = Bpmn.readModelFromStream(new ByteArrayInputStream(data.getBytes(StandardCharsets.UTF_8)));
                processInfo = bpmnProcessUtils.extractProcessInfo(modelInstance);
            } catch (Exception e) {
                log.error("解析BPMN文件失败", e);
                throw new ServiceException("解析BPMN文件失败: " + e.getMessage());
            }
        } else if ("dmn".equals(type)) {
            try {
                Map<String, Object> map = parseProcessXml(data, "decision");
                processInfo = new ProcessInfoDTO();
                processInfo.setId((String) map.get("key"));
                processInfo.setName((String) map.get("name"));
            } catch (Exception e) {
                log.error("解析DMN文件失败", e);
                throw new ServiceException("解析DMN文件失败: " + e.getMessage());
            }
        }

        return processInfo;
    }

    @Transactional
    public OcResponse<ProcessInfoDTO> saveOcprocessDefInfo(String id, String body) {
        try {
            ProcessInfoAndPathName result = doSaveOCProcessDefInfo(id, body);

            return CommonUtils.getOcResponse(StrUtil.format("保存成功，文件相对路径: {}", result.pathName()), result.processInfo());
        } catch (Exception e) {
            log.error("保存流程失败", e);
            return CommonUtils.getErrorOcResponse(StrUtil.format("保存失败：", e.getMessage()), null);
        }
    }

    public ProcessInfoAndPathName doSaveOCProcessDefInfo(String id, String body) {
        // 1. 解析请求数据
        SaveProcessDefBodyDto saveDto = null;
        try {
            saveDto = jsonConverter.fromJson(URLDecoder.decode(body, StandardCharsets.UTF_8.name()), SaveProcessDefBodyDto.class);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String data = saveDto.getData();
        String type = saveDto.getType();

        // 2. 获取流程信息
        ProcessInfoDTO processInfo = bpmnProcessUtils.getProcessInfoFromData(data, type);
        if (processInfo == null) {
            throw new IllegalArgumentException("保存失败！解析" + typeMap.get(type) + "文件失败");
        }

        // 3. 验证流程信息（key重复、key修改、名称修改）
        String key = processInfo.getId();
        String name = StringUtils.isNotEmpty(saveDto.processName) ? saveDto.processName : processInfo.getName();
        validateProcessInfo(id, key, name, type);

        // 4. 获取或创建流程定义对象
        int version = getNextVersion(id);
        OCProcessdefinition ocProcessDef = createOrUpdateProcessDefinition(id, type, name);

        // 5. 保存流程文件
        String pathName = saveProcessFile(id, data, type, version);
        // 6. 保存版本记录

        saveProcessVersionLog(id, saveDto.getFormVersion(), key, pathName, version);

        // 7. 注册保存成功回调
        registerSaveSuccessCallback();

        // 8. 记录流程信息日志
        logProcessInfo(processInfo);
        return new ProcessInfoAndPathName(processInfo, pathName);
    }


    /**
     * 验证流程信息
     */
    private void validateProcessInfo(String id, String key, String name, String type) {
        String typeName = typeMap.get(type);

        // 验证流程key是否重复
        if (!verifyProcessKeyAndProcessDefId(key, id)) {
            throw new IllegalArgumentException("保存失败！" + typeName + "Id:" + key + "重复，请重新录入！");
        }

        // 验证已保存流程的key是否被修改
        String keyBef = getProcessKeyIfIsModified(key, id);
        if (StringUtils.isNotEmpty(keyBef)) {
            throw new IllegalArgumentException("保存失败！已保存的" + typeName + "Id:" + keyBef + "不能被修改！");
        }

        // 验证已部署流程的名称是否被修改
        String nameBef = getProcessNameIfIsDeployed(id);
        if (StringUtils.isNotEmpty(nameBef) && !nameBef.equals(name)) {
            throw new IllegalArgumentException("保存失败！已部署的" + typeName + "名称:" + nameBef + "不能被修改！");
        }

    }

    /**
     * 获取下一个版本号
     */
    private int getNextVersion(String id) {
        int version = 1;
        if (!CommonUtils.isEmpty(saveLogRepository.getMaxVersionByOcprocessdefinitionId(id))) {
            version = Integer.valueOf(saveLogRepository.getMaxVersionByOcprocessdefinitionId(id)) + 1;
        }
        return version;
    }

    /**
     * 创建或更新流程定义
     */
    private OCProcessdefinition createOrUpdateProcessDefinition(String id, String type, String name) {
        Optional<OCProcessdefinition> optional = processDefRepository.findById(id);
        OCProcessdefinition ocProcessdefinition = new OCProcessdefinition();

        if (Optional.empty().equals(optional)) {
            ocProcessdefinition.setId(id);
            ocProcessdefinition.setCreated(UserInfoContext.getUser().getUserId());
            ocProcessdefinition.setCreatedTime(new Date());
            ocProcessdefinition.setTenantId(AppInfoContext.getApp().getAppInstId());
            ocProcessdefinition.setStatus("0");
            ocProcessdefinition.setType(type);
        } else {
            ocProcessdefinition = optional.get();
        }

        ocProcessdefinition.setName(name);
        processDefRepository.save(ocProcessdefinition);
        return ocProcessdefinition;
    }

    /**
     * 保存流程文件
     */
    private String saveProcessFile(String id, String data, String type, int version) {
        String tenantId = AppInfoContext.getApp().getAppInstId();
        String pathName = FileUtil.file(bpmnFilePathPrefix, tenantId, bpmnFilePathPostfix, id, version + "." + type).getPath();

        commonUtils.saveBpmnFileByTenantId(data, pathName);

        return FileUtil.subPath(bpmnFilePathPrefix, pathName);
    }

    /**
     * 保存流程版本记录
     */
    private void saveProcessVersionLog(String id, String formVersion, String key, String pathName, int version) {
        OCProcessdefinitionSaveLog saveLog = new OCProcessdefinitionSaveLog();
        saveLog.setOcprocessdefinitionId(id);
        saveLog.setOperator(UserInfoContext.getUser().getUserId());
        saveLog.setOperatorTime(new Date());
        saveLog.setResourcelocation(pathName);
        saveLog.setVersion(version);
        saveLog.setVersionFrom(CommonUtils.isEmpty(formVersion) ? 0 : Integer.valueOf(formVersion));
        saveLog.setProcessDepKey(key);
        saveLogRepository.save(saveLog);
    }

    /**
     * 注册保存成功回调
     */
    private void registerSaveSuccessCallback() {
        final String appDefId = AppInfoContext.getApp().getAppDefId();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                processSaveSuccessCallBack(appDefId);
            }
        });
    }

    /**
     * 处理流程保存成功回调
     */
    private void processSaveSuccessCallBack(String appDefId) {
        OcResponse<Void> ocResponse = uiRemoteService.processSaveSuccessCallBack(appDefId);
        if (!"0".equals(ocResponse.getStatus())) {
            throw new ServiceException(ocResponse.getError());
        }
    }

    /**
     * 记录流程信息日志
     */
    private void logProcessInfo(ProcessInfoDTO processInfo) {
        if (processInfo != null) {
            log.info("保存流程信息: id={}, name={}, userTasks.size={}",
                    processInfo.getId(), processInfo.getName(),
                    processInfo.getUserTasks() != null ? processInfo.getUserTasks().size() : 0);

            if (processInfo.getUserTasks() != null) {
                for (UserTaskInfoDTO task : processInfo.getUserTasks()) {
                    log.info("用户任务: id={}, name={}", task.getId(), task.getName());
                }
            }
        }
    }

    public OcResponse renameById(String id, String body) {
        try {
            String tenantId = AppInfoContext.getApp().getTenantId();
            String name = jsonConverter.fromJson(body, Map.class).get("name").toString();
            if (CommonUtils.isEmpty(name)) {
                return CommonUtils.getErrorOcResponse("重命名失败！流程定义名称不能为空！", "");
            }
            String deployName = getProcessNameIfIsDeployed(id);
            if (!"".equals(deployName) && !name.equals(deployName)) {
                return CommonUtils.getErrorOcResponse("重命名失败！已部署的流程定义名称" + name + "不能被修改！", "");
            }
            OCProcessdefinition ocProcessdefinition = processDefRepository.findById(id).get();
            if (ocProcessdefinition.getName().equals(name)) {
                return CommonUtils.getErrorOcResponse("重命名失败！流程定义新名称不能与现在名称" + name + "重复！", "");
            }

            if (processDefRepository.getNameListByTenantId(tenantId).indexOf(name) > -1) {
                return CommonUtils.getErrorOcResponse("重命名失败！流程定义名称不能重复！", "");
            }

            ocProcessdefinition.setName(name);
            processDefRepository.save(ocProcessdefinition);

            log.info("流程定义重命名成功！id：" + id);
            return CommonUtils.getOcResponse("重命名成功！新名称：" + ocProcessdefinition.getName(), "");
        } catch (Exception e) {
            return CommonUtils.getErrorOcResponse("重命名失败！", e.getMessage());
        }
    }

    // 常量定义
    private static final String VERSION_KEY = "version";
    private static final String PROCESS_NAME_KEY = "processName";
    private static final String DATA_KEY = "data";
    private static final String TYPE_KEY = "type";
    private static final String PATH_NAME_KEY = "pathName";
    private static final String PATH_NAME_PREFIX = "文件相对路径：";
    private static final int DEFAULT_VERSION = 0;

    /**
     * 根据ID获取流程定义信息
     *
     * @param id      流程定义ID
     * @param version 版本号，可为空
     * @return 流程定义信息
     */
    public OcResponse getOcprocessDefInfoById(String id, Integer version) {
        try {
            // 1. 查找流程定义
            Optional<OCProcessdefinition> ocProcessdefinitionOp = processDefRepository.findById(id);

            // 兼容前端传流程定义id而非OCprocessDefinition.id的情况
            if (ocProcessdefinitionOp.isEmpty()) {
                Optional<OCProcessdefinitionSaveLog> byProcessDefinitionId = saveLogRepository.findByProcessDefinitionId(id);
                if (byProcessDefinitionId.isPresent()) {
                    String ocProcessdefinitionId = byProcessDefinitionId.get().getOcprocessdefinitionId();
                    ocProcessdefinitionOp = processdefinitionRepository.findById(ocProcessdefinitionId);
                    version = byProcessDefinitionId.get().getVersion();
                }
            }

            if (ObjectUtil.isNull(ocProcessdefinitionOp.isEmpty())) {
                return CommonUtils.getErrorOcResponse("流程定义不存在", null);
            }
            OCProcessdefinition ocProcessdefinition = ocProcessdefinitionOp.get();

            // 2. 获取版本信息
            String processId = ocProcessdefinition.getId();
            List<OCProcessdefinitionSaveLog> saveLogList = saveLogRepository.findByOcprocessdefinitionId(processId);
            if (CollUtil.isEmpty(saveLogList)) {
                return CommonUtils.getErrorOcResponse("流程定义版本信息不存在", null);
            }

            if (ObjectUtil.isNull(version)) {
                // 3. 确定使用的版本
                version = saveLogList.stream()
                        .mapToInt(OCProcessdefinitionSaveLog::getVersion)
                        .max()
                        .orElse(DEFAULT_VERSION);
            }


            // 4. 构建返回结果
            Map<String, Object> result = buildProcessDefResult(ocProcessdefinition, saveLogList, version);

            return CommonUtils.getOcResponse("获取成功！", result);
        } catch (Exception e) {
            log.error("获取流程定义文件失败，id: {}, version: {}", id, version, e);
            return CommonUtils.getErrorOcResponse("获取流程定义文件失败！", e.getMessage());
        }
    }

    /**
     * 构建流程定义结果
     */
    private Map<String, Object> buildProcessDefResult(OCProcessdefinition ocProcessdefinition,
                                                      List<OCProcessdefinitionSaveLog> saveLogList,
                                                      Integer targetVersion) throws Exception {
        // 获取版本列表
        List<String> versionList = generateVersionList(saveLogList);

        // 获取文件内容
        String data = getProcessFileContent(ocProcessdefinition.getId(), targetVersion);

        // 构建结果
        Map<String, Object> result = new HashMap<>();
        result.put(VERSION_KEY, versionList);
        result.put(PROCESS_NAME_KEY, ocProcessdefinition.getName());
        result.put(DATA_KEY, data);
        result.put(TYPE_KEY, ocProcessdefinition.getType());
        result.put(PATH_NAME_KEY, generatePathNameMessage(ocProcessdefinition.getId(), targetVersion));

        return result;
    }

    /**
     * 生成版本列表
     */
    private List<String> generateVersionList(List<OCProcessdefinitionSaveLog> saveLogList) {
        return saveLogList.stream()
                .map(saveLog -> String.valueOf(saveLog.getVersion()))
                .collect(Collectors.toList());
    }

    /**
     * 获取流程文件内容
     */
    private String getProcessFileContent(String processId, Integer version) throws Exception {
        OCProcessdefinitionSaveLog saveLog = saveLogRepository.findByOcprocessdefinitionIdAndVersion(processId, version);
        String pathName = FileUtil.file(bpmnFilePathPrefix, saveLog.getResourcelocation()).getPath();
        return CommonUtils.getBpmnFileTextByPath(pathName);
    }

    /**
     * 生成路径名称消息
     */
    private String generatePathNameMessage(String processId, Integer version) {
        OCProcessdefinitionSaveLog saveLog = saveLogRepository.findByOcprocessdefinitionIdAndVersion(processId, version);
        String pathName = FileUtil.file(bpmnFilePathPrefix, saveLog.getResourcelocation()).getPath();
        return PATH_NAME_PREFIX + pathName;
    }

    @Transactional
    public OcResponse<?> deployProcessdefByOCProcessdefId(String id, String version) throws Exception {
        Map<String, String> result = doDeployProcessDefByOcProcessDefId(id, version);
        return CommonUtils.getOcResponse("部署成功！", result);
    }

    @Transactional
    public Map<String, String> doDeployProcessDefByOcProcessDefId(String id, String version) throws Exception {
        Map<String, String> result = new HashMap<>();
        Optional<OCProcessdefinition> ocProcessdefinitionOp = processDefRepository.findById(id);
        OCProcessdefinition ocProcessdefinition = ocProcessdefinitionOp.get();

        String type = ocProcessdefinition.getType();
        String name = ocProcessdefinition.getName();
        String tenantId = ocProcessdefinition.getTenantId();
        OCProcessdefinitionSaveLog saveLog = saveLogRepository.findByOcprocessdefinitionIdAndVersion(id, Integer.valueOf(version));
        saveLog.setDeployed(true);
        String pathResource = FileUtil.file(bpmnFilePathPrefix, saveLog.getResourcelocation()).getPath();
        //-- TODO 重新部署逻辑
        DeploymentBuilder deploymentBuilder = bpmRepositoryService.createDeployment();

        deploymentBuilder
                .name(name)
                .tenantId(tenantId)
                .addInputStream(pathResource, new ByteArrayInputStream(CommonUtils.getBpmnFileTextByPath(pathResource).getBytes("UTF-8")));
        //.addInputStream(pathResource, FileUtils.readFileToString(pathResource,"UTF-8"));

        if (deploymentBuilder.getResourceNames().isEmpty()) {
            result.put("pathResource", pathResource);
            throw new IllegalStateException("部署失败！获取" + typeMap.get(type) + "文件内容为空！");
        }

        DeploymentWithDefinitionsDto deployment = DeploymentWithDefinitionsDto.fromDeployment(deploymentBuilder.deployWithResult());

        String deployId = deployment.getId();


        ocProcessdefinition.setProcDefId(deployId);
        ocProcessdefinition.setStatus("1");
        ocProcessdefinition.setDeployFromVersion(Integer.valueOf(version));
        processDefRepository.save(ocProcessdefinition);


        // 获取并保存流程定义ID
        String processDefinitionId = deployment.getDeployedProcessDefinitions().keySet().stream().findFirst().orElse(null);


        saveLog.setProcessDefinitionId(processDefinitionId);

        saveLogRepository.save(saveLog);

        OCProcessdefinitionDeployDetail deployDetail = new OCProcessdefinitionDeployDetail();
        deployDetail.setOcProcessId(id);
        if ("dmn".equals(type)) {
            deployDetail.setDecisionDefId(deployId);
        } else {
            deployDetail.setProcessDepId(deployId);
        }

        detailRepository.save(deployDetail);
        return result;
    }

    private static void setMapList(OCProcessdefinition ocProcessdefinition, List<Map<String, String>> mapList) {
        Map<String, String> map = new HashMap<>();
        map.put("procDefId", CommonUtils.isEmpty(ocProcessdefinition.getProcDefId()) ? "" : ocProcessdefinition.getProcDefId());
        map.put("id", ocProcessdefinition.getId());
        map.put("name", ocProcessdefinition.getName());
        map.put("status", CommonUtils.getStatusByCode(ocProcessdefinition.getStatus()));
        map.put("created", ocProcessdefinition.getCreated());
        map.put("type", ocProcessdefinition.getType().toUpperCase());
        mapList.add(map);
    }

    private static Map<String, String> convertOCProcessdefinitionToMap(OCProcessdefinition ocProcessdefinition) {
        Map<String, String> map = new HashMap<>();
        map.put("procDefId", CommonUtils.isEmpty(ocProcessdefinition.getProcDefId()) ? "" : ocProcessdefinition.getProcDefId());
        map.put("id", ocProcessdefinition.getId());
        map.put("name", ocProcessdefinition.getName());
        map.put("status", CommonUtils.getStatusByCode(ocProcessdefinition.getStatus()));
        map.put("created", ocProcessdefinition.getCreated());
        map.put("type", ocProcessdefinition.getType().toUpperCase());
        return map;
    }

    public OcResponse getAppProcessInfo(String appDefId) {
        List result = new ArrayList();
        List saveLogs = new ArrayList();
        List<OCProcessdefinition> processdefinitions = processDefRepository.findAll();
        processdefinitions.stream().forEach(
                iter -> {
                    try {
                        Map map = jsonConverter.fromJson(jsonConverter.toJson(iter), Map.class);
                        result.add(map);
                    } catch (Exception e) {
                        log.error("getAppProcessInfo error", e);
                        e.printStackTrace();
                    }
                }
        );

        List<OCProcessdefinitionSaveLog> processdefinitionSaveLogs = saveLogRepository.findAll();
        Collections.sort(processdefinitionSaveLogs, new Comparator<OCProcessdefinitionSaveLog>() {
            @Override
            public int compare(OCProcessdefinitionSaveLog o1, OCProcessdefinitionSaveLog o2) {
                if (o1.getVersion() != null && o2.getVersion() != null) {
                    return o1.getVersion().compareTo(o2.getVersion());
                } else {
                    return 0;
                }
            }
        });
        processdefinitionSaveLogs.stream().forEach(
                iter -> {
                    try {
                        Map map = jsonConverter.fromJson(jsonConverter.toJson(iter), Map.class);
                        saveLogs.add(map);
                    } catch (Exception e) {
                        log.error("OCProcessdefinitionSaveLog error", e);
                        e.printStackTrace();
                    }
                }
        );

        Map mapReturn = new HashMap();
        mapReturn.put("OCProcessdefinition", result);
        mapReturn.put("OCProcessdefinitionSaveLog", saveLogs);
        return CommonUtils.getOcResponse(mapReturn);
    }

    private Boolean verifyProcessKeyAndProcessDefId(String key, String processDefId) {
        return saveLogRepository.findAll().stream().filter(f -> !processDefId.equals(f.getOcprocessdefinitionId()) && key.equals(f.getProcessDepKey())).count() == 0;
    }

    private String getProcessKeyIfIsModified(String key, String processDefId) {
        if (saveLogRepository.findByOcprocessdefinitionId(processDefId).size() == 0) {
            return "";
        }
        String keyId = saveLogRepository.findByOcprocessdefinitionId(processDefId).get(0).getProcessDepKey();
        return key.equals(keyId) ? "" : keyId;
    }

    private String getProcessNameIfIsDeployed(String processDefId) {
        if (detailRepository.findByOcProcessId(processDefId).size() == 0) {
            return "";
        }
        return processDefRepository.getOne(processDefId).getName();
    }

    public OcResponse<?> importAppProcessInfo(String id, String body) {

        AppInfo appInfo = new AppInfo();
        appInfo.setTenantId("0");
        appInfo.setAppDefId(id);
        appInfo.setAppInstId(id);
        AppInfoContext.setApp(appInfo);

        Map<String, List> map = jsonConverter.fromJson(body, Map.class);
        List<Map> process = map.get("OCProcessdefinition");
        List<Map> oCProcessdefinitionSaveLog = map.get("OCProcessdefinitionSaveLog");

        List<OCProcessdefinition> ocProcessdefinitions = new ArrayList<>();
        process.stream().forEach(iter -> {
            OCProcessdefinition OCProcessdefinition = jsonConverter.fromJson(jsonConverter.toJson(iter), OCProcessdefinition.class);

            if (StringUtils.isNotEmpty(MapUtil.getStr(iter, "status")) && "1".equals(MapUtil.getStr(iter, "status"))) {
                OCProcessdefinition.setStatus("0");
            }
            ocProcessdefinitions.add(OCProcessdefinition);
        });
        processDefRepository.saveAll(ocProcessdefinitions);

        Map<String, String> deployMap = new HashMap<>();

        List<OCProcessdefinitionSaveLog> ocProcessdefinitionSaveLogs = new ArrayList<>();
        //ListSort(ocProcessdefinitionSaveLogs);

        oCProcessdefinitionSaveLog.stream().forEach(iter -> {
            OCProcessdefinitionSaveLog saveLog = jsonConverter.fromJson(jsonConverter.toJson(iter), OCProcessdefinitionSaveLog.class);
            if (iter.containsKey("deployed")) {
                System.out.println("--");
            }
            ocProcessdefinitionSaveLogs.add(saveLog);
        });


        // 删除所有 saveLog，再重新插入
        for (OCProcessdefinitionSaveLog iter : ocProcessdefinitionSaveLogs) {
            if (iter.getDeployed() != null && iter.getDeployed()) {
                deployMap.put(iter.getOcprocessdefinitionId(), String.valueOf(iter.getVersion()));
            }
        }
        saveLogRepository.deleteAll();
        saveLogRepository.saveAllAndFlush(ocProcessdefinitionSaveLogs);

        //saveLogRepository.saveAll(ocProcessdefinitionSaveLogs);

        log.info("importAppProcessInfo:" + jsonConverter.toJson(deployMap));

        deployMap.forEach((processDefinitionId, version) -> {
            try {
                self.deployProcessdefByOCProcessdefId(processDefinitionId, version);
            } catch (Exception exception) {
                log.error("部署流程定义:{}，版本：{}时出错", processDefinitionId, version);
            }
        });

        return CommonUtils.getOcResponse("success", null);
    }
}
