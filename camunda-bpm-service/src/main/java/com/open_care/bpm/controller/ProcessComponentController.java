package com.open_care.bpm.controller;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.service.ProcessComponentService;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import lombok.extern.log4j.Log4j2;
import org.apache.tomcat.util.http.ResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 流程组件控制器
 *
 * <AUTHOR>
 */
@RestController

@Log4j2
public class ProcessComponentController {
    
    @Autowired
    private ProcessComponentService processComponentService;
    


} 