package com.open_care.bpm.engine;

import cn.hutool.core.util.StrUtil;
import com.open_care.api.common.delete.DeleteParamRequest;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.GetRequestDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.dto.crud.SaveRequestDTO;
import com.open_care.api.common.edit.EditDataResponse;
import com.open_care.api.common.query.QueryDataResponse;
import com.open_care.bpm.dto.ProcessCategoryDTO;
import com.open_care.bpm.service.ProcessCategoryService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date :2025/2/27
 */
@RestController
@RequestMapping
public class CrudController implements InitializingBean {

    @Autowired
    protected ProcessCategoryService processCategoryService;
    public Map<String, Function<QueryRequestDTO, OcResponse<QueryDataResponse<?>>>> queryMap = new HashMap<>();
    public Map<String, Function<SaveRequestDTO, OcResponse<EditDataResponse>>> saveMap = new HashMap<>();
    public Map<String, BiFunction<GetRequestDTO, String, OcResponse<EditDataResponse>>> getMap = new HashMap<>();
    public Map<String, BiFunction<SaveRequestDTO, String, OcResponse<EditDataResponse>>> updateMap = new HashMap<>();
    public Map<String, Function<DeleteParamRequest,OcResponse<Void>>> deleteMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        queryMap.put(ProcessCategoryDTO.class.getName(), processCategoryService::query);
        saveMap.put(ProcessCategoryDTO.class.getName(), processCategoryService::save);
        getMap.put(ProcessCategoryDTO.class.getName(), processCategoryService::get);
        updateMap.put(ProcessCategoryDTO.class.getName(),processCategoryService::update);
        deleteMap.put(ProcessCategoryDTO.class.getName(), processCategoryService::delete);
    }


    @PostMapping("/query")
    public OcResponse<QueryDataResponse<?>> query(@RequestBody QueryRequestDTO queryRequestDTO) {
        if (queryMap.containsKey(queryRequestDTO.getEntityName())) {
            return queryMap.get(queryRequestDTO.getEntityName()).apply(queryRequestDTO);
        }
        return entityHandlerNotExisted(queryRequestDTO.getEntityName());
    }


    @PostMapping("/save")
    public OcResponse<EditDataResponse> save(@RequestBody SaveRequestDTO saveRequestDTO) {
        if (saveMap.containsKey(saveRequestDTO.getEntityName())) {
            return saveMap.get(saveRequestDTO.getEntityName()).apply(saveRequestDTO);
        }
        return entityHandlerNotExisted(saveRequestDTO.getEntityName());
    }

    @PutMapping("/update/{id}")
    public OcResponse<EditDataResponse> update(@PathVariable("id") String id, @RequestBody SaveRequestDTO saveRequestDTO){
        if (updateMap.containsKey(saveRequestDTO.getEntityName())) {
            return updateMap.get(saveRequestDTO.getEntityName()).apply(saveRequestDTO,id);
        }
        return entityHandlerNotExisted(saveRequestDTO.getEntityName());
    }


    @PostMapping("/delete")
    OcResponse<Void> delete(@RequestBody DeleteParamRequest deleteParamRequest){
        if(deleteMap.containsKey(deleteParamRequest.getEntityName())){
            return deleteMap.get(deleteParamRequest.getEntityName()).apply(deleteParamRequest);
        }
        return entityHandlerNotExisted(deleteParamRequest.getEntityName());
    }

    //@Override
    @RequestMapping(value = {"/get/{entityName}/{id}"}, method = {RequestMethod.POST})
    public OcResponse<EditDataResponse> get(@RequestBody GetRequestDTO getRequestDTO, @PathVariable("entityName") String entityName, @PathVariable("id") String id) {
        if (getMap.containsKey(entityName)) {
            return getMap.get(entityName).apply(getRequestDTO, id);
        }
        return entityHandlerNotExisted(getRequestDTO.getEntityName());
    }


    public <T> OcResponse<T> entityHandlerNotExisted(String entityName) {
        OcResponse<T> response = new OcResponse<>();
        response.setStatus("-1");
        response.setMsg(StrUtil.format("{} 找不到处理器", entityName));
        return response;
    }

}
