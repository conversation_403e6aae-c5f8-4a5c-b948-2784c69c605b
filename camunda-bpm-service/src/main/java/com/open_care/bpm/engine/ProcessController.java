/**
 * Copyright 2018-2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.engine;

import cn.hutool.core.lang.Pair;
import com.open_care.api.client.ProcessRemoteService;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.query.QueryDataResponse;
import com.open_care.api.dto.process.CompleteProcessTaskDTO;
import com.open_care.api.enums.SortType;
import com.open_care.api.enums.TaskAcceptingUserType;
import com.open_care.api.common.dto.ProcessHistoryDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationRequestDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationResultDTO;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.dto.ProcessInstQueryRequestDTO;
import com.open_care.bpm.dto.ProcessModificationDTO;
import com.open_care.bpm.dto.TaskHistoryQueryRequestDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.dto.TaskQueryRequestDTO;
import com.open_care.bpm.dto.TaskTransferDTO;
import com.open_care.bpm.dto.TaskTransferLogDTO;
import com.open_care.bpm.dto.TaskTransferLogQueryRequestDTO;
import com.open_care.bpm.enums.PriorityEnum;
import com.open_care.bpm.service.ProcessEngineService;
import com.open_care.bpm.service.ProcessInstanceService;
import com.open_care.bpm.service.TaskAssigneeService;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.bpm.utils.PageUtils;
import com.open_care.util.json.JsonConverter;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Log4j2
@RestController
@RequestMapping
public class ProcessController implements ProcessRemoteService {
    @Autowired
    ProcessEngineService processEngineService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    TaskService taskService;

    @Autowired
    RepositoryService repositoryService;

    @Autowired
    HistoryService historyService;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    private TaskAssigneeService taskAssigneeService;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Override
    public OcResponse startProcessInstanceByKey(@PathVariable("processDefinitionKey") String processDefinitionKey, @RequestBody String body) {
        return processEngineService.startProcessInstanceByKey(processDefinitionKey, body);
    }

    @Override
    public OcResponse<TaskInfoDTO> getTaskByTaskId(@PathVariable("taskId") String taskId) {
        return processEngineService.getTaskByTaskId(taskId);
    }

    @Override
    public OcResponse<?> processModification(ProcessModificationDTO processModificationDTO) {
        return processEngineService.processModification(processModificationDTO);
    }

    @Override
    public OcResponse startProcessInstanceById(@PathVariable(value = "processDefinitionId") String processDefinitionId, @RequestBody String body) {
        return processEngineService.startProcessInstanceById(processDefinitionId, body);
    }

    @Override
    public OcResponse startProcessInstanceByKeyAndRunFirstTask(@PathVariable(value = "processDefinitionKey") String processDefinitionKey, @RequestBody String body) {
        return processEngineService.startProcessInstanceByKeyAndRunFirstTask(processDefinitionKey, body);
    }

    @Override
    public OcResponse startProcessInstanceByIdAndRunFirstTask(@PathVariable(value = "processDefinitionId") String processDefinitionId, @RequestBody String body) {
        return processEngineService.startProcessInstanceByIdAndRunFirstTask(processDefinitionId, body);
    }

    @Override
    public OcResponse candidateTask(@PathVariable(value = "taskId") String taskId, @RequestBody Map body) {
        return processEngineService.candidateTask(taskId, body);
    }

    @Override
    public OcResponse claimTask(@PathVariable(value = "taskId") String taskId, @PathVariable(value = "userId") String userId) {
        return processEngineService.claimTask(taskId, userId);
    }

    @Override
    public OcResponse<?> claimTask(Set<String> taskIds, String userId) {
        return processEngineService.claimTask(taskIds, userId);
    }

    @Override
    public OcResponse unclaimTask(@PathVariable("taskId") String taskId) {
        return processEngineService.unclaimTask(taskId);
    }

    @Override
    public OcResponse<Object> unclaimTask(Set<String> taskIds) {
        return processEngineService.unclaimTask(taskIds);
    }

    @Override
    public OcResponse queryIdentityEntityByTaskId(String taskId) {
        return processEngineService.queryIdentityEntityByTaskId(taskId);
    }

    @Override
    public OcResponse<String> modifyTaskPriorityByTaskId(String taskId, PriorityEnum priorityEnum) {
        return processEngineService.modifyTaskPriorityByTaskId(taskId, priorityEnum);
    }

    @Override
    public OcResponse delegateTask(@PathVariable(value = "taskId") String taskId, @PathVariable(value = "userId") String userId) {
        return processEngineService.delegateTask(taskId, userId);
    }

    @Override
    public OcResponse delegateTask(@RequestBody Set<String> taskIds, @PathVariable(value = "userId") String userId) {
        return processEngineService.delegateTask(taskIds, userId);
    }

    @Override
    public OcResponse<?> assigneeTask(@PathVariable(value = "taskId") String taskId, @PathVariable(value = "userId") String userId) {
        return processEngineService.assigneeTask(taskId, userId);
    }

    @Override
    public OcResponse<?> assigneeTask(@RequestBody Set<String> taskIds, @PathVariable(value = "userId") String userId) {
        return processEngineService.assigneeTask(taskIds, userId);
    }

    @Override
    public OcResponse resolveTask(@PathVariable(value = "taskId") String taskId, @RequestBody String body) {
        return processEngineService.resolveTask(taskId, body);
    }

    @Override
    public OcResponse completeTask(@PathVariable(value = "taskId") String taskId, @RequestBody Map<String, Object> variables) {
        return processEngineService.completeTask(taskId, variables);
    }

    @Override
    public ResponseEntity<String> setTaskVariables(@PathVariable(value = "taskId") String taskId, @RequestBody String body) {
        return processEngineService.setTaskVariables(taskId, body);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByProcDefKey(@PathVariable(value = "processDefinitionKey") String processDefinitionKey) {
        TaskQueryRequestDTO taskQueryRequestDTO = TaskQueryRequestDTO.builder()
                .processDefinitionKey(processDefinitionKey)
                .build();

        return ResponseEntity.ok(
                jsonConverter.toJson(
                        processEngineService.findUserTasks(taskQueryRequestDTO).getValue()
                )
        );
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByProcDef(@PathVariable(value = "processDefinitionId") String processDefinitionId) {
        return processEngineService.getCurrentTasksByProcDef(processDefinitionId);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByProcInst(@PathVariable(value = "processInstanceId") String processInstanceId) {
        return processEngineService.getCurrentTasksByProcInst(processInstanceId);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByUser(@PathVariable(value = "userId") String userId) {
        return ResponseEntity.ok(
                jsonConverter.toJson(
                        processEngineService.findUserTasksByUserIds(Collections.singleton(userId), SortType.ASC)
                )
        );
    }

    @Override
    public ResponseEntity<Long> getCurrentTasksCountByUser(@PathVariable("userId") String userId) {
        return ResponseEntity.ok(processEngineService.findUserTasksCountByUserId(Collections.singleton(userId)));
    }

    @Override
    public OcResponse<QueryDataResponse<List<TaskInfoDTO>>> findUserTasksWithPagination(TaskQueryRequestDTO taskQueryRequestDTO) {
        PageUtils.checkPageValid(taskQueryRequestDTO.getPage());
        Pair<Long, List<TaskInfoDTO>> userTaskAndTotalCount = processEngineService.findUserTasks(taskQueryRequestDTO);
        taskQueryRequestDTO.getPage().setTotal(userTaskAndTotalCount.getKey().intValue());

        QueryDataResponse<List<TaskInfoDTO>> queryDataResponse = new QueryDataResponse<>();
        queryDataResponse.setPagination(taskQueryRequestDTO.getPage());
        queryDataResponse.setData(userTaskAndTotalCount.getValue());
        return CommonUtils.getOcResponse(queryDataResponse);
    }

    @Override
    public OcResponse<QueryDataResponse<List<TaskInfoDTO>>> findUserTaskHistoryWithPagination(TaskHistoryQueryRequestDTO taskQueryRequestDTO) {
        PageUtils.checkPageValid(taskQueryRequestDTO.getPage());
        Pair<Long, List<TaskInfoDTO>> userTaskAndTotalCount = processEngineService.findUserTasksHistory(taskQueryRequestDTO);
        taskQueryRequestDTO.getPage().setTotal(userTaskAndTotalCount.getKey().intValue());

        QueryDataResponse<List<TaskInfoDTO>> queryDataResponse = new QueryDataResponse<>();
        queryDataResponse.setPagination(taskQueryRequestDTO.getPage());
        queryDataResponse.setData(userTaskAndTotalCount.getValue());
        return CommonUtils.getOcResponse(queryDataResponse);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByUserAndProcDefId(@PathVariable(value = "userId") String userId,
                                                                    @PathVariable(value = "processDefinitionId") String processDefinitionId,
                                                                    @RequestBody Map body
    ) {
        TaskQueryRequestDTO taskQueryRequestDTO = TaskQueryRequestDTO.builder()
                .userId(userId)
                .processDefinitionId(processDefinitionId)
                .taskAcceptingUserType(
                        TaskAcceptingUserType.typeOf(
                                findUserTaskType(body)
                        )
                )
                .build();

        return ResponseEntity.ok(
                jsonConverter.toJson(
                        processEngineService.findUserTasks(taskQueryRequestDTO).getValue()
                )
        );
    }

    private String findUserTaskType(Map body) {
        return body.containsKey("body") ? ((Map) body.get("body")).get("type").toString() : body.get("type").toString();
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByUserAndProcDefKey(@PathVariable(value = "userId") String userId,
                                                                     @PathVariable(value = "processDefinitionKey") String processDefinitionKey,
                                                                     @RequestBody Map body
    ) {
        TaskQueryRequestDTO taskQueryRequestDTO = TaskQueryRequestDTO.builder()
                .userId(userId)
                .processDefinitionKey(processDefinitionKey)
                .taskAcceptingUserType(
                        TaskAcceptingUserType.typeOf(
                                findUserTaskType(body)
                        )
                )
                .build();

        return ResponseEntity.ok(
                jsonConverter.toJson(
                        processEngineService.findUserTasks(taskQueryRequestDTO).getValue()
                )
        );
    }

    @Override
    public ResponseEntity<String> getHistoryTasksByUserAndProcDef(@PathVariable(value = "userId") String userId, @PathVariable(value = "processDefinitionId") String processDefinitionId) {
        return processEngineService.getHistoryTasksByUserAndProcDef(userId, processDefinitionId);
    }

    @Override
    public ResponseEntity<String> getTasksByProcessKey(@PathVariable(value = "processDefinitionId") String processDefinitionId) {
        return new ResponseEntity<>(jsonConverter.toJson(processEngineService.getTaskDefByProcessDefinition(processDefinitionId)), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<String> getTaskDelegateUsersByTaskId(@PathVariable(value = "taskId") String taskId) {
        return processEngineService.getTaskDelegateUsersByTaskId(taskId);
    }

    @Override
    public ResponseEntity<String> getTaskAssigneeUsersByTaskId(@PathVariable(value = "taskId") String taskId) {
        return processEngineService.getTaskAssigneeUsersByTaskId(taskId);
    }

    @Override
    public ResponseEntity<String> getProcessInstances() {
        return new ResponseEntity<>(processEngineService.getProcessInstances(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<String> clearProcessInstances() {
        return new ResponseEntity<>(processEngineService.clearProcessInstances(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<String> processDefinitions() {
        return new ResponseEntity<String>(jsonConverter.toJson(processEngineService.getProcessDefinitions()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<String> userTaskDefs() {
        return new ResponseEntity<String>(processEngineService.getUserTaskDefinitions(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<String> getProcessDefinitionXml(@PathVariable(value = "processDefinitionId") String processDefinitionId) {
        return new ResponseEntity<String>(processEngineService.getProcessDefinitionXml(processDefinitionId), HttpStatus.OK);
    }

    /**
     * 获取流程实例的历史信息，包括执行历史和流程图
     *
     * @param processInstanceId 流程实例ID
     * @return 包含流程历史信息的响应对象
     */
    @Override
    public OcResponse<ProcessHistoryDTO> getHistoryTasksByProcInst(@PathVariable(value = "processInstanceId") String processInstanceId) {
        return processEngineService.getHistoryTasksByProcInst(processInstanceId);
    }

    @Override
    public OcResponse deleteProcessInstance(@PathVariable(value = "processInstanceId") String processInstanceId, @RequestBody String body) {
        return processEngineService.deleteProcessInstance(processInstanceId, body);
    }

    @Override //运行中的任务无法删除
    public OcResponse deleteTask(@PathVariable(value = "taskId") String taskId, @RequestBody String body) {
        return processEngineService.deleteTask(taskId, body);
    }

    @Override
    public OcResponse getTaskAuditHistory(@PathVariable(value = "processInstanceId") String processInstanceId) {
        return processEngineService.getTaskAuditHistory(processInstanceId);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByUsers(@RequestBody String body) {
        return processEngineService.getCurrentTasksByUsers(((List<String>) jsonConverter.fromJson(body, List.class)));
    }

    @Override
    public ResponseEntity<String> getCurrentTasks() {
        return processEngineService.getCurrentTasks();
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByTaskDefKey(@PathVariable(value = "taskDefKey") String taskDefKey) {
        return processEngineService.getCurrentTasksByTaskDefKey(taskDefKey);
    }

    @Override
    public ResponseEntity<String> getHistoryTasksByTaskDefKey(@PathVariable(value = "taskDefKey") String taskDefKey) {
        return processEngineService.getHistoryTasksByTaskDefKey(taskDefKey);
    }

    @Override
    public ResponseEntity<String> getHistoryTasksByProcessInstId(@PathVariable(value = "processInstanceId") String processInstanceId) {
        return processEngineService.getHistoryTasksByProcessInstId(processInstanceId);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByTaskDefKeys(@RequestBody List<String> taskDefKeys) {

        return processEngineService.getCurrentTasksByTaskDefKeys(taskDefKeys);
    }

    @Override
    public ResponseEntity<String> getHistoryTasksByTaskDefKeys(@RequestBody List<String> taskDefKeys) {
        return null;
    }

    @Override
    public OcResponse batchCompleteTasks(@RequestBody List<CompleteProcessTaskDTO> completeTaskDTOList) {
        return processEngineService.batchCompleteTasks(completeTaskDTOList);
    }

    @Override
    public ResponseEntity<String> getCurrentTasksByProcInstIds(@RequestBody List<String> procInstIds) {
        return processEngineService.getCurrentTasksByProcInstIds(procInstIds);
    }

    @Override
    public ResponseEntity<String> getHistoryTasksByUserId(@PathVariable(value = "userId") String userId) {
        return processEngineService.getHistoryTasksByUserId(userId);
    }

    @Override
    public OcResponse restartProcessInstances(String s) {
        return null;
    }

    @Override
    public OcResponse<QueryDataResponse<List<TaskTransferLogDTO>>> findTaskTransferLog(@RequestBody TaskTransferLogQueryRequestDTO taskTransferLogQueryRequestDTO) {
        return taskAssigneeService.findTaskTransferLog(taskTransferLogQueryRequestDTO);
    }

    @Override
    public OcResponse<List<TaskTransferLogDTO>> taskTransfer(@RequestBody TaskTransferDTO taskTransferDTO) {
        return taskAssigneeService.taskTransfer(taskTransferDTO);
    }


    @Override
    public OcResponse<QueryDataResponse<List<ProcessInstDTO>>> findProcessInstWithPagination(ProcessInstQueryRequestDTO requestDTO) {
        // 校验分页参数
        PageUtils.checkPageValid(requestDTO.getPagination());

        // 调用服务进行查询
        Pair<Long, List<ProcessInstDTO>> result = processInstanceService.findProcessInstances(requestDTO);

        // 设置分页信息
        requestDTO.getPagination().setTotal(result.getKey().intValue());

        // 构建响应
        QueryDataResponse<List<ProcessInstDTO>> queryDataResponse = new QueryDataResponse<>();
        queryDataResponse.setPagination(requestDTO.getPagination());
        queryDataResponse.setData(result.getValue());
        queryDataResponse.setAuthority(new ArrayList<>());

        return CommonUtils.getOcResponse(queryDataResponse);
    }

    /**
     * 迁移流程定义实例
     * 支持将某一个流程key的某个版本或者这个版本之前的所有版本迁移到最新版本或者某个具体的版本
     *
     * @param requestDTO 迁移请求参数
     * @return 迁移结果
     */
    @Override
    public OcResponse<ProcessDefinitionMigrationResultDTO> migrateProcessDefinitionInstances(ProcessDefinitionMigrationRequestDTO requestDTO) {
        try {
            log.info("开始迁移流程定义实例: {}, 源版本: {}, 目标版本: {}",
                    requestDTO.getProcessDefinitionKey(),
                    requestDTO.getSourceVersion(),
                    requestDTO.getTargetVersion());

            ProcessDefinitionMigrationResultDTO resultDTO = processInstanceService.migrateProcessDefinitionInstances(requestDTO);

            log.info("流程定义实例迁移完成: {}, 成功: {}, 失败: {}",
                    requestDTO.getProcessDefinitionKey(),
                    resultDTO.getSuccessCount(),
                    resultDTO.getFailureCount());

            return CommonUtils.getOcResponse("流程定义实例迁移完成", resultDTO);
        } catch (IllegalArgumentException e) {
            log.warn("迁移流程定义实例参数错误: {}", e.getMessage());
            return CommonUtils.getErrorOcResponse("迁移流程定义实例参数错误: " + e.getMessage(), null);
        } catch (Exception e) {
            log.error("迁移流程定义实例时发生错误: {}", requestDTO.getProcessDefinitionKey(), e);
            return CommonUtils.getErrorOcResponse("迁移流程定义实例失败: " + e.getMessage(), null);
        }
    }
}
