/**
 * Copyright 2019-2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.engine;


import cn.hutool.core.lang.Pair;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.bpmn.ProcessInstanceModificationDTO;
import com.open_care.api.common.export.Page;
import com.open_care.api.enums.SortType;
import com.open_care.bpm.command.DeleteTaskCommand;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.dto.TaskQueryRequestDTO;
import com.open_care.bpm.service.ProcessEngineService;
import com.open_care.bpm.service.ProcessProductService;
import com.open_care.bpm.utils.CommonUtils;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.ManagementService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.IdentityLinkType;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:lee
 * @Description:
 * @Date:2019-07-18 15:56
 * @Modified By:
 */
@RestController
@RequestMapping("/bpmTest")
public class BpmTestController {

    @Autowired
    ProcessEngineService processEngineService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    TaskService taskService;

    @Autowired
    HistoryService historyService;

    @Autowired
    ProcessProductService processProductService;

    @Autowired
    ManagementService managementService;

    @Autowired
    CommandExecutor commandExecutor;

    @PostMapping("/test456/{processInstId}/{activityId}")
    public OcResponse startBeforeActivity(@PathVariable(value = "processInstId") String processInstId,
                                          @PathVariable(value = "activityId") String activityId,
                                          @RequestBody ProcessInstanceModificationDTO body) {
        List<HistoricTaskInstance> historicTaskInstances =
                historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstId)
                        .taskDefinitionKey(activityId).orderByHistoricActivityInstanceStartTime().desc().list();

        HistoricTaskInstance historicTaskInstance = historicTaskInstances.get(0);

        List<HistoricActivityInstance> historicActivityInstances =
                historyService.createHistoricActivityInstanceQuery()
                        .executionId(historicTaskInstance.getExecutionId())
                        .processInstanceId(processInstId)
                        .list();

        ProcessInstanceModificationBuilder processInstanceModificationBuilder =
                runtimeService.createProcessInstanceModification(processInstId)
                        .startBeforeActivity(activityId);
        //.cancelAllForActivity("ParallelGateway_End")

        for (HistoricActivityInstance iter : historicActivityInstances) {
            if ("parallelGateway".equals(iter.getActivityType())) {
                processInstanceModificationBuilder =
                        processInstanceModificationBuilder.cancelActivityInstance(iter.getId());
            }
        }

        processInstanceModificationBuilder.execute();
        return CommonUtils.getOcResponse("success");
    }

    @GetMapping("/test1")
    public OcResponse test1() {
        String taskId = "f10ffa5a-11de-11eb-b46a-00ff49280e92";
        Task task = taskService.createTaskQuery().taskId(taskId).list().get(0);

        runtimeService.createProcessInstanceModification(task.getProcessInstanceId())
                .startBeforeActivity(task.getTaskDefinitionKey())//会签节点的activityId
                .setVariable("assignee", "cccccc")
                .execute();

        return CommonUtils.getOcResponse("success");
    }

    @GetMapping("deleteTask/{taskId}")
    public OcResponse deleteTask(@PathVariable("taskId") String taskId) {
        DeleteTaskCommand deleteTaskCommand = new DeleteTaskCommand(taskId);

        commandExecutor.execute(deleteTaskCommand);
        return CommonUtils.getOcResponse("success");
    }

    @GetMapping("findUserTask/{userId}")
    public OcResponse<Pair<Long, List<TaskInfoDTO>>> findUserTasksIfUserTypeNotNullTest(@PathVariable String userId) {
        Page page = new Page();
        page.setCurrent(0);
        page.setPageSize(10);
        return CommonUtils.getOcResponse(processEngineService.findUserTasks(TaskQueryRequestDTO.builder().userId(userId).page(page).createTimeSortType(SortType.DESC).build()));
    }

    @GetMapping("findUserTasksByUserIds/{userId}")
    public OcResponse<List<TaskInfoDTO>> findUserTasksByUserIds(@PathVariable String userId) {
        Page page = new Page();
        page.setCurrent(0);
        page.setPageSize(10);
        return CommonUtils.getOcResponse(processEngineService.findUserTasksByUserIds(Collections.singleton(userId), SortType.DESC));
    }

    @GetMapping("findUserTasksByUserIds/")
    public Object findUserTasksByUserIds1() {

        List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask("5d2d6fbc-7712-11ef-8b9b-0242ac130011");
        return identityLinksForTask.stream()
                .filter(identityLink -> IdentityLinkType.CANDIDATE.equals(identityLink.getType()) && identityLink.getUserId() != null)
                .map(IdentityLink::getUserId)
                .collect(Collectors.toList());
//        return identityLinksForTask;
    }
}
