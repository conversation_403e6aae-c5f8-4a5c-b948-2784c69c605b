/**
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.engine;

import cn.hutool.core.collection.CollUtil;
import com.open_care.api.client.ProcessEngineRemoteService;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.bpmn.CombineDTO;
import com.open_care.api.common.dto.bpmn.NewTaskDTO;
import com.open_care.api.common.dto.bpmn.ProcessInstanceModificationDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.bpm.constants.ProcessConst;
import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessRequestDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessResponseDTO;
import com.open_care.bpm.service.*;
import com.open_care.bpm.utils.CommonUtils;
import com.open_care.util.json.JsonConverter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.ResponseUtil;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.runtime.Execution;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@Log4j2
public class ProcessEngineController implements ProcessEngineRemoteService {

    @Autowired
    ProcessService processService;

    @Autowired
    ProcessProductService processProductService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    ProcessEngineService processEngineService;

    @Autowired
    TaskService taskService;

    @Autowired
    HistoryService historyService;

    @Autowired
    private ProcessComponentService processComponentService;

    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Override
    public OcResponse queryProcessDefList(@RequestBody QueryRequestDTO queryRequestDTO) {
        return processService.queryProcessDefList(queryRequestDTO);
    }

    @Override
    public String getProcessDefList(@PathVariable(value = "page") int page, @PathVariable(value = "size") int size) {
        return processService.getProcessDefList(page, size);
    }

    @Override
    public OcResponse<ProcessInfoDTO> saveOcprocessDefInfo(@PathVariable(value = "id") String id, @RequestBody String body) {
        return processService.saveOcprocessDefInfo(id, body);
    }

    @Override
    public OcResponse renameById(@PathVariable(value = "id") String id, @RequestBody String body) {
        return processService.renameById(id, body);
    }

    @Override
    public OcResponse getOcprocessDefInfoById(@PathVariable(value = "id") String id, @PathVariable(value = "version") Integer version) {
        return processService.getOcprocessDefInfoById(id, version);
    }

    @Override
    public OcResponse getOcprocessDefInfoByIdWithOutVersion(String id) {
        return processService.getOcprocessDefInfoById(id, null);
    }

    @Override
    public OcResponse deployProcessdefByOCProcessdefId(@PathVariable(value = "id") String id, @PathVariable(value = "version") String version) {
        log.info("deployProcessdefByOCProcessdefId id：{} version：{}", id, version);
        try {
            return processService.deployProcessdefByOCProcessdefId(id, version);
        } catch (Exception exception) {
            log.error("部署流程：{}，版本：{}时出错", id, version);
            return CommonUtils.getErrorOcResponse("部署失败！", exception.getMessage());
        }
    }

    @Override
    public OcResponse getAppProcessInfo(@PathVariable(value = "appDefId") String appDefId) {
        return processService.getAppProcessInfo(appDefId);
    }

    @Override
    public OcResponse importAppProcessInfo(@PathVariable(value = "appDefId") String appDefId, @RequestBody String body) {
        return processService.importAppProcessInfo(appDefId, body);
    }

    @Override
    public OcResponse createProductProcess(@PathVariable(value = "productId") String productId,
                                           @PathVariable(value = "processKey") String processKey,
                                           @RequestBody Map<String, Object> body) {
        String productProcessKey = processProductService.createProductProcess(productId, processKey, body);
        return CommonUtils.getOcResponse("success", productProcessKey);
    }

    @Override
    public OcResponse createPackageProcess(@PathVariable(value = "productId") String productId,
                                           @PathVariable(value = "processKey") String processKey,
                                           @RequestBody CombineDTO body) {
        String productProcessKey = processProductService.createPackageProcess(productId, processKey, body);
        return CommonUtils.getOcResponse("success", productProcessKey);
    }

    @Override
    public OcResponse startBeforeActivity(@PathVariable(value = "processInstId") String processInstId,
                                          @PathVariable(value = "activityId") String activityId,
                                          @RequestBody ProcessInstanceModificationDTO body) {
        HistoricProcessInstance historicProcessInstance = getHistoryFinishedProcessInstance(processInstId, activityId);
        if (Objects.nonNull(historicProcessInstance)) {
            return processEngineService.restartProcessInstances(historicProcessInstance, activityId);
        }

        if (!checkProcessInstanceIsExist(processInstId)) {
            return CommonUtils.getErrorOcResponse("processInstId '" + processInstId + "' not exists in runtime");
        }

        ProcessInstanceModificationBuilder processInstanceModificationBuilder =
                runtimeService.createProcessInstanceModification(processInstId)
                        .startBeforeActivity(activityId)
                        .setVariable(ProcessConst.TASK_VAR_IS_CREATED_BY_ROLLBACK, Boolean.TRUE);

        if (CollUtil.isEmpty(body.getCancelActivities())) {
            for (HistoricActivityInstance iter : processEngineService.getHistoricActivityInstance(processInstId, activityId)) {
                //TODO 需继续调查哪些类型的任务可以被取消()
                //TODO 如果并行网关之后的任务已经创建，并行网关这个activityInst cancel会出错
                //TODO 将！去掉后，取消弃检之类的操作会出错
                if (!"parallelGateway".equals(iter.getActivityType())) {
                    continue;
                }

                List<String> activeActivityIds = new ArrayList<>();
                //判断executionId是否存在
                List<Execution> executions = runtimeService.createExecutionQuery().executionId(iter.getExecutionId()).list();
                if (CollUtil.isNotEmpty(executions)) {
                    activeActivityIds = runtimeService.getActiveActivityIds(iter.getExecutionId());
                }
                log.error("startBeforeActivity activeActivityIds:" + processInstId + "," + activityId + "," + activeActivityIds);
                if (CollUtil.isNotEmpty(executions)) {
                    if (CollUtil.isEmpty(activeActivityIds) || activeActivityIds.contains(iter.getId())) {
                        processInstanceModificationBuilder =
                                processInstanceModificationBuilder.cancelActivityInstance(iter.getId());
                    }
                }
            }
        } else {
            for (String iter : body.getCancelActivities()) {
                if (StringUtils.isNotEmpty(iter)) {
                    processInstanceModificationBuilder = processInstanceModificationBuilder.cancelAllForActivity(iter);
                }
            }
        }

        processInstanceModificationBuilder.execute();
        return CommonUtils.getOcResponse("success");
    }

    @Override
    public OcResponse startAfterActivity(@PathVariable(value = "processInstId") String processInstId,
                                         @PathVariable(value = "activityId") String activityId,
                                         @RequestBody ProcessInstanceModificationDTO body) {
        if (!checkProcessInstanceIsExist(processInstId)) {
            return CommonUtils.getErrorOcResponse("processInstId is not exist");
        }

        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstId)
                .startAfterActivity(activityId);

        for (String iter : body.getCancelActivities()) {
            processInstanceModificationBuilder = processInstanceModificationBuilder.cancelAllForActivity(iter);
        }

        processInstanceModificationBuilder.execute();
        return CommonUtils.getOcResponse("success");
    }

    @Override
    public OcResponse cancelAllForActivity(@PathVariable(value = "processInstId") String processInstId,
                                           @PathVariable(value = "activityId") String activityId,
                                           @RequestBody ProcessInstanceModificationDTO body) {
        HistoricProcessInstance historicProcessInstance = getHistoryFinishedProcessInstance(processInstId, activityId);
        if (Objects.nonNull(historicProcessInstance)) {
            return processEngineService.restartProcessInstances(historicProcessInstance, activityId);
        }

        if (!checkProcessInstanceIsExist(processInstId)) {
            return CommonUtils.getErrorOcResponse("processInstId is not exist");
        }

        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstId)
                .cancelAllForActivity(activityId);

        for (String iter : body.getStartBeforeActivities()) {
            processInstanceModificationBuilder = processInstanceModificationBuilder.startBeforeActivity(iter);
        }

        processInstanceModificationBuilder.execute();
        return CommonUtils.getOcResponse("success");
    }

    private boolean checkProcessInstanceIsExist(String processInstanceId) {
        return CollUtil.isNotEmpty(runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).list());
    }

    private HistoricProcessInstance getHistoryFinishedProcessInstance(String processInstanceId, String activityId) {
        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery().
                processInstanceId(processInstanceId).finished().orderByProcessInstanceEndTime().desc().list();

        if (CollUtil.isNotEmpty(historicProcessInstances)) {
            return historicProcessInstances.get(0);
        }

        return null;
    }

    @Override
    public OcResponse addMultiInstance(@RequestBody NewTaskDTO newTaskDTO) {
        if (StringUtils.isNotEmpty(newTaskDTO.getTaskId())) {
            Task task = taskService.createTaskQuery().taskId(newTaskDTO.getTaskId()).list().get(0);
            newTaskDTO.setProcessInstId(task.getProcessInstanceId());
            newTaskDTO.setTaskDefKey(task.getTaskDefinitionKey());
        }

        if (CollUtil.isNotEmpty(newTaskDTO.getAssignees())) {
            newTaskDTO.getAssignees().stream().forEach(
                    iter -> {
                        runtimeService.createProcessInstanceModification(newTaskDTO.getProcessInstId())
                                .startBeforeActivity(newTaskDTO.getTaskDefKey())//会签节点的activityId
                                .setVariable("assignee", iter)
                                .execute();
                    }
            );
        }

        if (StringUtils.isNotEmpty(newTaskDTO.getTaskId())) {
            taskService.complete(newTaskDTO.getTaskId(), newTaskDTO.getBody());
        }
        return CommonUtils.getOcResponse("success");
    }

    /**
     * 获取所有流程组件
     *
     * @return 流程组件列表
     */
    @Override
    public OcResponse<List<ProcessComponentDTO>> getAllProcessComponents() {
        log.info("获取所有流程组件");
        List<ProcessComponentDTO> components = processComponentService.getAllComponents();

        OcResponse<List<ProcessComponentDTO>> response = new OcResponse<>();
        response.setStatus("0");
        response.setData(components);
        return response;
    }

    /**
     * 保留流程实例（保存成新流程实例，后端会自动更改 processName 和 processDefinitionKey）
     *
     * @param requestDTO
     * @return
     */
    @Override
    public OcResponse<SaveAndDeployThenMigrateProcessResponseDTO> saveAndDeployThenMigrateProcess(SaveAndDeployThenMigrateProcessRequestDTO requestDTO) {
        log.info("接收到流程保存、部署与迁移请求: {}", jsonConverter.toJson(requestDTO));
        try {
            SaveAndDeployThenMigrateProcessResponseDTO responseDTO = processDefinitionService.saveAndDeployThenMigrateProcess(requestDTO);
            return CommonUtils.getOcResponse(responseDTO.getMsg(), responseDTO);
        } catch (Exception e) {
            log.error("流程保存、部署与迁移处理异常", e);
            return CommonUtils.getErrorOcResponse("处理异常: " + e.getMessage(), null);
        }
    }
}
