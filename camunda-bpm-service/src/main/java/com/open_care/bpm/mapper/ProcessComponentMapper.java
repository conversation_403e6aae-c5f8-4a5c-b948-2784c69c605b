package com.open_care.bpm.mapper;

import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import com.open_care.camunda.entity.ProcessComponent;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 流程组件映射器
 * 使用MapStruct进行Entity和DTO之间的转换
 *
 * <AUTHOR>
 */
@Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true)
)
public interface ProcessComponentMapper {

    ProcessComponentMapper INSTANCE = Mappers.getMapper(ProcessComponentMapper.class);

    /**
     * 将实体转换为DTO
     *
     * @param entity 流程组件实体
     * @return 流程组件DTO
     */
    ProcessComponentDTO toDto(ProcessComponent entity);
    
    /**
     * 将DTO转换为实体
     *
     * @param dto 流程组件DTO
     * @return 流程组件实体
     */
    ProcessComponent toEntity(ProcessComponentDTO dto);
    
    /**
     * 将实体列表转换为DTO列表
     *
     * @param entities 流程组件实体列表
     * @return 流程组件DTO列表
     */
    List<ProcessComponentDTO> toDtoList(List<ProcessComponent> entities);
    
    /**
     * 将组件类型转换为其标签名称
     *
     * @param componentType 组件类型枚举
     * @return 组件类型标签名称
     */
    @Named("componentTypeToLabel")
    default String componentTypeToLabel(ProcessComponentTypeEnum componentType) {
        return componentType != null ? componentType.getLabel() : null;
    }
} 