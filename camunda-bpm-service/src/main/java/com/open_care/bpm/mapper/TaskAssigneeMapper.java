package com.open_care.bpm.mapper;

import com.open_care.bpm.dto.TaskAssignLogDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.dto.TaskTransferLogDTO;
import com.open_care.bpm.entity.CandidateGroup;
import com.open_care.bpm.entity.CandidateUser;
import com.open_care.bpm.entity.TaskAssigneeLog;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务转办数据转换Mapper
 *
 * <AUTHOR>
 */
@Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true)
)
public interface TaskAssigneeMapper {
    TaskAssigneeMapper INSTANCE = Mappers.getMapper(TaskAssigneeMapper.class);
    
    /**
     * 复制TaskInfoDTO属性到TaskTransferLogDTO
     * 
     * @param source 源TaskInfoDTO对象
     * @param target 目标TaskTransferLogDTO对象
     * @return 复制属性后的目标对象
     */
    TaskTransferLogDTO copyTaskInfoToTransferLogDTO(TaskInfoDTO source, @MappingTarget TaskTransferLogDTO target);
    
    /**
     * 将TaskAssigneeLog转换为TaskTransferLogDTO
     * 
     * @param source 源TaskAssigneeLog对象
     * @return 转换后的TaskTransferLogDTO对象
     */
    @Mapping(target = "taskId", source = "taskId")
    @Mapping(target = "taskName", source = "taskName")
    @Mapping(target = "processInstanceId", source = "processInstanceId")
    @Mapping(target = "processDefinitionId", source = "processDefinitionId")
    @Mapping(target = "taskDefinitionKey", source = "taskDefinitionKey")
    @Mapping(target = "tenantId", source = "tenantId")
    @Mapping(target = "owner", source = "originalOwner")
    @Mapping(target = "name", source = "taskName")
    @Mapping(target = "assignee", source = "newAssignee")
    @Mapping(target = "assignLog", expression = "java(createAssignLogDTO(source))")
    TaskTransferLogDTO toTaskTransferLogDTO(TaskAssigneeLog source);


    /**
     * 创建任务转办日志DTO
     *
     * @param source 源TaskAssigneeLog对象
     * @return 转办日志DTO
     */
    @Named("createAssignLogDTO")
    default TaskAssignLogDTO createAssignLogDTO(TaskAssigneeLog source) {
        if (source == null) {
            return null;
        }

        // 获取新的候选用户标识符列表
        List<String> newCandidateUsers = source.getNewCandidateUsers() != null ?
                source.getNewCandidateUsers().stream()
                        .map(CandidateUser::getIdentifier)
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        // 获取原始候选用户标识符列表
        List<String> originalCandidateUsers = source.getOriginalCandidateUsers() != null ?
                source.getOriginalCandidateUsers().stream()
                        .map(CandidateUser::getIdentifier)
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        // 获取新的候选组标识符列表
        List<String> newCandidateGroups = source.getNewCandidateGroups() != null ?
                source.getNewCandidateGroups().stream()
                        .map(CandidateGroup::getIdentifier)
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        // 获取原始候选组标识符列表
        List<String> originalCandidateGroups = source.getOriginalCandidateGroups() != null ?
                source.getOriginalCandidateGroups().stream()
                        .map(CandidateGroup::getIdentifier)
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        // 构建并返回转办日志DTO
        return TaskAssignLogDTO.builder()
                .owner(source.getOriginalOwner())
                .assignee(source.getNewAssignee())
                .candidateUsers(newCandidateUsers)
                .candidateGroups(newCandidateGroups)
                .assignTime(source.getAssignTime())
                .assignReason(source.getAssignReason())
                .assignRemark(source.getAssignRemark())
                .originalAssignee(source.getOriginalAssignee())
                .originalCandidateUsers(originalCandidateUsers)
                .originalCandidateGroups(originalCandidateGroups)
                .operator(source.getOperator())
                .build();
    }
} 