package com.open_care.bpm.mapper;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.bpm.dto.IdentityEntityDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.TaskAssignLogDTO;
import com.open_care.bpm.dto.TaskCandidates;
import com.open_care.bpm.dto.TaskHandleLogDTO;
import com.open_care.bpm.enums.TaskCategoryEnum;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.open_care.api.common.dto.TaskPropertiesDTO;
import com.open_care.api.common.enums.TaskStateEnum;
import com.open_care.bpm.core.ProcessVariableUtil;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.service.TaskAssigneeService;
import com.open_care.camunda.util.ExtensionPropertyUtils;
import com.open_care.camunda.util.PropertyJsonWrapper;
import com.open_care.camunda.util.TaskConst;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.log4j.Log4j2;

/**
 * Camunda任务对象与DTO转换器
 */
@Component
@Log4j2
public class TaskMapper {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private TaskAssigneeService taskAssigneeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 批量转换Task列表为TaskInfoDTO列表
     *
     * @param tasks                任务列表
     * @param taskCandidatesMap    任务候选人映射
     * @param processInstanceMap   流程实例映射
     * @param processDefinitionMap 流程定义映射
     * @param taskVariablesMap     任务变量映射
     * @param processVariablesMap  流程变量映射
     * @return TaskInfoDTO列表
     */
    public List<TaskInfoDTO> convertToDTOList(
            List<Task> tasks,
            Map<String, TaskCandidates> taskCandidatesMap,
            Map<String, ProcessInstance> processInstanceMap,
            Map<String, ProcessDefinition> processDefinitionMap,
            Map<String, Map<String, Object>> processVariablesMap,
            Map<String, Map<String, Object>> taskVariablesMap) {

        if (CollUtil.isEmpty(tasks)) {
            return new ArrayList<>();
        }

        List<TaskInfoDTO> taskInfoDTOList = new ArrayList<>();

        for (Task task : tasks) {
            TaskInfoDTO taskDTO = convertToDTO(task);

            // 设置候选人信息
            setTaskCandidates(taskDTO, task.getId(), taskCandidatesMap);

            //设置变量
            taskDTO.setVariables(taskVariablesMap.get(task.getId()));

            // 设置流程实例和流程定义信息
            setProcessInstanceInfo(taskDTO, task.getProcessInstanceId(), processInstanceMap, processDefinitionMap, processVariablesMap);

            // 设置任务变量
            postInitTaskVariables(taskDTO, task.getId(), taskVariablesMap, processVariablesMap);


            taskInfoDTOList.add(taskDTO);
        }

        return taskInfoDTOList;
    }

    /**
     * 批量转换历史任务列表为TaskInfoDTO列表
     *
     * @param historicTasks        历史任务列表
     * @param taskCandidatesMap    任务候选人映射
     * @param processInstanceMap   历史流程实例映射
     * @param processDefinitionMap 流程定义映射
     * @param taskVariablesMap     任务变量映射
     * @param processVariablesMap  流程变量映射
     * @return TaskInfoDTO列表
     */
    public List<TaskInfoDTO> convertHistoricTasksToDTOList(
            List<HistoricTaskInstance> historicTasks,
            Map<String, TaskCandidates> taskCandidatesMap,
            Map<String, HistoricProcessInstance> processInstanceMap,
            Map<String, ProcessDefinition> processDefinitionMap,
            Map<String, Map<String, Object>> taskVariablesMap,
            Map<String, Map<String, Object>> processVariablesMap) {

        if (CollUtil.isEmpty(historicTasks)) {
            return new ArrayList<>();
        }

        List<TaskInfoDTO> taskInfoDTOList = new ArrayList<>();

        for (HistoricTaskInstance historicTask : historicTasks) {
            // 获取流程名称
            String processName = Optional.ofNullable(processInstanceMap.get(historicTask.getProcessInstanceId()))
                    .map(HistoricProcessInstance::getProcessDefinitionName)
                    .orElse(null);

            // 转换基本信息
            TaskInfoDTO taskDTO = convertToDTO(historicTask, processName);

            // 设置候选人信息
            setTaskCandidates(taskDTO, historicTask.getId(), taskCandidatesMap);

            taskDTO.setVariables(taskVariablesMap.get(historicTask.getId()));

            // 设置流程实例和业务键信息
            setHistoricProcessInstanceInfo(taskDTO, historicTask.getProcessInstanceId(), processInstanceMap, processDefinitionMap, processVariablesMap);

            // 设置任务变量
           postInitTaskVariables(taskDTO, historicTask.getId(), taskVariablesMap, processVariablesMap);

            taskInfoDTOList.add(taskDTO);
        }

        return taskInfoDTOList;
    }

    /**
     * 设置流程定义信息
     */
    private void setProcessDefinitionInfo(ProcessInstDTO processInstDTO, ProcessDefinition processDefinition) {
        if (ObjectUtil.isNotNull(processDefinition)) {
            processInstDTO.setProcessInfo(ProcessInfoDTO.builder()
                    .processDefinitionId(processDefinition.getId())
                    .processDefinitionKey(processDefinition.getKey())
                    .processDefinitionName(processDefinition.getName())
                    .name(processDefinition.getName())
                    .build());

            // 设置流程标题
            processInstDTO.setProcessTitle(processDefinition.getName());
        }
    }

    /**
     * 设置流程实例信息
     */
    private void setProcessInstInfo(
            TaskInfoDTO taskDTO,
            ProcessInstance processInstance,
            ProcessDefinition processDefinition,
            Map<String, Map<String, Object>> processVariablesMap
    ) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();
        processInstDTO.setVariables(processVariablesMap.get(processInstance.getId()));
        processInstDTO.setEnded(processInstance.isEnded());
        processInstDTO.setSuspended(processInstance.isSuspended());

        processInstDTO.setId(processInstance.getId());
        processInstDTO.setBusinessKey(processInstance.getBusinessKey());
        processInstDTO.setRemark(ProcessVariableUtil.getRemarkFromVariables(processInstDTO.getVariables()));


        // 添加更多流程实例信息
        try {
            // 获取流程实例的变量
            Map<String, Object> variables = runtimeService.getVariables(processInstance.getId());

            // 设置流程实例状态
            processInstDTO.setCommitter(ProcessVariableUtil.getCommitterFromVariables(processInstDTO.getVariables()));
            processInstDTO.setCommitTime(ProcessVariableUtil.getCommitTimeFromVariables(processInstDTO.getVariables()));


            // 获取流程实例的开始时间
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstance.getId())
                    .singleResult();

            if (historicProcessInstance != null) {
                processInstDTO.setCommitTime(historicProcessInstance.getStartTime());
                processInstDTO.setLastCommitTime(
                        historicProcessInstance.getEndTime() != null ? historicProcessInstance.getEndTime()
                                : historicProcessInstance.getStartTime());
            }
        } catch (Exception e) {
            log.warn("获取流程实例额外信息失败", e);
        }

        setProcessDefinitionInfo(processInstDTO, processDefinition);
        taskDTO.setProcessInst(processInstDTO);
    }

    /**
     * 设置历史流程实例信息
     */
    private void setHistoricProcessInstInfo(
            TaskInfoDTO taskDTO,
            HistoricProcessInstance historicProcessInstance,
            ProcessDefinition processDefinition,
            Map<String, Map<String, Object>> processVariablesMap
    ) {
        ProcessInstDTO processInstDTO = new ProcessInstDTO();
        processInstDTO.setEnded(true);
        processInstDTO.setEndTime(historicProcessInstance.getEndTime());
        processInstDTO.setSuspended(false);

        processInstDTO.setId(historicProcessInstance.getId());


        processInstDTO.setVariables(processVariablesMap.getOrDefault(processInstDTO.getId(), new HashMap<>()));
        processInstDTO.setBusinessKey(historicProcessInstance.getBusinessKey());
        processInstDTO.setRemark(ProcessVariableUtil.getRemarkFromVariables(processInstDTO.getVariables()));


        // 添加更多流程实例信息
        try {
            // 设置流程实例状态
            processInstDTO.setCommitter(ProcessVariableUtil.getCommitterFromVariables(processInstDTO.getVariables()));
            processInstDTO
                    .setCommitterName(ProcessVariableUtil.getCommitterFromVariables(processInstDTO.getVariables()));
            processInstDTO.setCommitTime(ProcessVariableUtil.getCommitTimeFromVariables(processInstDTO.getVariables()));

            processInstDTO.setLastCommitTime(
                    historicProcessInstance.getEndTime() != null ? historicProcessInstance.getEndTime()
                            : historicProcessInstance.getStartTime());
        } catch (Exception e) {
            log.warn("获取历史流程实例额外信息失败", e);
        }

        setProcessDefinitionInfo(processInstDTO, processDefinition);
        taskDTO.setProcessInst(processInstDTO);
    }

    public TaskInfoDTO convertToDTO(Task taskEntity) {
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setTaskId(taskEntity.getId());
        dto.setOwner(taskEntity.getOwner());
        dto.setAssignee(taskEntity.getAssignee());
        dto.setName(taskEntity.getName());
        dto.setTaskName(taskEntity.getName());
        dto.setDescription(taskEntity.getDescription());
        dto.setTenantId(taskEntity.getTenantId());
        dto.setTitle(taskEntity.getName());
        dto.setProcessInstanceId(taskEntity.getProcessInstanceId());
        dto.setProcessDefinitionId(taskEntity.getProcessDefinitionId());
        dto.setProcessName(null);
        dto.setProcessDefinitionKey(null);
        dto.setType("todoTasks");
        dto.setTaskDefinitionKey(taskEntity.getTaskDefinitionKey());
        dto.setCreateTime(taskEntity.getCreateTime());
        dto.setTaskCreateTime(taskEntity.getCreateTime());
        dto.setPriority(taskEntity.getPriority());
        dto.setTaskState(TaskStateEnum.valueOfState(taskEntity.getTaskState()));

        // 设置任务类别：如果assignee为null则是共享任务，否则是个人任务
        dto.setTaskCategory(taskEntity.getAssignee() == null ? TaskCategoryEnum.SHARED_POOL_TASKS : TaskCategoryEnum.PERSONAL_TASKS);

        // 获取任务扩展属性
        PropertyJsonWrapper properties = ExtensionPropertyUtils.getAllPropertiesAsJsonWrapper(taskEntity, taskService,
                repositoryService);

        // 设置任务属性
        boolean isAssignment = properties.getBooleanByPrefixPathSuffix(
                ExtensionPropertyUtils.compose(TaskConst.TASK, TaskConst.IS_ASSIGNMENT), TaskConst.EMPTY, false,
                TaskConst.ENABLE);
        TaskPropertiesDTO taskPropertiesDTO = TaskPropertiesDTO.builder()
                .enableTaskAssign(taskAssigneeService.isTaskAssigneeEnabled(properties))
                .enableTaskMultiAssign(taskAssigneeService.isTaskMultiAssigneeEnabled(properties))
                .isAssignment(isAssignment)
                .build();

        // 设置任务类别
        if (StrUtil.isBlank(dto.getAssignee())) {
            dto.setTaskCategory(TaskCategoryEnum.SHARED_POOL_TASKS);
        } else {
            dto.setTaskCategory(TaskCategoryEnum.PERSONAL_TASKS);
        }

        dto.setProperties(taskPropertiesDTO);

        return dto;
    }

    /**
     * 转换历史任务为DTO
     */
    public TaskInfoDTO convertToDTO(HistoricTaskInstance taskInstance, String processName) {
        TaskInfoDTO dto = new TaskInfoDTO();
        dto.setTaskId(taskInstance.getId());
        dto.setOwner(taskInstance.getOwner());
        dto.setAssignee(taskInstance.getAssignee());
        dto.setName(taskInstance.getName());
        dto.setTaskName(taskInstance.getName());
        dto.setTaskDefinitionKey(taskInstance.getTaskDefinitionKey());
        dto.setProcessInstanceId(taskInstance.getProcessInstanceId());
        dto.setProcessDefinitionId(taskInstance.getProcessDefinitionId());
        dto.setProcessName(processName);
        dto.setTaskCreateTime(taskInstance.getStartTime());
        dto.setCreateTime(taskInstance.getStartTime());
        dto.setEndTime(taskInstance.getEndTime());
        dto.setPriority(taskInstance.getPriority());
        dto.setTaskState(TaskStateEnum.valueOfState(taskInstance.getTaskState()));

        // 设置任务类别：如果assignee为null则是共享任务，否则是个人任务
        dto.setTaskCategory(taskInstance.getAssignee() == null ?
                TaskCategoryEnum.SHARED_POOL_TASKS :
                TaskCategoryEnum.PERSONAL_TASKS);
        return dto;
    }

    /**
     * 转换IdentityLink为DTO
     */
    public IdentityEntityDTO convertToDTO(IdentityLink identityLink, String businessKey, Task task) {
        TaskInfoDTO taskInfoDTO = null;
        if (task instanceof TaskEntity) {
            taskInfoDTO = convertToDTO((TaskEntity) task);
            taskInfoDTO.setBusinessKey(businessKey);
        }
        return new IdentityEntityDTO(
                identityLink.getType(),
                identityLink.getUserId(),
                identityLink.getGroupId(),
                identityLink.getTaskId(),
                identityLink.getProcessDefId(),
                identityLink.getTenantId(),
                taskInfoDTO);
    }

    public void initIsAssignedByOthers(TaskInfoDTO taskInfoDTO) {
        // 获取任务扩展属性
        List<TaskAssignLogDTO> assigneeLogs = ProcessVariableUtil
                .getAssignLogsFromVariables(taskInfoDTO.getVariables());

        // 判断任务是否被他人指派
        boolean isAssignedByOthers = assigneeLogs != null && !assigneeLogs.isEmpty();

        taskInfoDTO.setIsAssignedByOthers(isAssignedByOthers);
    }

    /**
     * 设置任务候选人信息
     */
    private void setTaskCandidates(TaskInfoDTO taskDTO, String taskId, Map<String, TaskCandidates> taskCandidatesMap) {
        if (taskCandidatesMap != null && taskCandidatesMap.containsKey(taskId)) {
            taskDTO.setCandidateUsers(taskCandidatesMap.get(taskId).getCandidateUsers());
            taskDTO.setCandidateGroups(taskCandidatesMap.get(taskId).getCandidateGroups());
        }
    }

    /**
     * 设置任务变量
     */
    private void postInitTaskVariables(TaskInfoDTO taskDTO, String taskId, Map<String, Map<String, Object>> taskVariablesMap, Map<String, Map<String, Object>> processVariablesMap) {
        if (taskVariablesMap != null) {
            if (ObjectUtil.isNotNull(taskDTO.getProcessInst()) || processVariablesMap.containsKey(taskDTO.getProcessInst().getId())) {
                taskDTO.getVariables().putAll(processVariablesMap.getOrDefault(taskDTO.getProcessInst().getId(), new HashMap<>()));
            }
            initIsAssignedByOthers(taskDTO);

            // 设置handleLog属性，将task.complete前缀的变量添加到handleLog中
            setHandleLogFromTaskVariables(taskDTO);
        }
    }

    /**
     * 从任务变量中提取task.complete前缀的变量，设置到TaskInfoDTO的handleLog属性中
     *
     * @param taskDTO 任务DTO
     */
    private void setHandleLogFromTaskVariables(TaskInfoDTO taskDTO) {
        Map<String, Object> variables = taskDTO.getVariables();
        if (MapUtil.isNotEmpty(variables)) {
            // 获取task.complete前缀的变量
            String handler = ProcessVariableUtil.getCompleteHandlerFromTask(taskDTO);
            String handlerName = ProcessVariableUtil.getCompleteHandlerNameFromTask(taskDTO);
            Date handleTime = ProcessVariableUtil.getCompleteHandleTimeFromTask(taskDTO);
            String handleRemark = ProcessVariableUtil.getCompleteHandleRemarkFromTask(taskDTO);
            String handleResult = ProcessVariableUtil.getCompleteHandleResultFromTask(taskDTO);
            Map<String, Object> completeVariables = ProcessVariableUtil.getCompleteVariablesFromTask(taskDTO);

            // 如果存在任何一个task.complete前缀的变量，则创建handleLog对象
            if (handler != null || handlerName != null || handleTime != null ||
                    handleRemark != null || handleResult != null || completeVariables != null) {

                TaskHandleLogDTO handleLog = new TaskHandleLogDTO();
                handleLog.setHandler(handler);
                handleLog.setHandlerName(handlerName);
                handleLog.setHandleTime(handleTime);
                handleLog.setHandleRemark(handleRemark);
                handleLog.setHandleResult(handleResult);
                handleLog.setVariables(completeVariables);

                taskDTO.setHandleLog(handleLog);
            }
        }
    }



    /**
     * 设置流程实例和流程定义信息
     */
    private void setProcessInstanceInfo(TaskInfoDTO taskDTO,
                                        String processInstanceId,
                                        Map<String, ProcessInstance> processInstanceMap,
                                        Map<String, ProcessDefinition> processDefinitionMap,
                                        Map<String, Map<String, Object>> processVariablesMap
    ) {

        try {
            if (processInstanceMap != null && StrUtil.isNotBlank(processInstanceId)) {
                ProcessInstance processInstance = processInstanceMap.get(processInstanceId);
                if (ObjectUtil.isNotNull(processInstance)) {
                    taskDTO.setBusinessKey(processInstance.getBusinessKey());

                    // 设置流程定义相关信息
                    if (processDefinitionMap != null) {
                        String processDefinitionId = processInstance.getProcessDefinitionId();
                        ProcessDefinition processDefinition = processDefinitionMap.get(processDefinitionId);
                        if (ObjectUtil.isNotNull(processDefinition)) {
                            taskDTO.setProcessDefinitionKey(processDefinition.getKey());
                            taskDTO.setProcessName(processDefinition.getName());

                            // 设置流程实例信息
                            setProcessInstInfo(taskDTO, processInstance, processDefinition, processVariablesMap);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置流程实例信息失败", e);
        }
    }

    /**
     * 设置历史流程实例和流程定义信息
     */
    private void setHistoricProcessInstanceInfo(
            TaskInfoDTO taskDTO, String processInstanceId,
            Map<String, HistoricProcessInstance> processInstanceMap,
            Map<String, ProcessDefinition> processDefinitionMap,
            Map<String, Map<String, Object>> processVariablesMap
    ) {

        try {
            if (processInstanceMap != null && StrUtil.isNotBlank(processInstanceId)) {
                HistoricProcessInstance historicProcessInstance = processInstanceMap.get(processInstanceId);
                if (ObjectUtil.isNotNull(historicProcessInstance)) {
                    taskDTO.setBusinessKey(historicProcessInstance.getBusinessKey());

                    // 设置流程定义相关信息
                    if (processDefinitionMap != null) {
                        String processDefinitionId = historicProcessInstance.getProcessDefinitionId();
                        ProcessDefinition processDefinition = processDefinitionMap.get(processDefinitionId);
                        if (ObjectUtil.isNotNull(processDefinition)) {
                            taskDTO.setProcessDefinitionKey(processDefinition.getKey());

                            // 设置流程实例信息
                            setHistoricProcessInstInfo(taskDTO, historicProcessInstance, processDefinition, processVariablesMap);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("设置历史流程实例信息失败", e);
        }
    }
}