package com.open_care.bpm.mapper;

import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.ProcessInstDTO;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date :2025/5/26
 */
@Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true)
)
public interface ProcessInstMapper {
    ProcessInstMapper INSTANCE = Mappers.getMapper(ProcessInstMapper.class);

    ProcessInstDTO toDto(ProcessInstance processInstance);



    default ProcessInstDTO toDto(ProcessInstance processInstance, ProcessDefinition processDefinition){
        ProcessInstDTO dto = toDto(processInstance);

        dto.setProcessInfo(toDto(processDefinition));

        return dto;
    }

    ProcessInfoDTO toDto(ProcessDefinition processDefinition);
}
