package com.open_care.bpm.mapper;

import com.open_care.bpm.entity.OCProcessCategory;
import com.open_care.bpm.dto.ProcessCategoryDTO;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date :2025/2/28
 */
@Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true)
)
public interface ProcessCategoryMapper {

    ProcessCategoryMapper INSTANCE = Mappers.getMapper(ProcessCategoryMapper.class);


    @Mapping(target = "parentCategory", ignore = true)
    @Named("toDtoIgnoreParent")
    ProcessCategoryDTO toDtoIgnoreParent(OCProcessCategory source);

    @Mapping(target = "parentCategory", ignore = true)
    @Mapping(target = "categoryNo", ignore = true)
    OCProcessCategory fromDtoIgnoreParentCategoryNo(ProcessCategoryDTO source);

    @Mapping(target = "parentCategory", ignore = true)
    @Mapping(target = "categoryNo", ignore = true)
    OCProcessCategory mergeIgnoreParentCategoryNo(OCProcessCategory source, @MappingTarget OCProcessCategory target);

    @Mapping(target = "parentCategory", qualifiedByName = "toDtoIgnoreParent")
    ProcessCategoryDTO toDto(OCProcessCategory source);
}
