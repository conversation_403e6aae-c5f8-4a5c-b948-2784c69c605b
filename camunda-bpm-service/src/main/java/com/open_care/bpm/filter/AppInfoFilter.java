/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.open_care.bpm.filter;

import com.open_care.bpm.context.AppInfo;
import com.open_care.bpm.context.AppInfoContext;
import com.open_care.util.json.JsonConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Component
public class AppInfoFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(AppInfoFilter.class);

    @Autowired
    private JsonConverter jsonConverter;



    public AppInfoFilter() {
    }

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        this.initAppInfo((HttpServletRequest)request);
        chain.doFilter(request, response);
    }

    private void initAppInfo(HttpServletRequest request) {
        String appJson = request.getHeader("appInfo");
        if(StringUtils.isNotBlank(appJson)) {
            try {
                appJson = URLDecoder.decode(appJson, "UTF-8");
                AppInfo appInfo = this.jsonConverter.fromJson(appJson, AppInfo.class);
                AppInfoContext.setApp(appInfo);
            } catch (UnsupportedEncodingException var4) {
                log.error("init appInfo error", var4);
            }
        }

    }

    public void destroy() {
    }
}