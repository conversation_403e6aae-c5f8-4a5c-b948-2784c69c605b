/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.filter;

import com.open_care.bpm.context.UserInfo;
import com.open_care.bpm.context.UserInfoContext;
import com.open_care.util.json.JsonConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Component
public class UserInfoFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(UserInfoFilter.class);

    @Autowired
    private JsonConverter jsonConverter;

    public UserInfoFilter() {
    }

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        this.initUserInfo((HttpServletRequest) request);
        chain.doFilter(request, response);
    }

    private void initUserInfo(HttpServletRequest request) {
        String userJson = request.getHeader("userInfo");
        if (StringUtils.isNotBlank(userJson)) {
            try {
                userJson = URLDecoder.decode(userJson, "UTF-8");
                UserInfo userInfo = this.jsonConverter.fromJson(userJson, UserInfo.class);
                UserInfoContext.setUser(userInfo);
            } catch (UnsupportedEncodingException var4) {
                log.error("init userInfo error", var4);
            }
        }

    }

    public void destroy() {
    }
}