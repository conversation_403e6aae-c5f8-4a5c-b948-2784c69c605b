package com.open_care.bpm.utils;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.open_care.api.common.export.Page;

public final class PageUtils {

    /**
     * 判断分页参数合法并支持分页
     *
     * @param page 待校验的分页参数
     * @return true: 支持分页
     */
    public static boolean isSupportedPagination(Page page) {
        return ObjUtil.isNotNull(page) && CompareUtil.compare(page.getCurrent(), 0) >= 0 && CompareUtil.compare(page.getPageSize(), 0) >= 0;
    }

    public static void checkPageValid(Page page) {
        Assert.notNull(page, "分页参数不能为空");
        Assert.isTrue(CompareUtil.compare(page.getCurrent(), 0) >= 0, "分页参数的起始位置不能为空或负数");
        Assert.isTrue(CompareUtil.compare(page.getPageSize(), 0) >= 0, "分页参数的每页条数不能为空或负数");
    }

}
