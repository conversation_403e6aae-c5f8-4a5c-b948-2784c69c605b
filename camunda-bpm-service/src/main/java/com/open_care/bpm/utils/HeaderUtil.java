/**
 * Copyright 2019 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.utils;

import com.open_care.api.common.ServiceException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class HeaderUtil {
    public static HttpServletRequest getHttpServletRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }



    public static Map<String, String> getHeaders(HttpServletRequest request) {
        if(request == null){
            return new HashMap<>();
        }
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }

    public static String getHeader(String key){
        try {
            Map<String, String> headers = getHeaders(getHttpServletRequest());
            if(headers.containsKey(key)) {
                return URLDecoder.decode(headers.get(key),"UTF-8");
            }else{
                return "";
            }
        }catch (UnsupportedEncodingException e){
            throw new ServiceException(e);
        }
    }
}