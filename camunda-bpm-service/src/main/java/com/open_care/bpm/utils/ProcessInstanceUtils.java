/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.utils;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.runtime.ActivityInstance;

import java.util.Objects;

public class ProcessInstanceUtils {
    public static final String ACTIVITY_TYPE_USER_TASK = "userTask";

    public static ActivityInstance getActivityInstanceFromParentActivityInstance(ActivityInstance currActivityInstance, String activityInstanceId) {
        if ( currActivityInstance.getId().equals(activityInstanceId)) {
            return currActivityInstance;
        }

        for ( ActivityInstance activityInstance :  currActivityInstance.getChildActivityInstances() ) {
            ActivityInstance foundActivityInstance = getActivityInstanceFromParentActivityInstance(activityInstance, activityInstanceId);
            if ( foundActivityInstance != null ) {
                return foundActivityInstance;
            }
        }

        return null;
    }

    public static boolean isUserTask(DelegateTask delegateTask) {
        ActivityInstance rootActivityInstance = delegateTask.getProcessEngineServices().getRuntimeService().getActivityInstance(delegateTask.getProcessInstanceId());
        if ( Objects.isNull(rootActivityInstance) ) {
            return false;
        }

        ActivityInstance currActivityInstance = ProcessInstanceUtils.getActivityInstanceFromParentActivityInstance(rootActivityInstance, delegateTask.getExecution().getActivityInstanceId());
        if ( Objects.isNull(currActivityInstance)) {
            return false;
        }

        return StringUtils.equals(currActivityInstance.getActivityType(), ACTIVITY_TYPE_USER_TASK);
    }
}
