/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.open_care.util.json.JacksonJsonConvert;
import com.open_care.util.json.JsonConverter;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * REST API调用工具类
 */
public class RestAPIUtil {
    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送POST请求
     * 
     * @param baseUrl 基础URL
     * @param path 路径
     * @param body 请求体
     * @return 响应
     */
    public static ResponseEntity<String> postForObject(String baseUrl, String path, String body) {
        return postForObject(baseUrl, path, body, null);
    }

    /**
     * 发送POST请求
     * 
     * @param baseUrl 基础URL
     * @param path 路径
     * @param body 请求体
     * @param uriVariables URI变量
     * @return 响应
     */
    public static ResponseEntity<String> postForObject(String baseUrl, String path, String body, Map<String, ?> uriVariables) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(body, headers);
        
        String url = baseUrl + path;
        if (uriVariables != null) {
            return restTemplate.exchange(url, HttpMethod.POST, entity, String.class, uriVariables);
        } else {
            return restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        }
    }

    /**
     * 发送GET请求
     * 
     * @param baseUrl 基础URL
     * @param path 路径
     * @param uriVariables URI变量
     * @return 响应
     */
    public static ResponseEntity<String> getForObject(String baseUrl, String path, Map<String, ?> uriVariables) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        String url = baseUrl + path;
        if (uriVariables != null) {
            return restTemplate.exchange(url, HttpMethod.GET, entity, String.class, uriVariables);
        } else {
            return restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        }
    }

    public static HttpEntity<String> makeHttpEntityWithHeaderAndRequestBody(String requestBody) {
        HttpHeaders headers = getHeaders(getHttpServletRequest());
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return new HttpEntity(requestBody, headers);
    }

    public static HttpEntity<String> makeHttpEntityWithTokenAndRequestBody(String requestBody, String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Authorization", "Bearer " + token);
        return new HttpEntity(requestBody, headers);
    }


    public static HttpServletRequest getHttpServletRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    private static HttpHeaders getHeaders(HttpServletRequest request) {
        if(request == null){
            return null;
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            httpHeaders.add(key, value);
        }
        return httpHeaders;
    }

    public static String getHeader(String key){
        try {
            HttpHeaders headers = getHeaders(getHttpServletRequest());
            if(headers.containsKey(key)) {
                return URLDecoder.decode(getHttpServletRequest().getHeader(key),"UTF-8");
            }else{
                return "";
            }
        }catch (UnsupportedEncodingException e){
            e.printStackTrace();
            return "";
        }
    }

    public static String addTaskInfo(String body,JsonObject taskInfo){
        JsonArray jsonArray = JacksonJsonConvert.JSON_CONVERTER.fromJson(body, JsonArray.class);
        jsonArray.forEach(
                iter->{
                    if(iter.isJsonObject() && iter.getAsJsonObject().has("data")){
                        JsonObject jsonObject = iter.getAsJsonObject().getAsJsonObject("data");
                        jsonObject.add("task", taskInfo);
                    }
                }
        );

        return jsonArray.toString();
    }

    public static HttpEntity<String> makeHttpEntityWithHeaderWithPreferReturnAndRequestBody(boolean preferReturnMinimal, String requestBody) {
        JsonConverter jsonConverter = JacksonJsonConvert.JSON_CONVERTER;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        String preferReturnValue = preferReturnMinimal ? "minimal" : "representation";
        headers.add("Prefer", "return=" + preferReturnValue);
        Map appInfo = new HashMap();
        appInfo.put("tenantId", "0");
        appInfo.put("appDefId", "0");
        appInfo.put("appInstId", "0");
        Map userInfo = new HashMap();
        userInfo.put("userid", "admin");
        headers.add("userInfo", jsonConverter.toJson(userInfo));
        headers.add("appInfo", jsonConverter.toJson(appInfo));
        return new HttpEntity(requestBody, headers);
    }

    public static HttpEntity<String> makeHttpEntityWithHeaderWithPreferReturn(boolean preferReturnMinimal) {
        return makeHttpEntityWithHeaderWithPreferReturnAndRequestBody(preferReturnMinimal, "");
    }
}
