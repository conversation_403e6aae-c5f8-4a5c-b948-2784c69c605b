/**
 * Copyright 2018-2021 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.bpm.service.RemoteService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Objects;

@Service
public class CommonUtils {
    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);

    @Autowired
    RemoteService remoteService;
    @Autowired
    TaskService taskService;

    public static <T> OcResponse<T> getOcResponse(String msg, T result) {
        return new OcResponse<>(msg, result, "0");
    }

    public static <T> OcResponse<T> getOcResponse(T result) {
        return new OcResponse<>("success", result, "0");
    }

    public static <T> OcResponse<T> getErrorOcResponse(String msg, T result) {
        return new OcResponse<>(msg, result, "-1");
    }

    public static OcResponse<Void> getErrorOcResponse(String msg) {
        return new OcResponse<>(msg, null, "-1");
    }

    public static void main(String[] args) {
        OcResponse<Void> errorOcResponse = getErrorOcResponse("123");
        System.out.println(errorOcResponse);
    }

    public static String getStatusByCode(String code) {
        if ("0".equals(code)) {
            return "已保存";
        } else if ("1".equals(code)) {
            return "已部署";
        } else if ("2".equals(code)) {
            return "已禁用";
        } else {
            return "未知";
        }
    }

    public static String getBpmnFileTextByPath(String path) throws Exception {
        File file = new File(path);
        if (!file.exists()) {
            throw new Exception("文件路径不对！" + path);
        }
        return FileUtils.readFileToString(file, "UTF-8");
    }

    public void saveBpmnFileByTenantId(String txt, String pathName) {
        FileUtil.writeString(txt, new File(pathName), "UTF-8");
        logger.info("saveXmlFileByTenantId：" + pathName + "成功！");
    }

    public static boolean createDir(String dirName) {
        File dir = new File(dirName);
        if (!dirName.endsWith(File.separator)) {
            dirName = dirName + File.separator;
        }

        if (dir.mkdirs()) {
            logger.info("创建目录" + dirName + "成功！");
            return true;
        } else {
            logger.info("目录" + dirName + "已存在！");
            return false;
        }
    }

    /**
     * 判断对象是否为空
     */
    public static Boolean isEmpty(Object o) {
        if (o == null) {
            return true;
        }
        if (("").equals(o.toString().trim())) {
            return true;
        }
        if (("null").equals(o.toString().trim())) {
            return true;
        }
        if (("undefined").equals(o.toString().trim())) {
            return true;
        }
        return false;
    }

//    public void sendMessageByTaskId(String taskId) {
//        if (Objects.nonNull(taskService.getVariables(taskId).get("taskAssigneeMessageTemplate"))) {
//            String messageTemplateId = taskService.getVariables(taskId).get("taskAssigneeMessageTemplate").toString();
//            remoteService.messageSend(messageTemplateId);
//        }
//    }

    public static String getFieldValueStringByFieldNameForQueryFilters(final QueryRequestDTO queryRequest, final String fieldName) {
        return CollUtil.isEmpty(queryRequest.getFilters()) ? null : (String) queryRequest.getFilters().stream().filter((iter) -> {
            return StringUtils.equals(iter.getFieldName(), fieldName);
        }).map((iter) -> {
            return iter.getFieldValue().toString();
        }).findFirst().orElse(null);
    }

}
