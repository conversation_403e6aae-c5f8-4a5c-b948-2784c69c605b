package com.open_care.bpm.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.api.enums.SortType;
import com.open_care.api.enums.TaskAcceptingUserType;
import com.open_care.bpm.dto.VariableFilterGroup;
import com.open_care.bpm.dto.LogicalOperator;
import com.open_care.bpm.dto.VariableFilter;
import com.open_care.bpm.dto.TaskQueryRequestDTO;
import com.open_care.bpm.dto.SingleVariableFilter;
import com.open_care.bpm.dto.VariableOperator;
import com.open_care.bpm.dto.VariableScopeType;
import org.camunda.bpm.engine.task.TaskQuery;

public abstract class TaskQueryDTOUtils {
    /**
     * 根据queryDTO构建出来taskQuery
     *
     * @param queryDTO 用户传的
     * @return 构建完成的taskQuery
     */
    public static TaskQuery getUserTasksQuery(TaskQueryRequestDTO queryDTO, TaskQuery taskQuery) {
        taskQuery = baseInfoQuery(queryDTO, taskQuery);


        if (CollUtil.isNotEmpty(queryDTO.getBusinessKeys())) {
            TaskQuery orQuery = taskQuery.or();
            for (String businessKey : queryDTO.getBusinessKeys()) {
                orQuery = orQuery.processInstanceBusinessKeyLike(businessKey);
            }
            taskQuery = orQuery.endOr();
        }

        if (
                (ObjUtil.isNotNull(queryDTO.getTaskAcceptingUserType()) && StrUtil.isNotBlank(queryDTO.getUserId()))
                        || ObjUtil.isNotNull(queryDTO.getUserGroups())
        ) {
            // 创建单个大的OR查询块，所有条件都在这个块内
            taskQuery = taskQuery.or();
            
            if (ObjUtil.isNotNull(queryDTO.getTaskAcceptingUserType()) && StrUtil.isNotBlank(queryDTO.getUserId())) {
                String userId = queryDTO.getUserId();
                TaskAcceptingUserType taskAcceptingUserType = ObjUtil.defaultIfNull(queryDTO.getTaskAcceptingUserType(), TaskAcceptingUserType.All);
                
                switch (taskAcceptingUserType) {
                    case ASSIGNEE:
                        // 仅查询指派给当前用户的任务
                        taskQuery = taskQuery.taskAssignee(userId);
                        break;
                    case CANDIDATE:
                        // 仅查询未指派且当前用户是候选人的任务
                        taskQuery = taskQuery.taskUnassigned().taskCandidateUser(userId);
                        break;
                    case All:
                        // 分别添加两个条件
                        // 在同一个OR条件内添加另一个分支条件
                        // 条件2: 任务未指派且用户是候选人
                        // 需要使用两个API调用，默认它们之间是AND关系
                        taskQuery.taskAssignee(userId).taskCandidateUser(userId);
                        break;
                }
            }


            // 如果有用户组，添加"未指派且用户组是候选组"的条件
            if (ObjUtil.isNotNull(queryDTO.getUserGroups())) {
                // 在同一个OR块中添加这个条件
                taskQuery = taskQuery.taskCandidateGroupIn(queryDTO.getUserGroups());
            }
            
            // 结束OR查询块
            taskQuery = taskQuery.endOr();
        }

        if (CollUtil.isNotEmpty(queryDTO.getTaskIds())) {
            TaskQuery or = taskQuery.or();
            for (String taskId : queryDTO.getTaskIds()) {
                or = or.taskId(taskId);
            }
            taskQuery = or.endOr();
        }

        // 处理变量过滤条件
        // 处理所有过滤条件，TaskQueryRequestDTO中的过滤器之间是AND关系
        for (VariableFilter filter : CollUtil.emptyIfNull(queryDTO.getVariableFilters())) {
            taskQuery = applyFilter(taskQuery, filter);
        }

        // 处理变量Map
        if (CollUtil.isNotEmpty(queryDTO.getVariables())) {
            for (String variableName : queryDTO.getVariables().keySet()) {
                taskQuery = taskQuery.processVariableValueEquals(variableName, queryDTO.getVariables().get(variableName));
            }
        }

        if (ObjUtil.isNotNull(queryDTO.getCreateTimeSortType())) {
            taskQuery = taskQuery.orderByTaskCreateTime();
            taskQuery = ObjUtil.equals(queryDTO.getCreateTimeSortType(), SortType.ASC)
                    ? taskQuery.asc()
                    : taskQuery.desc();
        }


        if (ObjUtil.isNotNull(queryDTO.getPrioritySortType())) {
            taskQuery = taskQuery.orderByTaskPriority();
            taskQuery = ObjUtil.equals(queryDTO.getPrioritySortType(), SortType.ASC)
                    ? taskQuery.asc()
                    : taskQuery.desc();
        }

        return taskQuery;
    }

    /**
     * 应用过滤条件
     *
     * @param taskQuery 任务查询对象
     * @param filter    过滤器
     * @return 更新后的任务查询对象
     */
    private static TaskQuery applyFilter(TaskQuery taskQuery, VariableFilter filter) {
        if (filter instanceof SingleVariableFilter) {
            return applySingleVariableFilter(taskQuery, (SingleVariableFilter) filter);
        } else if (filter instanceof VariableFilterGroup) {
            return applyVariableFilterGroup(taskQuery, (VariableFilterGroup) filter);
        }
        return taskQuery;
    }

    /**
     * 应用单个变量过滤条件
     *
     * @param taskQuery 任务查询对象
     * @param filter    单变量过滤器
     * @return 更新后的任务查询对象
     */
    private static TaskQuery applySingleVariableFilter(TaskQuery taskQuery, SingleVariableFilter filter) {
        String name = filter.getName();
        Object value = filter.getValue();
        VariableOperator operator = filter.getOperator();
        VariableScopeType scope = filter.getVariableScope();

        if (StrUtil.isBlank(name) || operator == null || scope == null) {
            return taskQuery;
        }
        return switch (operator) {
            case EQUALS -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueEquals(name, value)
                    : taskQuery.processVariableValueEquals(name, value);
            case NOT_EQUALS -> scope == VariableScopeType.TASK
                    ? taskQuery.or().taskVariableValueNotEquals(name, value).taskVariableValueEquals(name, null).endOr()
                    : taskQuery.or().processVariableValueNotEquals(name, value).processVariableValueEquals(name, null).endOr();
            case GREATER_THAN -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueGreaterThan(name, value)
                    : taskQuery.processVariableValueGreaterThan(name, value);
            case GREATER_THAN_OR_EQUALS -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueGreaterThanOrEquals(name, value)
                    : taskQuery.processVariableValueGreaterThanOrEquals(name, value);
            case LESS_THAN -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueLessThan(name, value)
                    : taskQuery.processVariableValueLessThan(name, value);
            case LESS_THAN_OR_EQUALS -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueLessThanOrEquals(name, value)
                    : taskQuery.processVariableValueLessThanOrEquals(name, value);
            case LIKE -> scope == VariableScopeType.TASK
                    ? taskQuery.taskVariableValueLike(name, (String) value)
                    : taskQuery.processVariableValueLike(name, (String) value);
            case NOT_LIKE ->
                // Camunda API可能不直接支持taskVariableValueNotLike，需要检查文档
                // 使用等效的方法
                    taskQuery.or().processVariableValueNotLike(name, (String) value).processVariableValueEquals(name, null).endOr();
        };
    }

    /**
     * 应用变量过滤器组条件
     *
     * @param taskQuery   任务查询对象
     * @param filterGroup 变量过滤器组
     * @return 更新后的任务查询对象
     */
    private static TaskQuery applyVariableFilterGroup(TaskQuery taskQuery, VariableFilterGroup filterGroup) {
        if (CollUtil.isEmpty(filterGroup.getFilters())) {
            return taskQuery;
        }

        LogicalOperator operator = filterGroup.getOperator();

        // 如果是OR操作符，需要使用Camunda的OR语法
        if (operator == LogicalOperator.OR) {
            TaskQuery orQuery = taskQuery.or();

            for (SingleVariableFilter filter : filterGroup.getFilters()) {
                orQuery = applySingleVariableFilter(orQuery, filter);
            }

            return orQuery.endOr();
        } else {
            // AND操作符，直接依次应用所有过滤条件
            for (SingleVariableFilter filter : filterGroup.getFilters()) {
                taskQuery = applySingleVariableFilter(taskQuery, filter);
            }

            return taskQuery;
        }
    }

    private static TaskQuery baseInfoQuery(TaskQueryRequestDTO queryDTO, TaskQuery taskQuery) {
        if (StrUtil.isNotBlank(queryDTO.getProcessDefinitionId())) {
            taskQuery = taskQuery.processDefinitionId(queryDTO.getProcessDefinitionId());
        }

        if (StrUtil.isNotBlank(queryDTO.getProcessDefinitionKey())) {
            taskQuery = taskQuery.processDefinitionKey(queryDTO.getProcessDefinitionKey());
        }

        if (ObjUtil.isNotNull(queryDTO.getTaskPriority())) {
            taskQuery = taskQuery.taskPriority(queryDTO.getTaskPriority());
        }

        if (StrUtil.isNotBlank(queryDTO.getProcessInstanceId())) {
            taskQuery = taskQuery.processInstanceId(queryDTO.getProcessInstanceId());
        }

        if (StrUtil.isNotBlank(queryDTO.getTaskId())) {
            taskQuery = taskQuery.taskId(queryDTO.getTaskId());
        }

        if (StrUtil.isNotBlank(queryDTO.getTaskDefinitionKey())) {
            taskQuery = taskQuery.taskDefinitionKey(queryDTO.getTaskDefinitionKey());
        }

        if (StrUtil.isNotBlank(queryDTO.getName())) {
            taskQuery = taskQuery.taskNameLike("%" + queryDTO.getName() + "%");
        }

        if (StrUtil.isNotBlank(queryDTO.getAssignee())) {
            taskQuery = taskQuery.taskAssignee(queryDTO.getAssignee());
        }

        if (StrUtil.isNotBlank(queryDTO.getCandidateUser())) {
            taskQuery = taskQuery.taskCandidateUser(queryDTO.getCandidateUser());
        }

        if (StrUtil.isNotBlank(queryDTO.getCandidateGroup())) {
            taskQuery = taskQuery.taskCandidateGroup(queryDTO.getCandidateGroup());
        }

        if (StrUtil.isNotBlank(queryDTO.getExecutionId())) {
            taskQuery = taskQuery.executionId(queryDTO.getExecutionId());
        }

        if (StrUtil.isNotBlank(queryDTO.getBusinessKey())) {
            taskQuery = taskQuery.processInstanceBusinessKey(queryDTO.getBusinessKey());
        }

        if (ObjUtil.isNotNull(queryDTO.getPriority())) {
            taskQuery = taskQuery.taskPriority(queryDTO.getPriority());
        }

        if(ObjUtil.isNotNull(queryDTO.getCreateTimeStart())){
            taskQuery = taskQuery.taskCreatedAfter(queryDTO.getCreateTimeStart());
        }
        if(ObjUtil.isNotNull(queryDTO.getCreateTimeStart())){
            taskQuery = taskQuery.taskCreatedBefore(queryDTO.getCreateTimeStart());
        }

        return taskQuery;
    }
}
