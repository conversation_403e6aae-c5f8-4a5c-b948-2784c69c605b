/**
 * Copyright 2021 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.aop;

import com.open_care.api.common.OCInterfaceCallException;
import com.open_care.api.common.dto.OcResponse;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Date;
import java.util.Objects;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class OpenCareRestControllerResponseAdvice implements ResponseBodyAdvice<Object> {
    private static final Logger logger = LoggerFactory.getLogger(OpenCareRestControllerResponseAdvice.class);
    @Value("${spring.application.name}")
    private String applicationName;

    public OpenCareRestControllerResponseAdvice() {
    }

    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        return body;
    }

    @ExceptionHandler({Exception.class})
    @ResponseBody
    public Object ExceptionHandler(HttpServletRequest req, Exception ex) {
        if (ex instanceof IllegalStateException) {
            return this.getResponseEntityFromBusinessRuntimeException((IllegalStateException)ex);
        } else if (ex instanceof IllegalArgumentException) {
            return this.getResponseEntityFromBusinessRuntimeException((IllegalArgumentException)ex);
        } else if (ex instanceof OCInterfaceCallException) {
            return this.getResponseEntityFromOCInterfaceCallException((OCInterfaceCallException)ex);
        } else if (ex instanceof UndeclaredThrowableException) {
            Throwable targetEx = ((UndeclaredThrowableException)ex).getUndeclaredThrowable();
            return this.getResponseEntityFromException((Exception)targetEx);
        } else {
            return this.getResponseEntityFromException(ex);
        }
    }

    private OcResponse getResponseEntityFromOCInterfaceCallException(OCInterfaceCallException ex) {
        return Objects.nonNull(ex.getResponse()) ? ex.getResponse() : this.getResponseEntityFromBusinessRuntimeException(ex);
    }

    private OcResponse getResponseEntityFromBusinessRuntimeException(RuntimeException rex) {
        logger.error("业务异常错误:", rex);
        OcResponse result = new OcResponse();
        result.setStatus("-1");
        result.setMsg(rex.getMessage());
        result.setError(this.applicationName + ":" + ExceptionUtils.getStackTrace(rex));
        return result;
    }

    private OcResponse getResponseEntityFromException(Exception ex) {
        String errorId = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss SSS");
        logger.error("system error " + errorId + ":", ex);
        OcResponse result = new OcResponse();
        result.setStatus("-1");
        result.setMsg("系统出现异常错误，请联系管理员");
        result.setError(this.applicationName + ":" + errorId + ":" + ExceptionUtils.getStackTrace(ex));
        return result;
    }
}
