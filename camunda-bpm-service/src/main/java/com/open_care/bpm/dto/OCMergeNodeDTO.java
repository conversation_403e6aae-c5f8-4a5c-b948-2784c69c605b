/**
 * Copyright 2020 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import org.camunda.bpm.model.bpmn.instance.FlowNode;

import java.util.List;

public class OCMergeNodeDTO implements IBaseDTO {
    @OcColumn(title = "流程节点")
    private FlowNode flowNode;

    @OcColumn(title = "前置节点列表")
    private List<FlowNode> previousNodes;

    @OcColumn(title = "后续节点列表")
    private List<FlowNode> SucceedingNodes;

    public FlowNode getFlowNode() {
        return flowNode;
    }

    public void setFlowNode(FlowNode flowNode) {
        this.flowNode = flowNode;
    }

    public List<FlowNode> getPreviousNodes() {
        return previousNodes;
    }

    public void setPreviousNodes(List<FlowNode> previousNodes) {
        this.previousNodes = previousNodes;
    }

    public List<FlowNode> getSucceedingNodes() {
        return SucceedingNodes;
    }

    public void setSucceedingNodes(List<FlowNode> succeedingNodes) {
        SucceedingNodes = succeedingNodes;
    }
}