/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;

import java.util.ArrayList;
import java.util.List;

public class TaskDefinitionDTO implements IBaseDTO {
    @OcColumn(title = "任务定义ID")
    String taskDefinitionId;

    @OcColumn(title = "任务定义名称")
    String taskDefinitionName;

    @OcColumn(title = "参数列表")
    List<ParameterDTO> parameters = new ArrayList<>();

    @OcColumn(title = "属性列表")
    List<PropertyDTO> properties = new ArrayList<>();

    public String getTaskDefinitionId() {
        return taskDefinitionId;
    }

    public void setTaskDefinitionId(String taskDefinitionId) {
        this.taskDefinitionId = taskDefinitionId;
    }

    public String getTaskDefinitionName() {
        return taskDefinitionName;
    }

    public void setTaskDefinitionName(String taskDefinitionName) {
        this.taskDefinitionName = taskDefinitionName;
    }

    public List<ParameterDTO> getParameters() {
        return parameters;
    }

    public void setParameters(List<ParameterDTO> parameters) {
        this.parameters = parameters;
    }

    public List<PropertyDTO> getProperties() {
        return properties;
    }

    public void setProperties(List<PropertyDTO> properties) {
        this.properties = properties;
    }
}