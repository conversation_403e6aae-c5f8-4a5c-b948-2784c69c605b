/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;

import java.util.ArrayList;
import java.util.List;

public class ProcessDefinitionDTO implements IBaseDTO {
    @OcColumn(title = "流程定义ID")
    String processDefinitionId;

    @OcColumn(title = "流程定义名称")
    String processDefinitionName;

    @OcColumn(title = "任务定义列表")
    List<TaskDefinitionDTO> taskDefs = new ArrayList<>();

    @OcColumn(title = "属性列表")
    List<PropertyDTO> properties = new ArrayList<>();

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessDefinitionName() {
        return processDefinitionName;
    }

    public void setProcessDefinitionName(String processDefinitionName) {
        this.processDefinitionName = processDefinitionName;
    }

    public List<TaskDefinitionDTO> getTaskDefs() {
        return taskDefs;
    }

    public void setTaskDefs(List<TaskDefinitionDTO> taskDefs) {
        this.taskDefs = taskDefs;
    }

    public List<PropertyDTO> getProperties() {
        return properties;
    }

    public void setProperties(List<PropertyDTO> properties) {
        this.properties = properties;
    }
}