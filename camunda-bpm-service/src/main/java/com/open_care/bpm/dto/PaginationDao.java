/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;

public class PaginationDao implements IBaseDTO {
    @OcColumn(title = "当前页")
    public int current;

    @OcColumn(title = "总数")
    public int total;

    @OcColumn(title = "每页数量")
    public int pageSize;

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public PaginationDao(int current, int total, int pageSize) {
        this.current = current;
        this.total = total;
        this.pageSize = pageSize;
    }
}