/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;

import java.util.List;
import java.util.Map;

public class ProcessDefListDto implements IBaseDTO {
    @OcColumn(title = "分页信息")
    public PaginationDao pagination;

    @OcColumn(title = "数据列表")
    public List<Map<String, String>> data;

    public PaginationDao getPagination() {
        return pagination;
    }

    public void setPagination(PaginationDao pagination) {
        this.pagination = pagination;
    }

    public List<Map<String, String>> getData() {
        return data;
    }

    public void setData(List<Map<String, String>> data) {
        this.data = data;
    }
}