/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.Getter;
import lombok.Setter;
import org.camunda.bpm.engine.runtime.ProcessInstance;

@Getter
@Setter
public class ProcessInstanceDTO implements IBaseDTO {
    @OcColumn(title = "ID")
    private String id;

    @OcColumn(title = "业务键")
    private String businessKey;

    @OcColumn(title = "租户ID")
    private String tenantId;

    @OcColumn(title = "流程定义ID")
    private String processDefinitionId;

    @OcColumn(title = "是否挂起")
    private Boolean suspended;

    @OcColumn(title = "是否结束")
    private Boolean ended;

    public ProcessInstanceDTO(ProcessInstance processInstance) {
        this.id = processInstance.getId();
        this.businessKey = processInstance.getBusinessKey();
        this.tenantId = processInstance.getTenantId();
        this.processDefinitionId = processInstance.getProcessDefinitionId();
        this.suspended = processInstance.isSuspended();
        this.ended = processInstance.isEnded();
    }
}
