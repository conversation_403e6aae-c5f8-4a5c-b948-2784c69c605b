/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;

public class PropertyDTO implements IBaseDTO {
    @OcColumn(title = "属性名称")
    String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}