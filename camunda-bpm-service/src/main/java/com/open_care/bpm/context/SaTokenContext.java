package com.open_care.bpm.context;

/**
 * <AUTHOR>
 * @date :2025/3/24
 */
public class SaTokenContext {

    public static final String SA_SAME_TOKEN_NAME = "SA_SAME_TOKEN";
    public static final String SA_TOKEN_USER_NAME = "satoken-user";

    private static final ThreadLocal<String> SA_SAME_TOKEN = new ThreadLocal<>();

    private static final ThreadLocal<String> SA_TOKEN_USER = new ThreadLocal<>();


    public static void setSaSameToken(String saSameToken) {
        SA_SAME_TOKEN.set(saSameToken);
    }

    public static String getSaSameToken() {
        return SA_SAME_TOKEN.get();
    }

    public static void setSaTokenUser(String saTokenUser) {
        SA_TOKEN_USER.set(saTokenUser);
    }

    public static String getSaTokenUser() {
        return SA_TOKEN_USER.get();
    }
}
