/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.context;

public class AppInfoContext {
    private static ThreadLocal<AppInfo> appInfo = new ThreadLocal();

    public AppInfoContext() {
    }

    public static AppInfo getApp() {
        return (AppInfo)appInfo.get();
    }

    public static void setApp(AppInfo app) {
        appInfo.set(app);
    }
}
