package com.open_care.bpm.context;

import cn.hutool.core.util.ObjectUtil;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 流程数据上下文类
 * 用于批量获取和缓存流程相关数据，避免重复查询
 *
 * <AUTHOR>
 */
public class ProcessDataContext {

    /**
     * 活动流程实例映射 - 流程实例ID -> ProcessInstance
     */
    private final Map<String, ProcessInstance> processInstanceMap;

    /**
     * 历史流程实例映射 - 流程实例ID -> HistoricProcessInstance
     */
    private final Map<String, HistoricProcessInstance> historicProcessInstanceMap;

    /**
     * 流程定义映射 - 流程定义ID -> ProcessDefinition
     */
    private final Map<String, ProcessDefinition> processDefinitionMap;

    /**
     * 历史流程变量映射 - 流程实例ID -> Map<变量名, 变量值>
     */
    private final Map<String, Map<String, Object>> historicVariablesMap;

    /**
     * 构造方法
     */
    public ProcessDataContext() {
        this.processInstanceMap = new HashMap<>();
        this.historicProcessInstanceMap = new HashMap<>();
        this.processDefinitionMap = new HashMap<>();
        this.historicVariablesMap = new HashMap<>();
    }

    /**
     * 构造方法
     *
     * @param processInstanceMap         活动流程实例映射
     * @param historicProcessInstanceMap 历史流程实例映射
     * @param processDefinitionMap       流程定义映射
     * @param historicVariablesMap       历史流程变量映射
     */
    public ProcessDataContext(
            Map<String, ProcessInstance> processInstanceMap,
            Map<String, HistoricProcessInstance> historicProcessInstanceMap,
            Map<String, ProcessDefinition> processDefinitionMap,
            Map<String, Map<String, Object>> historicVariablesMap) {
        this.processInstanceMap = processInstanceMap;
        this.historicProcessInstanceMap = historicProcessInstanceMap;
        this.processDefinitionMap = processDefinitionMap;
        this.historicVariablesMap = historicVariablesMap;
    }

    /**
     * 添加活动流程实例
     *
     * @param processInstances 流程实例列表
     */
    public void addProcessInstances(List<ProcessInstance> processInstances) {
        if (ObjectUtil.isNotEmpty(processInstances)) {
            Map<String, ProcessInstance> tempMap = processInstances.stream()
                    .collect(Collectors.toMap(ProcessInstance::getId, Function.identity()));
            this.processInstanceMap.putAll(tempMap);
        }
    }

    /**
     * 添加历史流程实例
     *
     * @param historicProcessInstances 历史流程实例列表
     */
    public void addHistoricProcessInstances(List<HistoricProcessInstance> historicProcessInstances) {
        if (ObjectUtil.isNotEmpty(historicProcessInstances)) {
            Map<String, HistoricProcessInstance> tempMap = historicProcessInstances.stream()
                    .collect(Collectors.toMap(HistoricProcessInstance::getId, Function.identity()));
            this.historicProcessInstanceMap.putAll(tempMap);
        }
    }

    /**
     * 添加流程定义
     *
     * @param processDefinitions 流程定义列表
     */
    public void addProcessDefinitions(List<ProcessDefinition> processDefinitions) {
        if (ObjectUtil.isNotEmpty(processDefinitions)) {
            Map<String, ProcessDefinition> tempMap = processDefinitions.stream()
                    .collect(Collectors.toMap(ProcessDefinition::getId, Function.identity()));
            this.processDefinitionMap.putAll(tempMap);
        }
    }

    /**
     * 添加历史流程变量
     *
     * @param historicVariables 历史流程变量列表
     */
    public void addHistoricVariables(List<HistoricVariableInstance> historicVariables) {
        if (ObjectUtil.isNotEmpty(historicVariables)) {
            Map<String, List<HistoricVariableInstance>> groupedVariables = historicVariables.stream()
                    .collect(Collectors.groupingBy(HistoricVariableInstance::getProcessInstanceId));

            groupedVariables.forEach((processInstanceId, variablesList) -> {
                Map<String, Object> variablesMap = variablesList.stream()
                        .filter(variable -> ObjectUtil.isNotNull(variable.getName()))
                        .filter(variable -> ObjectUtil.isNotNull(variable.getValue()))
                        .collect(Collectors.toMap(
                                HistoricVariableInstance::getName,
                                HistoricVariableInstance::getValue,
                                (v1, v2) -> v2  // 如果有重复键，保留最后一个值
                        ));
                this.historicVariablesMap.put(processInstanceId, variablesMap);
            });
        }
    }

    /**
     * 获取流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例
     */
    public ProcessInstance getProcessInstance(String processInstanceId) {
        return processInstanceMap.get(processInstanceId);
    }

    /**
     * 获取历史流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 历史流程实例
     */
    public HistoricProcessInstance getHistoricProcessInstance(String processInstanceId) {
        return historicProcessInstanceMap.get(processInstanceId);
    }

    /**
     * 获取流程定义
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程定义
     */
    public ProcessDefinition getProcessDefinition(String processDefinitionId) {
        return processDefinitionMap.get(processDefinitionId);
    }

    /**
     * 获取历史流程变量
     *
     * @param processInstanceId 流程实例ID
     * @return 历史流程变量映射
     */
    public Map<String, Object> getHistoricVariables(String processInstanceId) {
        return historicVariablesMap.getOrDefault(processInstanceId, new HashMap<>());
    }

    /**
     * 获取流程实例或历史流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例或历史流程实例的Optional对象
     */
    public Optional<Object> getProcessInstanceOrHistoric(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstance(processInstanceId);
        if (processInstance != null) {
            return Optional.of(processInstance);
        }

        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(processInstanceId);
        return Optional.ofNullable(historicProcessInstance);
    }

    /**
     * 根据流程实例ID获取对应的流程定义
     *
     * @param processInstanceId 流程实例ID
     * @return 流程定义的Optional对象
     */
    public Optional<ProcessDefinition> getProcessDefinitionByProcessInstanceId(String processInstanceId) {
        ProcessInstance processInstance = getProcessInstance(processInstanceId);
        if (processInstance != null) {
            return Optional.ofNullable(getProcessDefinition(processInstance.getProcessDefinitionId()));
        }

        HistoricProcessInstance historicProcessInstance = getHistoricProcessInstance(processInstanceId);
        if (historicProcessInstance != null) {
            return Optional.ofNullable(getProcessDefinition(historicProcessInstance.getProcessDefinitionId()));
        }

        return Optional.empty();
    }
} 