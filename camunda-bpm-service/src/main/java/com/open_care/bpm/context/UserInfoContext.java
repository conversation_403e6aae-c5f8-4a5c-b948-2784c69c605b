/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.context;

public class UserInfoContext {
    private static ThreadLocal<UserInfo> userInfo = new ThreadLocal();

    public UserInfoContext() {
    }

    public static UserInfo getUser() {
        return (UserInfo)userInfo.get();
    }

    public static void setUser(UserInfo user) {
        userInfo.set(user);
    }
}