package com.open_care.bpm.script.groovy.context;

import java.util.Map;

/**
 * HTTP请求上下文接口
 * 支持链式编程的HTTP请求操作
 * 
 * <AUTHOR>
 * @date 2025/5/26
 */
public interface HttpContext {

    /**
     * 创建GET请求
     * 
     * @param url 请求URL
     * @return HTTP请求构建器
     */
    HttpRequestBuilder get(String url);

    /**
     * 创建POST请求
     * 
     * @param url 请求URL
     * @return HTTP请求构建器
     */
    HttpRequestBuilder post(String url);

    /**
     * 创建PUT请求
     * 
     * @param url 请求URL
     * @return HTTP请求构建器
     */
    HttpRequestBuilder put(String url);

    /**
     * 创建DELETE请求
     * 
     * @param url 请求URL
     * @return HTTP请求构建器
     */
    HttpRequestBuilder delete(String url);

    /**
     * 创建PATCH请求
     * 
     * @param url 请求URL
     * @return HTTP请求构建器
     */
    HttpRequestBuilder patch(String url);

    /**
     * HTTP请求构建器接口
     * 支持链式编程
     */
    interface HttpRequestBuilder {

        /**
         * 设置请求头集合
         * 
         * @param headers 请求头Map
         * @return 当前构建器实例
         */
        HttpRequestBuilder headers(Map<String, String> headers);

        /**
         * 设置单个请求头
         * 
         * @param name 请求头名称
         * @param value 请求头值
         * @return 当前构建器实例
         */
        HttpRequestBuilder header(String name, String value);

        /**
         * 设置Content-Type
         * 
         * @param contentType Content-Type值
         * @return 当前构建器实例
         */
        HttpRequestBuilder contentType(String contentType);

        /**
         * 设置请求体（字符串）
         * 
         * @param body 请求体内容
         * @return 当前构建器实例
         */
        HttpRequestBuilder body(String body);

        /**
         * 设置请求体（字节数组）
         * 
         * @param body 请求体内容
         * @return 当前构建器实例
         */
        HttpRequestBuilder body(byte[] body);

        /**
         * 设置表单参数
         * 
         * @param form 表单参数Map
         * @return 当前构建器实例
         */
        HttpRequestBuilder form(Map<String, Object> form);

        /**
         * 设置单个表单参数
         * 
         * @param name 参数名
         * @param value 参数值
         * @return 当前构建器实例
         */
        HttpRequestBuilder form(String name, Object value);

        /**
         * 设置连接超时时间（毫秒）
         * 
         * @param timeout 超时时间
         * @return 当前构建器实例
         */
        HttpRequestBuilder timeout(int timeout);

        /**
         * 设置读取超时时间（毫秒）
         * 
         * @param timeout 超时时间
         * @return 当前构建器实例
         */
        HttpRequestBuilder readTimeout(int timeout);

        /**
         * 执行请求并返回响应字符串
         * 
         * @return 响应内容
         */
        String execute();

        /**
         * 执行请求并返回响应字节数组
         * 
         * @return 响应字节数组
         */
        byte[] executeBytes();

        /**
         * 执行请求并返回HTTP响应对象
         * 
         * @return HTTP响应对象
         */
        HttpResponse executeResponse();
    }

    /**
     * HTTP响应对象
     */
    interface HttpResponse {
        
        /**
         * 获取响应状态码
         * 
         * @return 状态码
         */
        int getStatus();

        /**
         * 获取响应头
         * 
         * @return 响应头Map
         */
        Map<String, String> getHeaders();

        /**
         * 获取响应体字符串
         * 
         * @return 响应体
         */
        String getBody();

        /**
         * 获取响应体字节数组
         * 
         * @return 响应体字节数组
         */
        byte[] getBodyBytes();

        /**
         * 判断请求是否成功（状态码2xx）
         * 
         * @return 是否成功
         */
        boolean isOk();
    }
}
