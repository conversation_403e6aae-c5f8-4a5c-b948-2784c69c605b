package com.open_care.bpm.script.groovy.context.exception;

/**
 * <AUTHOR>
 * @date :2025/5/26
 */
public class EntityCrudException extends ScriptExecutionException{
    public EntityCrudException(String message) {
        super(message);
    }

    public EntityCrudException(String message, Throwable cause) {
        super(message, cause);
    }

    public EntityCrudException(String errorCode, String message, Object... args) {
        super(errorCode, message, args);
    }
}
