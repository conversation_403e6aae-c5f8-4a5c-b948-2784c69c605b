package com.open_care.bpm.script.groovy.context;

/**
 * 通知上下文接口
 * 提供流程中发送用户通知的功能，通过Gateway服务的NotifyController实现
 * 
 * <AUTHOR>
 * @date :2025/5/26
 */
public interface NotifyContext {
    
    /**
     * 发送简单文本通知给指定用户（根据用户名）
     * 便捷方法，用于发送简单的文本通知，使用默认消息类型和级别
     * 
     * @param username 目标用户名
     * @param title 通知标题
     * @param message 通知内容
     */
    void sendByUsername(String username, String title, String message);
    
    /**
     * 发送文本通知给指定用户（根据用户名）
     * 支持自定义消息类型和级别
     * 
     * @param username 目标用户名
     * @param title 通知标题
     * @param message 通知内容
     * @param msgType 消息类型（如：systemMsg, batchPrint等）
     * @param level 消息级别（如：info, warning, error等）
     */
    void sendByUsername(String username, String title, String message, String msgType, String level);
    
    /**
     * 发送简单文本通知给指定用户（根据用户ID）
     * 便捷方法，用于发送简单的文本通知，使用默认消息类型和级别
     * 
     * @param userId 目标用户ID
     * @param title 通知标题
     * @param message 通知内容
     */
    void sendByUserId(String userId, String title, String message);
    
    /**
     * 发送文本通知给指定用户（根据用户ID）
     * 支持自定义消息类型和级别
     * 
     * @param userId 目标用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param msgType 消息类型（如：systemMsg, batchPrint等）
     * @param level 消息级别（如：info, warning, error等）
     */
    void sendByUserId(String userId, String title, String message, String msgType, String level);
}
