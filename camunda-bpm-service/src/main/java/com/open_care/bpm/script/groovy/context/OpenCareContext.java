/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.script.groovy.context;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Groovy脚本运行上下文主入口
 * 提供统一的OC对象供脚本调用
 *
 * <AUTHOR>
 */
@Component("openCareContext")
public class OpenCareContext {

    /**
     * CRUD操作上下文
     */
    @Autowired
    public CrudContext crud;

    @Autowired
    public HttpContext http;

    @Autowired
    public ProcessContext process;

    @Autowired
    public ReferenceContext reference;

    @Autowired
    public NotifyContext notify;


}