/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.script.groovy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.open_care.bpm.core.ProcessVariableUtil;
import com.open_care.bpm.script.groovy.context.OpenCareContext;

import lombok.extern.log4j.Log4j2;
import org.aspectj.apache.bcel.classfile.Module;
import org.camunda.bpm.engine.delegate.VariableScope;
import org.camunda.bpm.engine.impl.scripting.ExecutableScript;
import org.camunda.bpm.engine.impl.scripting.ScriptFactory;
import org.camunda.bpm.engine.impl.scripting.SourceExecutableScript;
import org.camunda.bpm.engine.impl.scripting.engine.ScriptingEngines;
import org.camunda.bpm.engine.impl.scripting.env.ScriptEnvResolver;
import org.camunda.bpm.engine.impl.scripting.env.ScriptingEnvironment;
import org.camunda.bpm.engine.ProcessEngineException;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.TypedValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 安全的 Groovy 脚本执行环境
 * 限制可用的类和包，只允许预定义的安全类
 */
@Log4j2
public class OcGroovyScriptEnvironment extends ScriptingEnvironment {

    private OpenCareContext openCareContext;


    public OcGroovyScriptEnvironment(ScriptFactory scriptFactory,
                                     List<ScriptEnvResolver> scriptEnvResolvers,
                                     ScriptingEngines scriptingEngines,
                                     OpenCareContext openCareContext
    ) {
        super(scriptFactory, scriptEnvResolvers, scriptingEngines);
        openCareContext = openCareContext;
    }

    @Override
    public Object execute(ExecutableScript script, VariableScope scope) {
        preExecute(script);

        return super.execute(script, scope);
    }

    @Override
    public Object execute(ExecutableScript script, VariableScope scope, Bindings bindings, ScriptEngine scriptEngine) {

        preExecute(script, scope, bindings, scriptEngine);

        try {
            return super.execute(script, scope, bindings, scriptEngine);
        } catch (ProcessEngineException e) {
            String scriptSource = null;
            if (script instanceof SourceExecutableScript) {
                scriptSource = ((SourceExecutableScript) script).getScriptSource();
            }
            log.error("脚本执行异常， 脚本内容 = {}", scriptSource, e);
        }

        return null;
    }

    /**
     * 在构造好  Bindings ScriptEngine 之前进行预处理
     *
     * @param script
     */
    private void preExecute(ExecutableScript script) {
        // 保存当前的 VariableScope 用于获取流程信息
        if (script.getLanguage().equals("groovy")) {
            // 检查脚本类型，避免类型转换异常
            if (script instanceof SourceExecutableScript) {
                SourceExecutableScript sourceExecutableScript = (SourceExecutableScript) script;
                String scriptSource = sourceExecutableScript.getScriptSource();

                // 添加安全的导入和 OC 上下文
                scriptSource = addSecureImports() + scriptSource;
                sourceExecutableScript.setScriptSource(scriptSource);
            } else {
                log.warn("脚本类型不是SourceExecutableScript，无法添加导入语句: {}", script.getClass().getName());
            }
        }
    }


    /**
     * 在构造好  Bindings ScriptEngine 之后，实际执行之前进行预处理
     *
     * @param script       执行脚本
     * @param scope        变量作用域
     * @param bindings     脚本绑定对象
     * @param scriptEngine 脚本引擎
     */
    private void preExecute(ExecutableScript script, VariableScope scope, Bindings bindings, ScriptEngine scriptEngine) {
        Map<String, Object> newBindings = new HashMap<>();

        // 保存安全的对象，移除 Spring Bean
        if (bindings.containsKey("execution")) {
            newBindings.put("execution", bindings.get("execution"));
        }

        // 注册 OC 上下文 - 从Spring容器中获取OpenCareContext实例
        newBindings.put("OC", openCareContext);
        newBindings.put("log", log);


        newBindings.put("variableScope", scope);

        bindings.clear();
        bindings.putAll(newBindings);
    }


    /**
     * 添加安全的导入语句和 OC 上下文
     */
    private String addSecureImports() {
        StringBuilder stringBuilder = new StringBuilder();

        // 流程相关类
        stringBuilder.append(StrUtil.format("import static {}.*\n", Variables.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", Variables.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", VariableMap.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", TypedValue.class.getName()));
        stringBuilder.append(StrUtil.format("import static {}.*\n", ProcessVariableUtil.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", ProcessVariableUtil.class.getName()));


        // 添加 com.open_care.bpm.script.groovy 包下的核心类导入
        stringBuilder.append(StrUtil.format("import {}\n", OpenCareContext.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.CrudContext.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.HttpContext.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.ProcessContext.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.ReferenceContext.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.NotifyContext.class.getName()));

        // 添加 service 包下的实现类导入
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.service.CrudService.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.service.HttpService.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.service.ProcessContextService.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.service.ReferenceContextService.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.service.NotifyService.class.getName()));

        // 添加引用相关的DTO类导入
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.api.common.dto.RefListDTO.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.api.MUI.OcValueOptions.class.getName()));

        // 添加 exception 包下的异常类导入
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.exception.ScriptExecutionException.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.exception.HttpRequestException.class.getName()));
        stringBuilder.append(StrUtil.format("import {}\n", com.open_care.bpm.script.groovy.context.exception.EntityCrudException.class.getName()));

        // 添加常用工具类包导入 - 使用具体类来获取包名
        stringBuilder.append(StrUtil.format("import {}.*\n", StrUtil.class.getPackage().getName()));
        stringBuilder.append(StrUtil.format("import {}.*\n", cn.hutool.core.date.DateUtil.class.getPackage().getName()));
        stringBuilder.append(StrUtil.format("import {}.*\n", cn.hutool.json.JSONUtil.class.getPackage().getName()));
        stringBuilder.append(StrUtil.format("import {}.*\n", HttpUtil.class.getPackage().getName()));

        // 添加 Java 标准库包导入 - 使用具体类来获取包名
        stringBuilder.append(StrUtil.format("import {}.*\n", java.util.List.class.getPackage().getName()));
        stringBuilder.append(StrUtil.format("import {}.*\n", java.time.LocalDateTime.class.getPackage().getName()));
        stringBuilder.append(StrUtil.format("import {}.*\n", java.math.BigDecimal.class.getPackage().getName()));


        // 添加注释说明
        stringBuilder.append(StrUtil.format("{}\n", "\n// === 可用的工具类和上下文 ==="));
        stringBuilder.append(StrUtil.format("{}\n", "// OC: Open Care 上下文 - 提供 crud, http, process, reference 等功能"));
        stringBuilder.append(StrUtil.format("{}\n", "//   - OC.crud: CRUD操作上下文，支持实体的增删改查"));
        stringBuilder.append(StrUtil.format("{}\n", "//   - OC.http: HTTP请求上下文，支持链式HTTP调用"));
        stringBuilder.append(StrUtil.format("{}\n", "//   - OC.process: 流程操作上下文，支持流程启动等操作"));
        stringBuilder.append(StrUtil.format("{}\n", "//   - OC.reference: 引用上下文，支持字典数据的获取和操作"));
        stringBuilder.append(StrUtil.format("{}\n", "//   - OC.notify: 通知上下文"));
        stringBuilder.append(StrUtil.format("{}\n", "// StrUtil: 字符串工具 - isBlank, isNotBlank, format 等"));
        stringBuilder.append(StrUtil.format("{}\n", "// CollUtil: 集合工具 - isEmpty, isNotEmpty, newArrayList 等"));
        stringBuilder.append(StrUtil.format("{}\n", "// DateUtil: 日期工具 - format, parse, now 等"));
        stringBuilder.append(StrUtil.format("{}\n", "// JSONUtil: JSON工具 - toJsonStr, parseObj 等"));
        stringBuilder.append(StrUtil.format("{}\n", "// HttpUtil: HTTP工具 - get, post 等"));
        stringBuilder.append(StrUtil.format("{}\n", "// RefListDTO: 字典引用项DTO"));
        stringBuilder.append(StrUtil.format("{}\n", "// OcValueOptions: 值选项对象"));
        stringBuilder.append(StrUtil.format("{}\n", "// === 用户脚本开始 ===\n"));

        return stringBuilder.toString();
    }
}
