package com.open_care.bpm.script.groovy.context;

import com.open_care.api.common.dto.BatchSaveDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.dto.crud.SaveRequestDTO;
import com.open_care.api.common.edit.EditDataResponse;
import com.open_care.dto.QueryResponseDTO;
import org.bouncycastle.LICENSE;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/5/26
 */
public interface CrudContext {


    /**
     * 保存实体
     *
     * @param requestDTO
     * @return
     */
    EditDataResponse save(SaveRequestDTO requestDTO);

    EditDataResponse get(String id, String entityName);

    /**
     * get 实体
     *
     * @param id
     * @param eagerProperties
     * @return
     */
    EditDataResponse get(String id, String entityName, String eagerProperties);


    /**
     * 删除实体，默认为逻辑删除
     *
     * @param id
     * @return
     */
    EditDataResponse delete(String id);


    /**
     * 查询实体
     *
     * @param requestDTO
     * @return
     */
    QueryResponseDTO<?> query(QueryRequestDTO requestDTO);


    /**
     * 批量保存
     *
     * @param batchSaveDTO
     * @return
     */
    List<EditDataResponse> batchSave(BatchSaveDTO batchSaveDTO);
}
