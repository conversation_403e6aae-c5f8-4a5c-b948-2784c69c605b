/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.script.groovy.context.exception;

/**
 * 脚本执行异常
 * 
 * <AUTHOR>
 */
public class ScriptExecutionException extends RuntimeException {
    
    private String errorCode;
    private Object[] args;
    
    public ScriptExecutionException(String message) {
        super(message);
    }
    
    public ScriptExecutionException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public ScriptExecutionException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public Object[] getArgs() {
        return args;
    }
} 