package com.open_care.bpm.script.groovy.context.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.common.dto.RefListDTO;
import com.open_care.api.MUI.OcValueOptions;
import com.open_care.bpm.script.groovy.context.ReferenceContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典参考数据上下文实现类
 * 
 * <AUTHOR>
 * @date :2025/5/27
 */
@Log4j2
@Service
public class ReferenceContextService implements ReferenceContext {
    
    @Autowired
    private ConfigurationRemoteService configurationRemoteService;
    
    // 常用字典编码常量
    private static final String SEX_CODE = "sex";
    private static final String DOCTOR_RANK_CODE = "doctorRank";
    private static final String DOCTOR_JOB_CODE = "doctorJob";
    private static final String BRANCH_HOSPITAL_LEVEL_CODE = "branchHospitalLevel";
    private static final String BRANCH_STATUS_CODE = "branchStatus";

    @Override
    public List<RefListDTO> getReference(String code) {
        try {
            if (StrUtil.isBlank(code)) {
                log.warn("字典编码为空");
                return Collections.emptyList();
            }
            return configurationRemoteService.getReferenceInfo(code);
        } catch (Exception e) {
            log.error("获取字典数据失败，code: {}", code, e);
            return Collections.emptyList();
        }
    }

    @Override
    public String getReferenceValue(String code, String key) {
        return getReferenceValue(code, key, null);
    }

    @Override
    public String getReferenceValue(String code, String key, String defaultValue) {
        try {
            if (StrUtil.isBlank(code) || StrUtil.isBlank(key)) {
                log.warn("字典编码或key为空，code: {}, key: {}", code, key);
                return defaultValue;
            }
            
            List<RefListDTO> refList = getReference(code);
            return refList.stream()
                    .filter(ref -> Objects.equals(ref.getKey(), key))
                    .map(RefListDTO::getValue)
                    .findFirst()
                    .orElse(defaultValue);
        } catch (Exception e) {
            log.error("获取字典项值失败，code: {}, key: {}", code, key, e);
            return defaultValue;
        }
    }

    @Override
    public Map<String, List<OcValueOptions>> getReferences(List<String> codes) {
        try {
            if (CollUtil.isEmpty(codes)) {
                log.warn("字典编码列表为空");
                return Collections.emptyMap();
            }
            return configurationRemoteService.getReferenceInfoByCodes(codes);
        } catch (Exception e) {
            log.error("批量获取字典数据失败，codes: {}", codes, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Map<String, String>> getAllReferences() {
        try {
            // 获取所有常用字典编码
            List<String> allCodes = Arrays.asList(
                SEX_CODE, DOCTOR_RANK_CODE, DOCTOR_JOB_CODE, 
                BRANCH_HOSPITAL_LEVEL_CODE, BRANCH_STATUS_CODE
            );
            
            Map<String, Map<String, String>> result = new HashMap<>();
            for (String code : allCodes) {
                List<RefListDTO> refList = getReference(code);
                if (CollUtil.isNotEmpty(refList)) {
                    result.put(code, toMap(refList));
                }
            }
            return result;
        } catch (Exception e) {
            log.error("获取所有字典数据失败", e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Set<String> getReferenceKeys(String code) {
        try {
            List<RefListDTO> refList = getReference(code);
            return refList.stream()
                    .map(RefListDTO::getKey)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("获取字典keys失败，code: {}", code, e);
            return Collections.emptySet();
        }
    }

    @Override
    public List<String> getReferenceValues(String code) {
        try {
            List<RefListDTO> refList = getReference(code);
            return refList.stream()
                    .map(RefListDTO::getValue)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取字典values失败，code: {}", code, e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean hasReference(String code, String key) {
        try {
            if (StrUtil.isBlank(code) || StrUtil.isBlank(key)) {
                return false;
            }
            
            List<RefListDTO> refList = getReference(code);
            return refList.stream()
                    .anyMatch(ref -> Objects.equals(ref.getKey(), key));
        } catch (Exception e) {
            log.error("检查字典项是否存在失败，code: {}, key: {}", code, key, e);
            return false;
        }
    }

    @Override
    public Map<String, String> toMap(List<RefListDTO> refListDTOs) {
        if (CollUtil.isEmpty(refListDTOs)) {
            return Collections.emptyMap();
        }
        
        return refListDTOs.stream()
                .filter(ref -> ObjectUtil.isNotNull(ref.getKey()) && ObjectUtil.isNotNull(ref.getValue()))
                .collect(Collectors.toMap(
                    RefListDTO::getKey,
                    RefListDTO::getValue,
                    (existing, replacement) -> existing // 遇到重复key时保留第一个
                ));
    }

    @Override
    public List<OcValueOptions> toValueOptions(List<RefListDTO> refListDTOs) {
        if (CollUtil.isEmpty(refListDTOs)) {
            return Collections.emptyList();
        }
        
        return refListDTOs.stream()
                .filter(ref -> ObjectUtil.isNotNull(ref.getKey()) && ObjectUtil.isNotNull(ref.getValue()))
                .map(ref -> {
                    OcValueOptions option = new OcValueOptions();
                    option.setValue(ref.getKey());
                    option.setLabel(ref.getValue());
                    return option;
                })
                .collect(Collectors.toList());
    }
}
