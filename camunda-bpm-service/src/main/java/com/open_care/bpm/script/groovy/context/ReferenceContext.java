package com.open_care.bpm.script.groovy.context;

import com.open_care.api.common.dto.RefListDTO;
import com.open_care.api.MUI.OcValueOptions;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 字典参考数据上下文接口
 * 提供在Camunda流程脚本中操作字典数据的能力
 * 
 * <AUTHOR>
 * @date :2025/5/26
 */
public interface ReferenceContext {

    /**
     * 根据字典编码获取字典项列表
     * @param code 字典编码，如："branchHospitalLevel", "doctorRank"
     * @return 字典项列表，包含key-value对
     */
    List<RefListDTO> getReference(String code);

    /**
     * 根据字典编码和key获取字典项的值
     * @param code 字典编码
     * @param key 字典项的key
     * @return 字典项的值，如果未找到返回null
     */
    String getReferenceValue(String code, String key);

    /**
     * 根据字典编码和key获取字典项的值，支持默认值
     * @param code 字典编码
     * @param key 字典项的key
     * @param defaultValue 默认值
     * @return 字典项的值，如果未找到返回默认值
     */
    String getReferenceValue(String code, String key, String defaultValue);

    /**
     * 批量获取多个字典的数据
     * @param codes 字典编码列表
     * @return Map结构，key为字典编码，value为该字典的所有选项
     */
    Map<String, List<OcValueOptions>> getReferences(List<String> codes);

    /**
     * 获取所有字典数据的简化映射
     * @return Map结构，key为字典编码，value为key-value映射
     */
    Map<String, Map<String, String>> getAllReferences();

    /**
     * 根据字典编码获取所有key的集合
     * @param code 字典编码
     * @return key集合
     */
    Set<String> getReferenceKeys(String code);

    /**
     * 根据字典编码获取所有value的集合
     * @param code 字典编码
     * @return value集合
     */
    List<String> getReferenceValues(String code);

    /**
     * 检查字典项是否存在
     * @param code 字典编码
     * @param key 字典项的key
     * @return 是否存在
     */
    boolean hasReference(String code, String key);


    /**
     * 将字典项列表转换为Map
     * @param refListDTOs 字典项列表
     * @return key-value映射
     */
    Map<String, String> toMap(List<RefListDTO> refListDTOs);

    /**
     * 将字典项列表转换为选项列表
     * @param refListDTOs 字典项列表
     * @return 选项列表
     */
    List<OcValueOptions> toValueOptions(List<RefListDTO> refListDTOs);
}
