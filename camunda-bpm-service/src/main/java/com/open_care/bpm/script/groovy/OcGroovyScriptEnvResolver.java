/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.script.groovy;

import org.camunda.bpm.engine.impl.scripting.env.ScriptEnvResolver;
import com.open_care.bpm.script.groovy.context.OpenCareContext;
import com.open_care.util.SpringContextUtil;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Groovy 脚本环境解析器
 * 提供 OC 上下文中的方法给脚本使用
 */
public class OcGroovyScriptEnvResolver implements ScriptEnvResolver {
    
    @Override
    public String[] resolve(String language) {
        if ("groovy".equals(language)) {
            List<String> result = new ArrayList<>();
            
            // 注册 OpenCareContext Bean 到脚本环境
            try {
                // 从Spring容器中获取OpenCareContext实例
                OpenCareContext openCareContext = SpringContextUtil.getApplicationContext().getBean(OpenCareContext.class);
                if (openCareContext != null) {
                    result.add("openCareContext");
                }
            } catch (Exception e) {
                // 如果获取失败，记录日志但不抛出异常，避免影响脚本执行
                System.err.println("Failed to resolve OpenCareContext: " + e.getMessage());
            }
            
            return result.toArray(new String[0]);
        }
        return null;
    }
}
