package com.open_care.bpm.script.groovy.context.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.open_care.bpm.script.groovy.context.HttpContext;
import com.open_care.bpm.script.groovy.context.exception.HttpRequestException;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * HTTP请求服务实现类
 * 基于hutool的HttpUtil实现链式编程的HTTP请求
 * 
 * <AUTHOR>
 * @date 2025/5/26
 */
@Log4j2
@Service
public class HttpService implements HttpContext {

    /**
     * 默认连接超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 30000;

    /**
     * 默认读取超时时间（毫秒）
     */
    private static final int DEFAULT_READ_TIMEOUT = 60000;

    @Override
    public HttpRequestBuilder get(String url) {
        return new HttpRequestBuilderImpl(Method.GET, url);
    }

    @Override
    public HttpRequestBuilder post(String url) {
        return new HttpRequestBuilderImpl(Method.POST, url);
    }

    @Override
    public HttpRequestBuilder put(String url) {
        return new HttpRequestBuilderImpl(Method.PUT, url);
    }

    @Override
    public HttpRequestBuilder delete(String url) {
        return new HttpRequestBuilderImpl(Method.DELETE, url);
    }

    @Override
    public HttpRequestBuilder patch(String url) {
        return new HttpRequestBuilderImpl(Method.PATCH, url);
    }

    /**
     * HTTP请求构建器实现类
     */
    public static class HttpRequestBuilderImpl implements HttpRequestBuilder {

        private final Method method;
        private final String url;
        private final Map<String, String> headers = new HashMap<>();
        private final Map<String, Object> formParams = new HashMap<>();
        private String bodyContent;
        private byte[] bodyBytes;
        private int timeout = DEFAULT_TIMEOUT;
        private int readTimeout = DEFAULT_READ_TIMEOUT;

        public HttpRequestBuilderImpl(Method method, String url) {
            this.method = method;
            this.url = url;
        }

        @Override
        public HttpRequestBuilder headers(Map<String, String> headers) {
            if (MapUtil.isNotEmpty(headers)) {
                this.headers.putAll(headers);
            }
            return this;
        }

        @Override
        public HttpRequestBuilder header(String name, String value) {
            if (StrUtil.isNotBlank(name) && StrUtil.isNotBlank(value)) {
                this.headers.put(name, value);
            }
            return this;
        }

        @Override
        public HttpRequestBuilder contentType(String contentType) {
            if (StrUtil.isNotBlank(contentType)) {
                this.headers.put("Content-Type", contentType);
            }
            return this;
        }

        @Override
        public HttpRequestBuilder body(String body) {
            this.bodyContent = body;
            this.bodyBytes = null; // 清除字节数组
            return this;
        }

        @Override
        public HttpRequestBuilder body(byte[] body) {
            this.bodyBytes = body;
            this.bodyContent = null; // 清除字符串内容
            return this;
        }

        @Override
        public HttpRequestBuilder form(Map<String, Object> form) {
            if (MapUtil.isNotEmpty(form)) {
                this.formParams.putAll(form);
            }
            return this;
        }

        @Override
        public HttpRequestBuilder form(String name, Object value) {
            if (StrUtil.isNotBlank(name) && ObjectUtil.isNotNull(value)) {
                this.formParams.put(name, value);
            }
            return this;
        }

        @Override
        public HttpRequestBuilder timeout(int timeout) {
            this.timeout = timeout;
            return this;
        }

        @Override
        public HttpRequestBuilder readTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        @Override
        public String execute() {
            try {
                HttpRequest request = buildRequest();
                cn.hutool.http.HttpResponse response = request.execute();
                return response.body();
            } catch (Exception e) {
                String errorMsg = StrUtil.format("HTTP {} 请求失败: {}, URL: {}", method.name(), e.getMessage(), url);
                log.error(errorMsg, e);
                throw new HttpRequestException(errorMsg, e);
            }
        }

        @Override
        public byte[] executeBytes() {
            try {
                HttpRequest request = buildRequest();
                cn.hutool.http.HttpResponse response = request.execute();
                return response.bodyBytes();
            } catch (Exception e) {
                String errorMsg = StrUtil.format("HTTP {} 请求失败: {}, URL: {}", method.name(), e.getMessage(), url);
                log.error(errorMsg, e);
                throw new HttpRequestException(errorMsg, e);
            }
        }

        @Override
        public HttpContext.HttpResponse executeResponse() {
            try {
                HttpRequest request = buildRequest();
                cn.hutool.http.HttpResponse response = request.execute();
                return new HttpResponseImpl(response);
            } catch (Exception e) {
                String errorMsg = StrUtil.format("HTTP {} 请求失败: {}, URL: {}", method.name(), e.getMessage(), url);
                log.error(errorMsg, e);
                throw new HttpRequestException(errorMsg, e);
            }
        }

        /**
         * 构建HttpRequest对象
         */
        private HttpRequest buildRequest() {
            HttpRequest request = HttpUtil.createRequest(method, url);

            // 设置超时时间
            request.timeout(timeout);
            request.setReadTimeout(readTimeout);

            // 设置请求头
            if (MapUtil.isNotEmpty(headers)) {
                request.headerMap(headers, true);
            }

            // 设置请求体
            if (StrUtil.isNotBlank(bodyContent)) {
                request.body(bodyContent);
            } else if (ObjectUtil.isNotNull(bodyBytes)) {
                request.body(bodyBytes);
            }

            // 设置表单参数
            if (MapUtil.isNotEmpty(formParams)) {
                request.form(formParams);
            }

            return request;
        }
    }

    /**
     * HTTP响应实现类
     */
    public static class HttpResponseImpl implements HttpContext.HttpResponse {

        private final cn.hutool.http.HttpResponse response;

        public HttpResponseImpl(cn.hutool.http.HttpResponse response) {
            this.response = response;
        }

        @Override
        public int getStatus() {
            return response.getStatus();
        }

        @Override
        public Map<String, String> getHeaders() {
            Map<String, String> headerMap = new HashMap<>();
            response.headers().forEach((name, values) -> {
                if (values != null && !values.isEmpty()) {
                    headerMap.put(name, values.get(0));
                }
            });
            return headerMap;
        }

        @Override
        public String getBody() {
            return response.body();
        }

        @Override
        public byte[] getBodyBytes() {
            return response.bodyBytes();
        }

        @Override
        public boolean isOk() {
            return response.isOk();
        }
    }
} 