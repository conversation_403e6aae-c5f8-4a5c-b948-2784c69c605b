package com.open_care.bpm.script.groovy.context.exception;

/**
 * HTTP请求异常
 * 
 * <AUTHOR>
 * @date 2025/5/26
 */
public class HttpRequestException extends ScriptExecutionException {
    
    public HttpRequestException(String message) {
        super(message);
    }

    public HttpRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public HttpRequestException(String errorCode, String message, Object... args) {
        super(errorCode, message, args);
    }
} 