/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.script.groovy;

import com.open_care.bpm.script.groovy.context.OpenCareContext;
import lombok.AllArgsConstructor;
import org.camunda.bpm.engine.impl.cfg.AbstractProcessEnginePlugin;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


public class OcGroovyScriptExtendPlugin extends AbstractProcessEnginePlugin {

    private final OpenCareContext openCareContext;

    public OcGroovyScriptExtendPlugin(OpenCareContext openCareContext) {
        this.openCareContext = openCareContext;
    }

    @Override

    public void postInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
        processEngineConfiguration.getEnvScriptResolvers().add(new OcGroovyScriptEnvResolver());

        processEngineConfiguration.setScriptingEnvironment(
                new OcGroovyScriptEnvironment(processEngineConfiguration.getScriptFactory(),
                        processEngineConfiguration.getEnvScriptResolvers(),
                        processEngineConfiguration.getScriptingEngines(),
                        openCareContext
                )

        );
    }
}
