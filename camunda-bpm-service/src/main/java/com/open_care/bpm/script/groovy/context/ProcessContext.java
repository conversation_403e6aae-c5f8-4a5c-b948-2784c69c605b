/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.script.groovy.context;

import com.open_care.bpm.dto.ProcessInstDTO;

import java.util.Map;

/**
 * 流程操作上下文接口
 * 提供流程启动、终止和信息获取功能
 *
 * <AUTHOR>
 * @date 2025/5/26
 */
public interface ProcessContext {

    /**
     * 启动流程实例
     *
     * @param processDefinitionKey 流程定义Key
     * @return 流程实例信息
     */
    ProcessInstDTO startProcess(String processDefinitionKey);

    /**
     * 启动流程实例（带变量）
     *
     * @param processDefinitionKey 流程定义Key
     * @param variables            流程变量
     * @return 流程实例信息
     */
    ProcessInstDTO startProcess(String processDefinitionKey, Map<String, Object> variables);

    /**
     * 启动流程实例（带业务Key和变量）
     *
     * @param processDefinitionKey 流程定义Key
     * @param businessKey          业务Key
     * @param variables            流程变量
     * @return 流程实例信息
     */
    ProcessInstDTO startProcess(String processDefinitionKey, String businessKey, Map<String, Object> variables);

    /**
     * 根据流程实例ID结束流程
     *
     * @param processInstanceId 流程实例ID
     * @param reason           结束原因
     */
    void terminateProcess(String processInstanceId, String reason);

    /**
     * 根据流程实例ID结束流程（无原因）
     *
     * @param processInstanceId 流程实例ID
     */
    void terminateProcess(String processInstanceId);

    /**
     * 根据流程实例ID停止流程（挂起）
     *
     * @param processInstanceId 流程实例ID
     */
    void suspendProcess(String processInstanceId);

    /**
     * 根据流程实例ID恢复流程（取消挂起）
     *
     * @param processInstanceId 流程实例ID
     */
    void activateProcess(String processInstanceId);

    /**
     * 根据流程实例ID设置流程变量
     *
     * @param processInstanceId 流程实例ID
     * @param variables        要设置的变量
     */
    void setProcessVariables(String processInstanceId, Map<String, Object> variables);

    /**
     * 根据流程实例ID设置单个流程变量
     *
     * @param processInstanceId 流程实例ID
     * @param variableName     变量名
     * @param value            变量值
     */
    void setProcessVariable(String processInstanceId, String variableName, Object value);

    /**
     * 根据任务ID设置任务本地变量
     *
     * @param taskId    任务ID
     * @param variables 要设置的变量
     */
    void setTaskLocalVariables(String taskId, Map<String, Object> variables);

    /**
     * 根据任务ID设置单个任务本地变量
     *
     * @param taskId       任务ID
     * @param variableName 变量名
     * @param value        变量值
     */
    void setTaskLocalVariable(String taskId, String variableName, Object value);

    /**
     * 根据任务ID设置流程实例变量
     *
     * @param taskId    任务ID
     * @param variables 要设置的变量
     */
    void setProcessVariablesByTaskId(String taskId, Map<String, Object> variables);

    /**
     * 根据任务ID设置单个流程实例变量
     *
     * @param taskId       任务ID
     * @param variableName 变量名
     * @param value        变量值
     */
    void setProcessVariableByTaskId(String taskId, String variableName, Object value);

    /**
     * 根据执行ID设置执行本地变量
     *
     * @param executionId 执行ID
     * @param variables   要设置的变量
     */
    void setExecutionLocalVariables(String executionId, Map<String, Object> variables);

    /**
     * 根据执行ID设置单个执行本地变量
     *
     * @param executionId  执行ID
     * @param variableName 变量名
     * @param value        变量值
     */
    void setExecutionLocalVariable(String executionId, String variableName, Object value);

    /**
     * 根据执行ID设置流程实例变量
     *
     * @param executionId 执行ID
     * @param variables   要设置的变量
     */
    void setProcessVariablesByExecutionId(String executionId, Map<String, Object> variables);

    /**
     * 根据执行ID设置单个流程实例变量
     *
     * @param executionId  执行ID
     * @param variableName 变量名
     * @param value        变量值
     */
    void setProcessVariableByExecutionId(String executionId, String variableName, Object value);

}
