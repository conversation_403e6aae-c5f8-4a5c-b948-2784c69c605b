package com.open_care.bpm.script.groovy.context.service;

import cn.hutool.core.util.StrUtil;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.mapper.ProcessInstMapper;
import com.open_care.bpm.script.groovy.context.ProcessContext;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.delegate.VariableScope;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/5/26
 */
@Service
@Log4j2
public class ProcessContextService implements ProcessContext {

    @Autowired
    @Lazy
    private RuntimeService runtimeService;

    @Autowired
    @Lazy
    private RepositoryService repositoryService;

    @Autowired
    @Lazy
    private TaskService taskService;

    /**
     * 启动流程实例
     *
     * @param processDefinitionKey 流程定义Key
     * @return 流程实例信息
     */
    @Override
    @Transactional
    public ProcessInstDTO startProcess(String processDefinitionKey) {
        log.info("启动流程实例，流程定义Key: {}", processDefinitionKey);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey);
        return toDto(processInstance);
    }

    private ProcessInstDTO toDto(ProcessInstance processInstance) {
        String processDefinitionId = processInstance.getProcessDefinitionId();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefinitionId).singleResult();
        return ProcessInstMapper.INSTANCE.toDto(processInstance, processDefinition);
    }

    /**
     * 启动流程实例（带变量）
     *
     * @param processDefinitionKey 流程定义Key
     * @param variables            流程变量
     * @return 流程实例信息
     */
    @Override
    @Transactional
    public ProcessInstDTO startProcess(String processDefinitionKey, Map<String, Object> variables) {
        log.info("启动流程实例，流程定义Key: {}，变量数量: {}", processDefinitionKey, 
                variables != null ? variables.size() : 0);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, variables);
        return toDto(processInstance);
    }

    /**
     * 启动流程实例（带业务Key和变量）
     *
     * @param processDefinitionKey 流程定义Key
     * @param businessKey          业务Key
     * @param variables            流程变量
     * @return 流程实例信息
     */
    @Override
    @Transactional
    public ProcessInstDTO startProcess(String processDefinitionKey, String businessKey, 
                                       Map<String, Object> variables) {
        log.info("启动流程实例，流程定义Key: {}，业务Key: {}，变量数量: {}", 
                processDefinitionKey, businessKey, variables != null ? variables.size() : 0);
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                processDefinitionKey, businessKey, variables);
        return toDto(processInstance);
    }

    /**
     * 根据流程实例ID结束流程
     *
     * @param processInstanceId 流程实例ID
     * @param reason           结束原因
     */
    @Override
    @Transactional
    public void terminateProcess(String processInstanceId, String reason) {
        log.info("结束流程实例，流程实例ID: {}，原因: {}", processInstanceId, reason);
        if (StrUtil.isNotBlank(reason)) {
            runtimeService.deleteProcessInstance(processInstanceId, reason);
        } else {
            runtimeService.deleteProcessInstance(processInstanceId, "手动终止");
        }
    }

    /**
     * 根据流程实例ID结束流程（无原因）
     *
     * @param processInstanceId 流程实例ID
     */
    @Override
    @Transactional
    public void terminateProcess(String processInstanceId) {
        terminateProcess(processInstanceId, "手动终止");
    }

    /**
     * 根据流程实例ID停止流程（挂起）
     *
     * @param processInstanceId 流程实例ID
     */
    @Override
    @Transactional
    public void suspendProcess(String processInstanceId) {
        log.info("挂起流程实例，流程实例ID: {}", processInstanceId);
        runtimeService.suspendProcessInstanceById(processInstanceId);
    }

    /**
     * 根据流程实例ID恢复流程（取消挂起）
     *
     * @param processInstanceId 流程实例ID
     */
    @Override
    @Transactional
    public void activateProcess(String processInstanceId) {
        log.info("激活流程实例，流程实例ID: {}", processInstanceId);
        runtimeService.activateProcessInstanceById(processInstanceId);
    }

    /**
     * 根据流程实例ID设置流程变量
     *
     * @param processInstanceId 流程实例ID
     * @param variables        要设置的变量
     */
    @Override
    @Transactional
    public void setProcessVariables(String processInstanceId, Map<String, Object> variables) {
        log.info("设置流程变量，流程实例ID: {}，变量数量: {}", processInstanceId, 
                variables != null ? variables.size() : 0);
        if (variables != null && !variables.isEmpty()) {
            runtimeService.setVariables(processInstanceId, variables);
        }
    }

    /**
     * 根据流程实例ID设置单个流程变量
     *
     * @param processInstanceId 流程实例ID
     * @param variableName     变量名
     * @param value            变量值
     */
    @Override
    @Transactional
    public void setProcessVariable(String processInstanceId, String variableName, Object value) {
        log.info("设置流程变量，流程实例ID: {}，变量名: {}，变量值: {}", 
                processInstanceId, variableName, value);
        runtimeService.setVariable(processInstanceId, variableName, value);
    }

    /**
     * 根据任务ID设置任务本地变量
     *
     * @param taskId    任务ID
     * @param variables 要设置的变量
     */
    @Override
    @Transactional
    public void setTaskLocalVariables(String taskId, Map<String, Object> variables) {
        log.info("设置任务本地变量，任务ID: {}，变量数量: {}", taskId, 
                variables != null ? variables.size() : 0);
        if (variables != null && !variables.isEmpty()) {
            taskService.setVariablesLocal(taskId, variables);
        }
    }

    /**
     * 根据任务ID设置单个任务本地变量
     *
     * @param taskId       任务ID
     * @param variableName 变量名
     * @param value        变量值
     */
    @Override
    @Transactional
    public void setTaskLocalVariable(String taskId, String variableName, Object value) {
        log.info("设置任务本地变量，任务ID: {}，变量名: {}，变量值: {}", taskId, variableName, value);
        taskService.setVariableLocal(taskId, variableName, value);
    }

    /**
     * 根据任务ID设置流程实例变量
     *
     * @param taskId    任务ID
     * @param variables 要设置的变量
     */
    @Override
    @Transactional
    public void setProcessVariablesByTaskId(String taskId, Map<String, Object> variables) {
        log.info("通过任务ID设置流程变量，任务ID: {}，变量数量: {}", taskId, 
                variables != null ? variables.size() : 0);
        if (variables != null && !variables.isEmpty()) {
            taskService.setVariables(taskId, variables);
        }
    }

    /**
     * 根据任务ID设置单个流程实例变量
     *
     * @param taskId       任务ID
     * @param variableName 变量名
     * @param value        变量值
     */
    @Override
    @Transactional
    public void setProcessVariableByTaskId(String taskId, String variableName, Object value) {
        log.info("通过任务ID设置流程变量，任务ID: {}，变量名: {}，变量值: {}", taskId, variableName, value);
        taskService.setVariable(taskId, variableName, value);
    }

    /**
     * 根据执行ID设置执行本地变量
     *
     * @param executionId 执行ID
     * @param variables   要设置的变量
     */
    @Override
    @Transactional
    public void setExecutionLocalVariables(String executionId, Map<String, Object> variables) {
        log.info("设置执行本地变量，执行ID: {}，变量数量: {}", executionId, 
                variables != null ? variables.size() : 0);
        if (variables != null && !variables.isEmpty()) {
            runtimeService.setVariablesLocal(executionId, variables);
        }
    }

    /**
     * 根据执行ID设置单个执行本地变量
     *
     * @param executionId  执行ID
     * @param variableName 变量名
     * @param value        变量值
     */
    @Override
    @Transactional
    public void setExecutionLocalVariable(String executionId, String variableName, Object value) {
        log.info("设置执行本地变量，执行ID: {}，变量名: {}，变量值: {}", executionId, variableName, value);
        runtimeService.setVariableLocal(executionId, variableName, value);
    }

    /**
     * 根据执行ID设置流程实例变量
     *
     * @param executionId 执行ID
     * @param variables   要设置的变量
     */
    @Override
    @Transactional
    public void setProcessVariablesByExecutionId(String executionId, Map<String, Object> variables) {
        log.info("通过执行ID设置流程变量，执行ID: {}，变量数量: {}", executionId, 
                variables != null ? variables.size() : 0);
        if (variables != null && !variables.isEmpty()) {
            runtimeService.setVariables(executionId, variables);
        }
    }

    /**
     * 根据执行ID设置单个流程实例变量
     *
     * @param executionId  执行ID
     * @param variableName 变量名
     * @param value        变量值
     */
    @Override
    @Transactional
    public void setProcessVariableByExecutionId(String executionId, String variableName, Object value) {
        log.info("通过执行ID设置流程变量，执行ID: {}，变量名: {}，变量值: {}", executionId, variableName, value);
        runtimeService.setVariable(executionId, variableName, value);
    }

}
