package com.open_care.bpm.script.groovy.context.service;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.api.client.GatewayRemoteService;
import com.open_care.api.dto.NotifyDTO;
import com.open_care.bpm.script.groovy.context.NotifyContext;
import com.open_care.util.json.JsonConverter;
import lombok.extern.log4j.Log4j2;
import org.hibernate.annotations.Array;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 通知服务实现类
 * 实现NotifyContext接口，通过Gateway服务发送用户通知
 *
 * <AUTHOR>
 * @date :2025/5/26
 */
@Service
@Log4j2
public class NotifyService implements NotifyContext {
    public static final String DEFAULT_MSG_TYPE = "systemMsg";
    public static final String DEFAULT_MSG_LEVEL = "info";
    public static final int DEFAULT_DISPLAY_TIME = 5000;

    @Autowired
    private GatewayRemoteService gatewayRemoteService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private JsonConverter jsonConverter;

    @Override
    @Async
    public void sendByUsername(String username, String title, String message) {
        sendByUsername(username, title, message, DEFAULT_MSG_TYPE, DEFAULT_MSG_LEVEL);
    }

    @Override
    @Async
    public void sendByUsername(String username, String title, String message, String msgType, String level) {
        Assert.notBlank(username, "用户名不能为空");
        Assert.notBlank(title, "通知标题不能为空");
        Assert.notBlank(message, "通知内容不能为空");

        try {
            log.info("发送通知给用户: {}, 标题: {}, 内容: {}, 类型: {}, 级别: {}", username, title, message, msgType, level);

            NotifyDTO notifyDTO = createNotifyDTO(username, title, message, msgType, level);

            gatewayRemoteService.sendJsonContentToUserByUsername(jsonConverter.toJson(notifyDTO));

            log.info("成功发送通知给用户: {}", username);
        } catch (Exception e) {
            log.error("发送通知失败，用户: {}, 标题: {}, 内容: {}, 错误: {}", username, title, message, e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendByUserId(String userId, String title, String message) {
        sendByUserId(userId, title, message, DEFAULT_MSG_TYPE, DEFAULT_MSG_LEVEL);
    }

    @Override
    @Async
    public void sendByUserId(String userId, String title, String message, String msgType, String level) {
        Assert.notBlank(userId, "用户ID不能为空");
        Assert.notBlank(title, "通知标题不能为空");
        Assert.notBlank(message, "通知内容不能为空");

        try {
            log.info("发送通知给用户ID: {}, 标题: {}, 内容: {}, 类型: {}, 级别: {}", userId, title, message, msgType, level);

            NotifyDTO notifyDTO = createNotifyDTO(userId, title, message, msgType, level);

            gatewayRemoteService.sendJsonContentToUser(jsonConverter.toJson(notifyDTO));

            log.info("成功发送通知给用户ID: {}", userId);
        } catch (Exception e) {
            log.error("发送通知失败，用户ID: {}, 标题: {}, 内容: {}, 错误: {}", userId, title, message, e.getMessage(), e);
        }
    }

    /**
     * 创建NotifyDTO对象（使用默认参数）
     *
     * @param userIdentifier 用户标识（用户名或用户ID）
     * @param title 通知标题
     * @param message 通知内容
     * @return NotifyDTO对象
     */
    private NotifyDTO createNotifyDTO(String userIdentifier, String title, String message) {
        return createNotifyDTO(userIdentifier, title, message, DEFAULT_MSG_TYPE, DEFAULT_MSG_LEVEL);
    }

    /**
     * 创建NotifyDTO对象（指定消息类型和级别）
     *
     * @param userIdentifier 用户标识（用户名或用户ID）
     * @param title 通知标题
     * @param message 通知内容
     * @param msgType 消息类型（如：systemMsg, batchPrint等）
     * @param level 消息级别（如：info, warning, error等）
     * @return NotifyDTO对象
     */
    private NotifyDTO createNotifyDTO(String userIdentifier, String title, String message, String msgType, String level) {
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setUserName(userIdentifier);
        
        // 如果参数为空，使用默认值
        String finalMsgType = StrUtil.isNotBlank(msgType) ? msgType : DEFAULT_MSG_TYPE;
        String finalLevel = StrUtil.isNotBlank(level) ? level : DEFAULT_MSG_LEVEL;
        
        notifyDTO.setJsonContent(notifyDTO.new JsonContent(
                finalMsgType,
                finalLevel,
                title,
                message,
                DEFAULT_DISPLAY_TIME
        ));
        return notifyDTO;
    }
}
