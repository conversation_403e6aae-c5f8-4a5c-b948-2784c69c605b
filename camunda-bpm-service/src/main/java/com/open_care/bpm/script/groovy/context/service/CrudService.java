package com.open_care.bpm.script.groovy.context.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.common.delete.DeleteParamRequest;
import com.open_care.api.common.dto.BatchSaveDTO;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.crud.GetRequestDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.dto.crud.SaveRequestDTO;
import com.open_care.api.common.edit.EditDataResponse;
import com.open_care.bpm.script.groovy.context.CrudContext;
import com.open_care.bpm.script.groovy.context.exception.EntityCrudException;
import com.open_care.dto.QueryResponseDTO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/5/26
 */
@Log4j2
@Service
public class CrudService implements CrudContext {

    /**
     * 成功状态码
     */
    private static final String SUCCESS_STATUS = "0";

    /**
     * 操作名称常量
     */
    private static final String OPERATION_SAVE = "保存";
    private static final String OPERATION_GET = "获取";
    private static final String OPERATION_DELETE = "删除";
    private static final String OPERATION_QUERY = "查询";
    private static final String OPERATION_BATCH_SAVE = "批量保存";

    private static final String EAGER_PROPERTIES_ROOT = "/";

    @Autowired
    private ConfigurationRemoteService configurationRemoteService;

    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 保存实体
     *
     * @param requestDTO
     * @return
     */
    @Override
    public EditDataResponse save(SaveRequestDTO requestDTO) {
        try {
            OcResponse<?> save = configurationRemoteService.save(requestDTO);
            if (StrUtil.equals(SUCCESS_STATUS, ObjectUtil.toString(save.getStatus()))) {
                return objectMapper.readValue(objectMapper.writeValueAsString(save.getData()), new TypeReference<>() {
                });
            } else {
                logAndThrowForEntityCrud(requestDTO, OPERATION_SAVE, null);
            }
        } catch (JsonProcessingException e) {
            logAndThrowForEntityCrud(requestDTO, OPERATION_SAVE, e);
        }
        return null;
    }

    private static void logAndThrowForEntityCrud(SaveRequestDTO requestDTO, String operator, Exception e) {
        String msg = StrUtil.format("实体 {} 失败， data = {}", operator, requestDTO.getData());
        log.error(msg, e);
        throw new EntityCrudException(msg);
    }

    @Override
    public EditDataResponse get(String id, String entityName) {
        return get(id, entityName, EAGER_PROPERTIES_ROOT);
    }

    /**
     * get 实体
     *
     * @param id
     * @param entityName
     * @return
     */
    @Override
    public EditDataResponse get(String id, String entityName, String eagerProperties) {
        try {
            GetRequestDTO getRequestDTO = buildGetRequest(id, entityName, eagerProperties);
            OcResponse<?> response = configurationRemoteService.get(getRequestDTO, entityName, id);
            if (StrUtil.equals(SUCCESS_STATUS, ObjectUtil.toString(response.getStatus()))) {
                return objectMapper.readValue(objectMapper.writeValueAsString(response.getData()), new TypeReference<>() {
                });
            } else {
                logAndThrowForEntityCrud(id, entityName, OPERATION_GET, null);
            }
        } catch (JsonProcessingException e) {
            logAndThrowForEntityCrud(id, entityName, OPERATION_GET, e);
        }
        return null;
    }


    /**
     * 删除实体，默认为逻辑删除
     *
     * @param id
     * @return
     */
    @Override
    public EditDataResponse delete(String id) {
        try {
            DeleteParamRequest deleteRequest = buildDeleteRequest(id);
            OcResponse<?> response = configurationRemoteService.delete(deleteRequest);
            if (StrUtil.equals(SUCCESS_STATUS, ObjectUtil.toString(response.getStatus()))) {
                return objectMapper.readValue(objectMapper.writeValueAsString(response.getData()), new TypeReference<>() {
                });
            } else {
                logAndThrowForEntityCrud(id, "删除", OPERATION_DELETE, null);
            }
        } catch (JsonProcessingException e) {
            logAndThrowForEntityCrud(id, "删除", OPERATION_DELETE, e);
        }
        return null;
    }

    /**
     * 查询实体
     *
     * @param requestDTO
     * @return
     */
    @Override
    public QueryResponseDTO<?> query(QueryRequestDTO requestDTO) {
        try {
            OcResponse<?> response = configurationRemoteService.query(requestDTO);
            if (StrUtil.equals(SUCCESS_STATUS, ObjectUtil.toString(response.getStatus()))) {
                return objectMapper.readValue(objectMapper.writeValueAsString(response.getData()), new TypeReference<>() {
                });
            } else {
                logAndThrowForEntityCrud(requestDTO, OPERATION_QUERY, null);
            }
        } catch (JsonProcessingException e) {
            logAndThrowForEntityCrud(requestDTO, OPERATION_QUERY, e);
        }
        return null;
    }

    /**
     * 批量保存
     *
     * @param batchSaveDTO
     * @return
     */
    @Override
    public List<EditDataResponse> batchSave(BatchSaveDTO batchSaveDTO) {
        try {
            OcResponse<?> response = configurationRemoteService.batchSave(batchSaveDTO);
            if (StrUtil.equals(SUCCESS_STATUS, ObjectUtil.toString(response.getStatus()))) {
                return objectMapper.readValue(objectMapper.writeValueAsString(response.getData()), new TypeReference<>() {
                });
            } else {
                logAndThrowForEntityCrud(batchSaveDTO, OPERATION_BATCH_SAVE, null);
            }
        } catch (JsonProcessingException e) {
            logAndThrowForEntityCrud(batchSaveDTO, OPERATION_BATCH_SAVE, e);
        }
        return List.of();
    }

    /**
     * 构建GetRequestDTO
     *
     * @param id
     * @param eagerProperties
     * @return
     */
    private GetRequestDTO buildGetRequest(String id, String entityName, String eagerProperties) {
        GetRequestDTO getRequestDTO = new GetRequestDTO();
        getRequestDTO.setEntityId(id);
        getRequestDTO.setEagerProperties(eagerProperties);
        getRequestDTO.setEntityName(entityName);
        if (StrUtil.isNotBlank(eagerProperties)) {
            getRequestDTO.setEagerProperties(eagerProperties);
        }
        return getRequestDTO;
    }

    /**
     * 构建DeleteParamRequest
     *
     * @param id
     * @return
     */
    private DeleteParamRequest buildDeleteRequest(String id) {
        DeleteParamRequest deleteRequest = new DeleteParamRequest();
        deleteRequest.setEntityInstIds(Collections.singletonList(id));
        return deleteRequest;
    }

    /**
     * 异常处理 - 用于Get操作
     *
     * @param id
     * @param eagerProperties
     * @param operator
     * @param e
     */
    private static void logAndThrowForEntityCrud(String id, String eagerProperties, String operator, Exception e) {
        String msg = StrUtil.format("实体 {} 失败，id = {}, eagerProperties = {}", operator, id, eagerProperties);
        log.error(msg, e);
        throw new EntityCrudException(msg);
    }

    /**
     * 异常处理 - 用于Delete操作
     *
     * @param id
     * @param isLogical
     * @param operator
     * @param e
     */
    private static void logAndThrowForEntityCrud(String id, boolean isLogical, String operator, Exception e) {
        String msg = StrUtil.format("实体 {} 失败，id = {}, isLogical = {}", operator, id, isLogical);
        log.error(msg, e);
        throw new EntityCrudException(msg);
    }

    /**
     * 异常处理 - 用于Query操作
     *
     * @param requestDTO
     * @param operator
     * @param e
     */
    private static void logAndThrowForEntityCrud(QueryRequestDTO requestDTO, String operator, Exception e) {
        String msg = StrUtil.format("实体 {} 失败，entityName = {}, filters = {}", operator,
                requestDTO.getEntityName(), requestDTO.getFilters());
        log.error(msg, e);
        throw new EntityCrudException(msg);
    }

    /**
     * 异常处理 - 用于BatchSave操作
     *
     * @param batchSaveDTO
     * @param operator
     * @param e
     */
    private static void logAndThrowForEntityCrud(BatchSaveDTO batchSaveDTO, String operator, Exception e) {
        String msg = StrUtil.format("实体 {} 失败，entityName = {}, dataList size = {}", operator, batchSaveDTO.getEntityName(), batchSaveDTO);
        log.error(msg, e);
        throw new EntityCrudException(msg);
    }
}
