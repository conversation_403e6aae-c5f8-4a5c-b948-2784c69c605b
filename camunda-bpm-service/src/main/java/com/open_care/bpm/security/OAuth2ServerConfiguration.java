///**
// * Copyright 2018 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.security;
//
//import com.nimbusds.jose.JWSVerifier;
//import com.nimbusds.jose.crypto.RSASSAVerifier;
//import com.open_care.bpm.security.token.JWTWso2TokenStore;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
//import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
//import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.InputStream;
//import java.security.KeyStore;
//import java.security.cert.Certificate;
//import java.security.interfaces.RSAPublicKey;
//
//@Configuration
//@EnableWebSecurity
//@EnableResourceServer
//public class OAuth2ServerConfiguration extends ResourceServerConfigurerAdapter {
//    public static final String APPROVED_APPLICATION = "serviceProvider";
//
//
//    @Bean
//    public TokenStore tokenStore() {
//        return new JWTWso2TokenStore(jwsVerifier());
//    }
//
//
//    @Override
//    public void configure(ResourceServerSecurityConfigurer resourceServerSecurityConfigurer) {
//        //JDBC
//        //TokenStore wso2TokenStore = new JdbcWso2TokenStore(wso2OauthDataSource());
//
//        //Access Token Validation
////        TokenStore wso2TokenStore = new WSWso2TokenStore(makeOAuthAdminServiceClient());
////        resourceServerSecurityConfigurer.tokenStore(wso2TokenStore);
//
//        //JSW Validation
////        TokenStore jwtWso2TokenStore = new JWTWso2TokenStore();
//        resourceServerSecurityConfigurer.tokenStore(tokenStore());
//
//        //EXAMPLE: DO NOT DO THIS
//        //        System.setProperty("javax.net.ssl.trustStore", "C:/dev/wso2is-5.0.0/repository/resourceServerSecurityConfigurer/security/wso2carbon.jks");
//        //        System.setProperty("javax.net.ssl.trustStorePassword", "wso2carbon");
//        //        System.setProperty("javax.net.ssl.trustStoreType", "JKS");
//
//    }
//
//    @Bean
//    public JWSVerifier jwsVerifier() {
//        RSAPublicKey publicKey = null;
//        InputStream file = null;
//        try {
//            File file1 = new File(System.getProperty("user.dir") + "/src/main/resources/wso2carbon.jks");
//            if (file1.exists()) {
//                file = new FileInputStream(file1);
//            } else {
//                file = OAuth2ServerConfiguration.class.getResourceAsStream("/wso2carbon.jks");
//            }
//            String alias = "wso2carbon";
//
//            KeyStore keystore = KeyStore.getInstance(KeyStore.getDefaultType());
//            keystore.load(file, "wso2carbon".toCharArray());
//            Certificate cert = keystore.getCertificate(alias);
//            // Get certificate of public key
//            // Get public key
//            publicKey = (RSAPublicKey) cert.getPublicKey();
//        } catch (Exception e) {
//            throw new IllegalStateException(e);
//        }
//
//        return new RSASSAVerifier(publicKey);
//    }
//
//    @Override
//    public void configure(HttpSecurity httpSecurity) throws Exception {
//        //sets the authority as the application name defined in wso2
//        httpSecurity.antMatcher("/**")
//                .authorizeRequests()
//                .antMatchers("/**/*.css", "/**/*.js","app/*.js","app/*.css").permitAll()
//                .antMatchers("/**").permitAll()
//                //.antMatchers("/rest/deployment/create/**","/com/open_care/bpm/OcprocessDefList/**").permitAll()
//                .anyRequest().authenticated();//.hasAuthority(APPROVED_APPLICATION);
//    }
//
//
//}
