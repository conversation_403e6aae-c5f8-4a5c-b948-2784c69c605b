///**
// * Copyright 2018 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.security.token;
//
//import com.nimbusds.jose.JWSVerifier;
//import com.nimbusds.jwt.SignedJWT;
//import org.apache.commons.lang3.time.DateUtils;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
//import org.springframework.security.oauth2.common.OAuth2AccessToken;
//import org.springframework.security.oauth2.common.OAuth2RefreshToken;
//import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
//import org.springframework.security.oauth2.provider.OAuth2Authentication;
//import org.springframework.security.oauth2.provider.OAuth2Request;
//import org.springframework.security.oauth2.provider.token.TokenStore;
//
//import java.text.ParseException;
//import java.util.Collection;
//import java.util.Collections;
//import java.util.Date;
//import java.util.Map;
//import java.util.stream.Collectors;
//
////import org.springframework.boot.autoconfigure.security.oauth2.resource.FixedAuthoritiesExtractor;
////import org.springframework.boot.autoconfigure.security.oauth2.resource.FixedAuthoritiesExtractor;
//
//public class JWTWso2TokenStore implements TokenStore {
//
//
//    private final JWSVerifier jwsVerifier;
//
//    private static final String[] PRINCIPAL_KEYS = new String[] {"user", "username", "userid", "user_id", "login", "id", "name", "sub"};
//
//    public JWTWso2TokenStore(JWSVerifier jwsVerifier) {
//        this.jwsVerifier = jwsVerifier;
//    }
//
//    private boolean validateAccessTokenBySignedJWT(String accessToken){
//        try {
//            return SignedJWT.parse(accessToken).verify(jwsVerifier);
//        } catch ( Exception e ) {
//            e.printStackTrace();
//        }
//        return false;
//    }
//
//
//    @Override
//    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
//        return readAuthentication(token.getValue());
//    }
//
//    @Override
//    public OAuth2Authentication readAuthentication(String tokenValue) {
//        if(!validateAccessTokenBySignedJWT(tokenValue)){
//            throw new InvalidTokenException(tokenValue);
//        }
//
//        Map<String, Object> jwtClaimsSetFromAccessToken = getJWTClaimsSetFromAccessToken(tokenValue);
//
////        jwtClaimsSetFromAccessToken.putAll(userService.getUserInfoByUserNameReturnJsonObject(jwtClaimsSetFromAccessToken.get("sub").toString()));
//        return extractAuthentication(jwtClaimsSetFromAccessToken);
//    }
//
//    @Override
//    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
//
//    }
//
//    @Override
//    public OAuth2AccessToken readAccessToken(String tokenValue) {
//        if(!validateAccessTokenBySignedJWT(tokenValue)){
//            throw new InvalidTokenException(tokenValue);
//        }
//
//        Map<String, Object> jwtClaimsSetFromAccessToken = getJWTClaimsSetFromAccessToken(tokenValue);
//
//        // FIXME: 2018/7/1 should be replace to Joda to handle timestamp.
//        DefaultOAuth2AccessToken oAuth2AccessToken = new DefaultOAuth2AccessToken(tokenValue);
//        //Calendar calendar = new GregorianCalendar();
//        //calendar.add(Calendar.SECOND, (int)((Date)jwtClaimsSetFromAccessToken.get("exp")).getTime());
//        oAuth2AccessToken.setExpiration(DateUtils.addDays((Date) jwtClaimsSetFromAccessToken.get("exp"),1));
//
//        return oAuth2AccessToken;
//    }
//
//    @Override
//    public void removeAccessToken(OAuth2AccessToken token) {
//
//    }
//
//    @Override
//    public void storeRefreshToken(OAuth2RefreshToken refreshToken, OAuth2Authentication authentication) {
//
//    }
//
//    @Override
//    public OAuth2RefreshToken readRefreshToken(String tokenValue) {
//        return null;
//    }
//
//    @Override
//    public OAuth2Authentication readAuthenticationForRefreshToken(OAuth2RefreshToken token) {
//        return null;
//    }
//
//    @Override
//    public void removeRefreshToken(OAuth2RefreshToken token) {
//
//    }
//
//    @Override
//    public void removeAccessTokenUsingRefreshToken(OAuth2RefreshToken refreshToken) {
//
//    }
//
//    @Override
//    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
//        return null;
//    }
//
//    @Override
//    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
//        return null;
//    }
//
//    @Override
//    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
//        return null;
//    }
//
//    private Map<String, Object> getJWTClaimsSetFromAccessToken(String accessToken){
//        try {
//            return SignedJWT.parse(accessToken).getJWTClaimsSet().getClaims().entrySet().stream().collect(Collectors.toMap(key -> key.getKey(), value -> value.getValue()));
//        } catch ( ParseException e ) {
//            return Collections.<String, Object>singletonMap("error", "Could not fetch user details by access token.");
//        }
//    }
//
//    private OAuth2Authentication extractAuthentication(Map<String, Object> userInfoMap) {
//        UsernamePasswordAuthenticationToken authenticationToken = makeUsernamePasswordAuthenticationToken(userInfoMap);
//        OAuth2Request oAuth2Request = new OAuth2Request(
//                null, userInfoMap.get("azp").toString(), null, true, null
//                ,null, null, null, null);
//
//        return new OAuth2Authentication(oAuth2Request, authenticationToken);
//    }
//
//    private UsernamePasswordAuthenticationToken makeUsernamePasswordAuthenticationToken(Map<String, Object> userInfoMap) {
//        Object principal = getPrincipal(userInfoMap);
//        //List<GrantedAuthority> authorities = new FixedAuthoritiesExtractor().extractAuthorities(userInfoMap);
//        //UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(principal, "N/A", authorities);
//        //authenticationToken.setDetails(userInfoMap);
//        return null;//authenticationToken;
//    }
//
//    /**
//     * Return the principal that should be used for the token. The default implementation
//     * looks for well know {@code user*} keys in the userInfoMap.
//     *
//     * @param userInfoMap the source userInfoMap
//     * @return the principal or {@literal "unknown"}
//     */
//    protected Object getPrincipal(Map<String, Object> userInfoMap) {
//        for (String key : PRINCIPAL_KEYS) {
//            if (userInfoMap.containsKey(key)) {
//                return userInfoMap;
//            }
//        }
//
//        return "unknown";
//    }
//
//}
