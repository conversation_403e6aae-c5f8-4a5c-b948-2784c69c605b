///**
// * Copyright 2021 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.security;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.web.DefaultSecurityFilterChain;
//import org.springframework.stereotype.Component;
//
//import jakarta.servlet.Filter;
//
//@Component("permitAllSecurityConfig")
//public class PermitAllSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
//
//    @Autowired
//    private Filter uriFormatFilter;
//
//    @Bean
//    public FilterRegistrationBean setFilter() {
//        FilterRegistrationBean filterBean = new FilterRegistrationBean();
//        filterBean.setFilter(uriFormatFilter);
//        filterBean.setName("uriFormatFilter");
//        filterBean.addUrlPatterns("/*");
//        filterBean.setOrder(-10000);
//        return filterBean;
//    }
//
//}
