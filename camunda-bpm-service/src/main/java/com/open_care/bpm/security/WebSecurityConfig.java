///**
// * Copyright 2018 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.security;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.authentication.AuthenticationManager;
//import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
//import org.springframework.security.web.SecurityFilterChain;
//import org.springframework.security.web.configuration.WebSecurityCustomizer;
//
////@Configuration
////@EnableWebSecurity
////@EnableResourceServer
////@EnableGlobalMethodSecurity(prePostEnabled = true) //开启security注解
//public class WebSecurityConfig {
//
//    @Bean
//    public AuthenticationManager authenticationManager() throws Exception {
//        return authentication -> authentication;
//    }
//
//    @Bean
//    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
//        //允许所有用户访问"/"和"/home"
//        /*http.authorizeRequests()
//                .antMatchers("/**").permitAll();*/
//        http.antMatcher("/**")
//                .authorizeRequests()
//                .antMatchers("/**").permitAll();
//
//        return http.build();
//    }
//
//    @Bean
//    public WebSecurityCustomizer webSecurityCustomizer() {
//        //解决静态资源被拦截的问题
//        return (web) -> web.ignoring().antMatchers("/**/*.js", "/lang/*.json", "/**/*.css", "/**/*.js", "/**/*.map", "/**/*.html",
//                "/**/*.png");
//    }
//}