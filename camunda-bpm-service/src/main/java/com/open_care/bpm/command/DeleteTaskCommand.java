/**
 * Copyright 2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.command;

import org.camunda.bpm.engine.impl.interceptor.Command;
import org.camunda.bpm.engine.impl.interceptor.CommandContext;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.TaskEntity;
import org.camunda.bpm.engine.impl.persistence.entity.TaskManager;

public class DeleteTaskCommand implements Command {
    protected String taskId;
    protected String deleteReason;

    public DeleteTaskCommand(String taskId) {
        this.taskId = taskId;
    }

    @Override
    public Void execute(CommandContext commandContext) {
        //获取所需服务
        TaskManager taskManager = commandContext.getTaskManager();
        //获取当前任务的来源任务及来源节点信息
        TaskEntity currentTask = taskManager.findTaskById(taskId);

        //删除当前任务,来源任务
        taskManager.deleteTask(currentTask, "retakeTask", false, false);
        return null;
    }

}
