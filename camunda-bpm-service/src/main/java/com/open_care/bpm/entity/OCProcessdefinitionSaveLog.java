/**
 * Copyright 2018-2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.JsonAdapter;
import com.open_care.bpm.config.OCDateTimeTypeAdapter;

import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Entity
public class OCProcessdefinitionSaveLog {
    @jakarta.persistence.Id
    @jakarta.persistence.GeneratedValue(strategy = jakarta.persistence.GenerationType.AUTO, generator = "uuid")
    @org.hibernate.annotations.GenericGenerator(name = "uuid", parameters = {}, strategy = "uuid2")
    String id;
    String operator;
    @JsonAdapter(OCDateTimeTypeAdapter.class)
    Date operatorTime;
    Integer version;
    Integer versionFrom;
    String resourcelocation;
    String ocprocessdefinitionId;
    String processDepKey;
    Boolean deployed;
    // json 序列化是忽略该属性，该属性发布后自动更新到数据库中，所有 config 查询 OCProcessdefinitionSaveLog 时 不返回该属性
    @JsonIgnore
    private String processDefinitionId;

}
