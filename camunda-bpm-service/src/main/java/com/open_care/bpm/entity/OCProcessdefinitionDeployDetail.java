/**
 * Copyright 2018-2019 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name="OCProcDefDeployDetail")
public class OCProcessdefinitionDeployDetail {
    @jakarta.persistence.Id
    @jakarta.persistence.GeneratedValue(strategy = jakarta.persistence.GenerationType.AUTO, generator = "uuid")
    @org.hibernate.annotations.GenericGenerator(name = "uuid", parameters = {}, strategy = "uuid2")
    String id;
    String ocProcessId;
    String processDepId;
    String decisionDefId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOcProcessId() {
        return ocProcessId;
    }

    public void setOcProcessId(String ocProcessId) {
        this.ocProcessId = ocProcessId;
    }

    public String getProcessDepId() {
        return processDepId;
    }

    public void setProcessDepId(String processDepId) {
        this.processDepId = processDepId;
    }

    public String getDecisionDefId() {
        return decisionDefId;
    }

    public void setDecisionDefId(String decisionDefId) {
        this.decisionDefId = decisionDefId;
    }

}
