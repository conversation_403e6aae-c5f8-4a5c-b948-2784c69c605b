/**
 * Copyright 2018-2022 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.entity;

import com.google.gson.annotations.JsonAdapter;
import com.open_care.bpm.config.OCDateTimeTypeAdapter;

import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class OCProcessdefinition {
    @jakarta.persistence.Id
//    @jakarta.persistence.GeneratedValue(strategy = jakarta.persistence.GenerationType.AUTO, generator = "uuid")
//    @org.hibernate.annotations.GenericGenerator(name = "uuid", parameters = {}, strategy = "uuid2")
    String id;
    String name;
    String created;
    @JsonAdapter(OCDateTimeTypeAdapter.class)
    Date   createdTime;
    String status;
    String description;
    String procDefId;
    String tenantId;
    Integer deployFromVersion;
    String type;

}
