package com.open_care.bpm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.GenericGenerator;

/**
 * 候选人/组基础实体
 *
 * <AUTHOR>
 */
@Data
@MappedSuperclass
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class CandidateEntity {

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 标识符（用户ID或组ID）
     */
    @Column(name = "identifier", nullable = false)
    private String identifier;

    /**
     * 名称（用户名或组名）
     */
    @Column(name = "name")
    private String name;

    /**
     * 关联的任务转办日志ID
     */
    @ManyToOne(cascade = {})
    @ToString.Exclude
    private TaskAssigneeLog taskAssigneeLog;

    /**
     * 类型：原始候选组或新候选组
     */
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private CandidateType type;

    /**
     * 候选组类型枚举
     */
    public enum CandidateType {
        /**
         * 原始候选组
         */
        ORIGINAL,

        /**
         * 新候选组
         */
        NEW
    }
} 