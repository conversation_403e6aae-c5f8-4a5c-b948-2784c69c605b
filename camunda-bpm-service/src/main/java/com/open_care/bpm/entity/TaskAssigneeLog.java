package com.open_care.bpm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.util.Date;
import java.util.List;

/**
 * 任务转办日志实体
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "oc_task_assignee_log")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAssigneeLog {

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    @Column(name = "id", length = 36)
    private String id;

    /**
     * 任务ID
     */
    @Column(name = "task_id", nullable = false)
    private String taskId;

    /**
     * 流程实例ID
     */
    @Column(name = "process_instance_id")
    private String processInstanceId;
    
    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id")
    private String processDefinitionId;
    
    /**
     * 任务定义Key
     */
    @Column(name = "task_definition_key")
    private String taskDefinitionKey;
    
    /**
     * 任务名称
     */
    @Column(name = "task_name")
    private String taskName;
    
    /**
     * 原任务所有者
     */
    @Column(name = "original_owner")
    private String originalOwner;

    /**
     * 新任务所有者
     */
    @Column(name = "new_owner")
    private String newOwner;

    /**
     * 原任务受让人
     */
    @Column
    private String originalAssignee;

    /**
     * 原任务候选人
     */
    @OneToMany(mappedBy = "taskAssigneeLog", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CandidateUser> originalCandidateUsers;

    /**
     * 原任务候选组
     */
    @OneToMany(mappedBy = "taskAssigneeLog", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CandidateGroup> originalCandidateGroups;

    /**
     * 新任务受让人
     */
    @Column
    private String newAssignee;

    /**
     * 新任务候选人
     */
    @OneToMany(mappedBy = "taskAssigneeLog", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CandidateUser> newCandidateUsers;

    /**
     * 新任务候选组
     */
    @OneToMany(mappedBy = "taskAssigneeLog", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CandidateGroup> newCandidateGroups;

    /**
     * 转办原因
     */
    @Column(name = "assign_reason", columnDefinition = "TEXT")
    private String assignReason;

    /**
     * 转办备注
     */
    @Column(name = "assign_remark", columnDefinition = "TEXT")
    private String assignRemark;

    /**
     * 转办时间
     */
    @Column(name = "assign_time")
    @Temporal(TemporalType.TIMESTAMP)
    @CreatedDate
    private Date assignTime;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private String tenantId;
    
    /**
     * 转办人ID
     */
    @Column(name = "operator")
    private String operator;

} 