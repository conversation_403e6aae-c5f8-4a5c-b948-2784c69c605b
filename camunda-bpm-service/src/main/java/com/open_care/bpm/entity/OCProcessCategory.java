package com.open_care.bpm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;

/**
 * <AUTHOR>
 * @date :2025/2/27
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class OCProcessCategory {

    @jakarta.persistence.Id
    @jakarta.persistence.GeneratedValue(strategy = jakarta.persistence.GenerationType.AUTO, generator = "uuid")
    @org.hibernate.annotations.GenericGenerator(name = "uuid", parameters = {}, strategy = "uuid2")
    private String id;

    // "类别编号"
    private String categoryNo;

    // "类别名称"
    private String categoryName;

    // "父类别",
    @ManyToOne
    private OCProcessCategory parentCategory;

    // "描述"
    private String description;
}
