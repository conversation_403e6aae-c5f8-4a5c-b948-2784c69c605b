package com.open_care.bpm.constants;

/**
 * 流程相关常量
 */
public interface ProcessConst {
    /**
     * 任务是否被委托的
     */
    String TASK_VAR_IS_DELEGATED = "#task_var_isDelegated";

    /**
     * 流程的委托人
     */
    String TASK_VAR_DELEGATOR = "#task_var_delegator";

    /**
     * 流程的优先级
     */
    String PROCESS_VAR_PRIORITY = "#process_var_priority";


    /**
     * 是否是回退产生的任务
     */
    String TASK_VAR_IS_CREATED_BY_ROLLBACK = "#process_var_isCreatedByRollback";


    //
    /**
     * 任务受让人
     */
    String TASK_VAR_ASSIGNEE = "taskAssignee";
    /**
     * 任务候选人
     */
    String TASK_VAR_CANDIDATES = "taskCandidates";
} 