/**
 * Copyright 2018-2019 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.repository;

import com.open_care.bpm.entity.OCProcessdefinition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProcessDefRepository extends JpaRepository<OCProcessdefinition,String> {
    List<OCProcessdefinition> findByTenantId(String s);

    @Query("select ocp from OCProcessdefinition ocp where tenantId=:tenantId")
    Page<OCProcessdefinition> getTenantId(@Param("tenantId") String tenantId, Pageable pageable);

    //-- NameContaining NameLike 根据名称进行模糊查询
    List<OCProcessdefinition> findByTenantIdAndNameContainingAndStatusAndCreatedContaining(String tenantId, String processDefName, String status, String created);

    @Query("select name from OCProcessdefinition ocp where ocp.tenantId=?1")
    List<String> getNameListByTenantId(String tenantId);

    @Query("select ocp from OCProcessdefinition ocp where ocp.tenantId=?1 and (ocp.status = ?3 or (?3 is null or length(?3) = 0 ) ) and (ocp.name like '%'||?2||'%' or (?2 is null or length(?2) = 0 )) and (ocp.created like '%'||?4||'%' or (?4 is null or length(?4) = 0 ))")
    //@Query("select ocp from OCProcessdefinition ocp where ocp.name like ?1")
    List<OCProcessdefinition> getListBySearchInfo(String tenantId, String processDefName, String status, String created, PageRequest pageRequest);

    List<OCProcessdefinition> findByprocDefId(String s);

    List<OCProcessdefinition> findByProcDefIdOrderByCreatedTimeDesc(String s);
}
