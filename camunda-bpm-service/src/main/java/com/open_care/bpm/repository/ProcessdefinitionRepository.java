/**
 * Copyright 2021 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.repository;

import com.open_care.bpm.entity.OCProcessdefinition;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @author: lee
 * @Description:
 * @Date:2021/12/28 22:48
 * @Modified By:
 */
public interface ProcessdefinitionRepository extends JpaRepository<OCProcessdefinition, String> {

    List<OCProcessdefinition> findByTenantIdAndNameContaining(String tenantId, String name, Pageable pageable);

    List<OCProcessdefinition> findByTenantIdAndStatus(String tenantId, String status, Pageable pageable);

    List<OCProcessdefinition> findByTenantIdAndStatusAndNameContaining(String tenantId, String status, String name, Pageable pageable);
}
