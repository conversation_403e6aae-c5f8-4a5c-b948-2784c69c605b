package com.open_care.bpm.repository;

import com.open_care.bpm.entity.OCProcessCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/2/28
 */
public interface ProcessCategoryRepository extends JpaRepository<OCProcessCategory, String> {


    @Query(
            "SELECT c FROM OCProcessCategory c " +
                    "WHERE (:parentCategories IS NULL and c.parentCategory is null) OR (:parentCategories IS NOT NULL and c.parentCategory in :parentCategories)"
    )
    List<OCProcessCategory> findByParentCategoryIn(Collection<OCProcessCategory> parentCategories);


    @Query(
            "SELECT c FROM OCProcessCategory c " +
                    "WHERE (c.categoryName = :categoryName) and ((:id is null )or (:id is not null and c.id != :id))"
    )
    List<OCProcessCategory> findByCategoryNameEqualsAndIdNot(String categoryName, String id);

}
