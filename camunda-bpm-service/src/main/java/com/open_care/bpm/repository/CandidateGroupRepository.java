package com.open_care.bpm.repository;

import com.open_care.bpm.entity.CandidateGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 候选组仓库接口
 *
 * <AUTHOR>
 */
@Repository
public interface CandidateGroupRepository extends JpaRepository<CandidateGroup, String> {

    /**
     * 根据任务转办日志ID和类型查询候选组
     *
     * @param taskAssigneeLogId 任务转办日志ID
     * @param type 候选组类型
     * @return 候选组列表
     */
    List<CandidateGroup> findByTaskAssigneeLogIdAndType(String taskAssigneeLogId, CandidateGroup.CandidateType type);
} 