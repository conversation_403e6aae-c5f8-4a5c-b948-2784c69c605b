/**
 * Copyright 2018 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
package com.open_care.bpm.repository;

import com.open_care.bpm.entity.OCProcessdefinitionSaveLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface ProcessDefSaveLogRepository extends JpaRepository<OCProcessdefinitionSaveLog,String> {
    OCProcessdefinitionSaveLog findByOcprocessdefinitionIdAndVersion(String id, int version);

    List<OCProcessdefinitionSaveLog> findByOcprocessdefinitionId(String id);

    @Query("SELECT max(version) FROM OCProcessdefinitionSaveLog where ocprocessdefinitionId = ?1 ")
    String getMaxVersionByOcprocessdefinitionId(String id);

    Optional<OCProcessdefinitionSaveLog> findByProcessDefinitionId(String processDefinitionId);
}
