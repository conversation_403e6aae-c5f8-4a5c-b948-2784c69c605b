///**
// * Copyright 2018 Open-Care - All Rights Reserved
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// *
// */
//
//package com.open_care.bpm.repository;
//
//import com.open_care.http.HttpRequestParameters;
//import com.open_care.util.RestTemplateUtil;
//import com.open_care.util.json.JsonConverter;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.client.RestTemplate;
//
//public class RestApiClient {
//    private String urlServerPort;
//    private  JsonConverter jsonConverter;
//    private RestTemplate restTemplate;
//
//    public RestApiClient() {
//    }
//
//    public RestApiClient(String urlServerPort, JsonConverter jsonConverter, RestTemplate restTemplate) {
//        this.urlServerPort = urlServerPort;
//        this.jsonConverter = jsonConverter;
//        this.restTemplate = restTemplate;
//    }
//
//    public ResponseEntity<String> restTemplateExchange(HttpRequestParameters httpRequestParameters) {
//        return RestTemplateUtil.restTemplateExchange(restTemplate, getUrlServerPort(), httpRequestParameters);
//    }
//
//    private String getUrlServerPort() {
//        return urlServerPort;
//    }
//}
