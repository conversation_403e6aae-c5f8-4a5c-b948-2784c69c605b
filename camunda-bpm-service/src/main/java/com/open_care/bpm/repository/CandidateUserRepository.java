package com.open_care.bpm.repository;

import com.open_care.bpm.entity.CandidateUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 候选用户仓库接口
 *
 * <AUTHOR>
 */
@Repository
public interface CandidateUserRepository extends JpaRepository<CandidateUser, String> {

    /**
     * 根据任务转办日志ID和类型查询候选用户
     *
     * @param taskAssigneeLogId 任务转办日志ID
     * @param type 候选人类型
     * @return 候选用户列表
     */
    List<CandidateUser> findByTaskAssigneeLogIdAndType(String taskAssigneeLogId, CandidateUser.CandidateType type);

    /**
     * 根据任务转办日志ID查询受让人
     *
     * @param taskAssigneeLogId 任务转办日志ID
     * @param isAssignee 是否为受让人
     * @return 受让人
     */
    CandidateUser findByTaskAssigneeLogIdAndIsAssignee(String taskAssigneeLogId, Boolean isAssignee);
} 