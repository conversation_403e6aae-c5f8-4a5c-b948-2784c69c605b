package com.open_care.util.json;

import cn.hutool.json.JSON;

import java.lang.reflect.Type;

/**
 * JSON转换器接口
 */
public interface JsonConverter {

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串
     */
    String toJson(Object object);

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param json JSON字符串
     * @param classOfT 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    <T> T fromJson(String json, Class<T> classOfT);

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param json JSON字符串
     * @param typeOfT 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    <T> T fromJson(String json, Type typeOfT);
} 