package com.open_care.util.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;

import java.lang.reflect.Type;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date :2025/4/10
 */
public class JacksonJsonConvert implements JsonConverter {

    public static JsonConverter JSON_CONVERTER = new JacksonJsonConvert();

    private final ObjectMapper objectMapper;
    
    public JacksonJsonConvert() {
        this(new ObjectMapper());
    }
    
    public JacksonJsonConvert(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper.copy();
        // 配置忽略不存在的属性
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串
     */
    @Override
    public String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换对象到JSON字符串失败", e);
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param json     JSON字符串
     * @param classOfT 目标类型
     * @return 转换后的对象
     */
    @Override
    public <T> T fromJson(String json, Class<T> classOfT) {
        try {
            return objectMapper.readValue(json, classOfT);
        } catch (IOException e) {
            throw new RuntimeException("从JSON字符串转换到对象失败", e);
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param json    JSON字符串
     * @param typeOfT 目标类型
     * @return 转换后的对象
     */
    @Override
    public <T> T fromJson(String json, Type typeOfT) {
        try {
            JavaType javaType = objectMapper.constructType(typeOfT);
            return objectMapper.readValue(json, javaType);
        } catch (IOException e) {
            throw new RuntimeException("从JSON字符串转换到对象失败", e);
        }
    }
}
