plugins {
    id("java-library")
    id("maven-publish")
}

dependencies {
    api(project(":camunda-bpm-api"))
    api(project(":camunda-common"))
    api(project(":camunda-bpm-core"))

    // Camunda相关依赖
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter")
    implementation("org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring")
    implementation("org.camunda.bpm:camunda-engine-plugin-spin")
    implementation("org.camunda.spin:camunda-spin-dataformat-json-jackson")
    implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest")

    // 使用Jakarta版本的Camunda Engine REST依赖
    implementation("org.camunda.bpm:camunda-engine-rest-core-jakarta")
    // 注释掉可能导致问题的依赖
    // implementation("org.camunda.bpm:camunda-engine-rest-jakarta:7.23.0")
    // 添加缺失的依赖，修改为正确的GroupId
    implementation("org.camunda.bpm:camunda-engine-rest-jakarta")

    // Spring相关依赖
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("jakarta.persistence:jakarta.persistence-api")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.hibernate.orm:hibernate-core")
    implementation("org.postgresql:postgresql")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
    implementation("org.springframework.retry:spring-retry")
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("com.open-care:hibernate-dialect")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
//    implementation("org.springframework.boot:spring-boot-starter-security")
//    implementation("org.springframework.security.oauth.boot:spring-security-oauth2-autoconfigure")
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    implementation("org.springframework.retry:spring-retry")

    // Jakarta依赖 (替代javax)
    implementation("jakarta.persistence:jakarta.persistence-api")
    implementation("jakarta.servlet:jakarta.servlet-api")
    implementation("jakarta.annotation:jakarta.annotation-api")
    implementation("jakarta.validation:jakarta.validation-api")

    // Hibernat

    // DOM4J
    implementation("org.dom4j:dom4j")

    // Open-Care 依赖
//    implementation("com.open-care:open-care-json-converter")
    api("com.open-care:api-jsonschema-definition")
//    implementation("com.open-care:open-care-core-util")
//    implementation("com.open-care:open-care-common-util")

    // 其他工具类依赖
    implementation("com.nimbusds:nimbus-jose-jwt")
    implementation("net.logstash.logback:logstash-logback-encoder")
    implementation("org.reflections:reflections")
    implementation("uk.com.robust-it:cloning")
    implementation("com.fasterxml.jackson.core:jackson-databind")
    implementation("com.fasterxml.jackson.core:jackson-core")
    implementation("com.google.code.gson:gson")
    implementation("cn.hutool:hutool-all")
    implementation("com.belerweb:pinyin4j")

    // Groovy支持 - 用于Camunda脚本引擎
    implementation("org.codehaus.groovy:groovy-all")
    implementation("org.codehaus.groovy:groovy-jsr223")


    // Lombok和MapStruct
    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")
    implementation("org.mapstruct:mapstruct")
    annotationProcessor("org.mapstruct:mapstruct-processor")
    annotationProcessor("org.projectlombok:lombok-mapstruct-binding")

    testCompileOnly("org.projectlombok:lombok")
    testAnnotationProcessor("org.projectlombok:lombok")
    testImplementation("org.mapstruct:mapstruct")
    testAnnotationProcessor("org.mapstruct:mapstruct-processor")
    testAnnotationProcessor("org.projectlombok:lombok-mapstruct-binding")


    // 添加测试依赖
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.mockito:mockito-junit-jupiter")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")
    //指定旧版本，camunda如果使用h2，使的是旧版本的h2
    testImplementation("com.h2database:h2")


}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

tasks.withType<GenerateModuleMetadata> {
    suppressedValidationErrors.add("dependencies-without-versions")
}

//publishing {
//    publications {
//        create<MavenPublication>("maven") {
//            from(components["java"])
//
//            pom {
//                name.set("Camunda BPM Service")
//                description.set("Camunda BPM Service library")
//            }
//
//            versionMapping {
//                usage("java-api") {
//                    fromResolutionOf("runtimeClasspath")
//                }
//                usage("java-runtime") {
//                    fromResolutionResult()
//                }
//            }
//        }
//    }
//
//    repositories {
//        mavenLocal()
//        maven {
//            val releasesRepoUrl = "https://repo.open-care.com/repository/maven-releases/"
//            val snapshotsRepoUrl = "https://repo.open-care.com/repository/maven-snapshots/"
//            url = uri(if (version.toString().endsWith("SNAPSHOT")) snapshotsRepoUrl else releasesRepoUrl)
//
//            credentials {
//                username = project.findProperty("nexusUsername") as String? ?: ""
//                password = project.findProperty("nexusPassword") as String? ?: ""
//            }
//        }
//    }
//}

// 配置测试任务
tasks.withType<Test> {
    useJUnitPlatform()
}