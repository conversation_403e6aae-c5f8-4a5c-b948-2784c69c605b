#!/bin/bash

#!/bin/bash

USER_HOME=/opt/home

if [ ! -d "${USER_HOME}" ]; then
    mkdir "${USER_HOME}"
fi

# 是一种内存优化技术，在 64 位 JVM 中使用压缩指针（32 位）来代替常规的 64 位指针，以减少内存占用 这些压缩指针指向类元数据
# -XX:CompressedClassSpaceSize=128M 是设置compressed_class_space  29M  30M 1024M 2.87% 区域大小

echo "启动 camunda"

java  -Duser.home=${USER_HOME}  \
-jar  \
-XX:+StartAttachListener  \
-Dfile.encoding=UTF-8  \
-Xms1028M -Xmx1028M \
-Djdk.attach.allowAttachSelf=true \
-XX:CompressedClassSpaceSize=128M \
-XX:OnOutOfMemoryError=/opt/open-care/oom_trigger.sh \
 /opt/open-care/camunda.jar
