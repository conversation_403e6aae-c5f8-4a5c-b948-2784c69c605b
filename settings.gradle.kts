pluginManagement {
    plugins {
        id("java")
        id("org.springframework.boot") version "3.3.6"
        id("io.spring.dependency-management") version "1.1.6"
    }
}
// The settings file is the entry point of every Gradle build.
// Its primary purpose is to define the subprojects.
// It is also used for some aspects of project-wide configuration, like managing plugins, dependencies, etc.
// https://docs.gradle.org/current/userguide/settings_file_basics.html

dependencyResolutionManagement {
    // Use Maven Central as the default repository (where <PERSON><PERSON><PERSON> will download dependencies) in all subprojects.
    @Suppress("UnstableApiUsage")
    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven {
            url = uri("https://repo.open-care.com/repository/maven-public/")
            credentials {
                username = providers.gradleProperty("nexusUsername").orNull
                password = providers.gradleProperty("nexusPassword").orNull
            }
        }
        mavenCentral()
    }
}

rootProject.name = "camunda-plugin"
include("camunda-server")
include("camunda-server-nacos-support")
include("camunda-pulsar-connector-plugin")
include("camunda-bpm-api")
include("camunda-bpm-service")
include("camunda-common")

include("camunda-bpm-core")