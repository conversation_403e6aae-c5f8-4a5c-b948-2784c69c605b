
------------------------------------------------------------
Project ':camunda-server'
------------------------------------------------------------

annotationProcessor - Annotation processors and their dependencies for source set 'main'.
No dependencies

bootArchives - Configuration for Spring Boot archive artifacts. (n)
No dependencies

compileClasspath - Compile classpath for source set 'main'.
+--- project :camunda-server-nacos-support
+--- project :camunda-pulsar-connector-plugin
+--- project :camunda-bpm-service
+--- org.springframework.boot:spring-boot-starter-web -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6
|    |    +--- org.springframework.boot:spring-boot:3.3.6
|    |    |    +--- org.springframework:spring-core:6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8
|    |    |              \--- io.micrometer:micrometer-commons:1.13.8
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.6
|    |    |    +--- ch.qos.logback:logback-classic:1.5.12
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.12
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- org.springframework.boot:spring-boot-starter-json:3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    +--- org.springframework:spring-web:6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.33
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|    +--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.springframework:spring-webmvc:6.1.15
|         +--- org.springframework:spring-aop:6.1.15 (*)
|         +--- org.springframework:spring-beans:6.1.15 (*)
|         +--- org.springframework:spring-context:6.1.15 (*)
|         +--- org.springframework:spring-core:6.1.15 (*)
|         +--- org.springframework:spring-expression:6.1.15 (*)
|         \--- org.springframework:spring-web:6.1.15 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0
|    +--- org.camunda.bpm:camunda-engine-spring-6:7.23.0
|    |    \--- org.camunda.bpm:camunda-engine:7.23.0
|    |         +--- org.camunda.bpm.model:camunda-bpmn-model:7.23.0
|    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         +--- org.camunda.bpm.model:camunda-cmmn-model:7.23.0
|    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         +--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         +--- org.camunda.bpm.dmn:camunda-engine-dmn:7.23.0
|    |         |    +--- org.camunda.commons:camunda-commons-utils:7.23.0
|    |         |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0
|    |         |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |         |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0
|    |         |    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |         |    +--- org.camunda.bpm.model:camunda-dmn-model:7.23.0
|    |         |    |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0
|    |         |    |    \--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-juel:7.23.0
|    |         |    |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         |    |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-scala:7.23.0
|    |         |    |    +--- org.camunda.feel:feel-engine:1.19.1
|    |         |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |         |    |    |    \--- com.fasterxml.uuid:java-uuid-generator:5.1.0
|    |         |    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |         |    |    \--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |         |    +--- org.camunda.feel:feel-engine:1.19.1 (*)
|    |         |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         +--- org.mybatis:mybatis:3.5.15
|    |         +--- org.springframework:spring-beans:6.2.4 -> 6.1.15 (*)
|    |         \--- joda-time:joda-time:2.12.5 -> 2.12.7
|    +--- org.springframework:spring-context:6.2.5 -> 6.1.15 (*)
|    +--- org.springframework:spring-jdbc:6.2.5 -> 6.1.15
|    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.springframework:spring-tx:6.1.15
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    +--- org.springframework:spring-tx:6.2.5 -> 6.1.15 (*)
|    +--- org.springframework:spring-orm:6.2.5 -> 6.1.15
|    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    +--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    \--- org.springframework:spring-tx:6.1.15 (*)
|    +--- org.springframework.boot:spring-boot-autoconfigure:3.4.4 -> 3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-starter:3.4.4 -> 3.3.6 (*)
|    +--- org.apache.commons:commons-lang3:3.17.0 -> 3.14.0
|    +--- com.fasterxml.uuid:java-uuid-generator:4.3.0 -> 5.1.0 (*)
|    \--- com.sun.xml.bind:jaxb-impl:4.0.5
|         \--- com.sun.xml.bind:jaxb-core:4.0.5
|              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|                   \--- jakarta.activation:jakarta.activation-api:2.1.3
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0
|    |    +--- commons-fileupload:commons-fileupload:1.5
|    |    |    \--- commons-io:commons-io:2.11.0 -> 2.16.1
|    |    +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6
|         +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-validation:3.3.6
|         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|         |    \--- org.hibernate.validator:hibernate-validator:8.0.1.Final
|         |         +--- jakarta.validation:jakarta.validation-api:3.0.2
|         |         +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|         |         \--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|         +--- org.springframework:spring-web:6.1.15 (*)
|         +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9
|         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9
|         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    \--- org.glassfish.hk2:osgi-resource-locator:1.0.3
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9
|         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.9
|         |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    \--- jakarta.validation:jakarta.validation-api:3.0.2
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.9
|         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.9
|         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|         |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|         |    +--- org.jboss.logging:jboss-logging:3.6.0.Final -> 3.5.3.Final
|         |    +--- org.glassfish.expressly:expressly:5.0.0
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.ext:jersey-spring6:3.1.9
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.9
|         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6
|         |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    +--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6
|         |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6
|         |    |    |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    |    \--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    |    \--- org.javassist:javassist:3.30.2-GA
|         |    |    \--- org.javassist:javassist:3.30.2-GA
|         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|         |    +--- org.glassfish.hk2:hk2:3.0.6
|         |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-core:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    |    \--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-runlevel:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|         |    |    |    \--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    \--- org.glassfish.hk2:class-model:3.0.6
|         |    |         \--- org.ow2.asm:asm-commons:9.6
|         |    +--- org.glassfish.hk2:spring-bridge:3.0.6
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         \--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.9
|              +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|              +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.9
|              |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|              +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (*)
|              +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|              +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (*)
|              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp-core:7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0 (*)
|    |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.4.4 -> 3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6 (*)
|    \--- org.camunda.bpm.webapp:camunda-webapp-webjar:7.23.0
|         \--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- com.zaxxer:HikariCP:5.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    \--- org.springframework:spring-jdbc:6.1.15 (*)
+--- org.postgresql:postgresql -> 42.7.4
+--- org.springframework.boot:spring-boot-starter-actuator -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.6
|    |    +--- org.springframework.boot:spring-boot-actuator:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
|    +--- io.micrometer:micrometer-observation:1.13.8 (*)
|    \--- io.micrometer:micrometer-jakarta9:1.13.8
|         +--- io.micrometer:micrometer-core:1.13.8
|         |    +--- io.micrometer:micrometer-commons:1.13.8
|         |    \--- io.micrometer:micrometer-observation:1.13.8 (*)
|         +--- io.micrometer:micrometer-commons:1.13.8
|         \--- io.micrometer:micrometer-observation:1.13.8 (*)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2
|    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-core:2.1.2
|    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-eventbus:2.1.2
|    |    |    +--- org.reactivestreams:reactive-streams:1.0.1 -> 1.0.4
|    |    |    +--- org.eclipse.collections:eclipse-collections:9.0.0
|    |    |    |    \--- org.eclipse.collections:eclipse-collections-api:9.0.0
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.8.10 -> 2.17.3 (*)
|    |    |    +--- com.lmax:disruptor:3.3.7
|    |    |    +--- io.vavr:vavr:0.9.2
|    |    |    |    \--- io.vavr:vavr-match:0.9.2
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0
|    +--- org.springframework.cloud:spring-cloud-starter:4.2.0 -> 4.1.2
|    |    +--- org.springframework.boot:spring-boot-starter:3.2.4 -> 3.3.6 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    \--- org.springframework.security:spring-security-rsa:1.1.2
|    |         \--- org.bouncycastle:bcprov-jdk18on:1.77 -> 1.78.1
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:4.2.0 -> 4.1.1
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.2.4 -> 3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.4 -> 3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    \--- org.aspectj:aspectjweaver:********
|    |    +--- io.github.openfeign.form:feign-form-spring:3.8.0
|    |    |    +--- io.github.openfeign.form:feign-form:3.8.0
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    +--- org.springframework:spring-web:5.1.5.RELEASE -> 6.1.15 (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    \--- commons-fileupload:commons-fileupload:1.5 (*)
|    +--- org.springframework:spring-web:6.2.0 -> 6.1.15 (*)
|    +--- org.springframework.cloud:spring-cloud-commons:4.2.0 -> 4.1.2 (*)
|    +--- io.github.openfeign:feign-core:13.5 -> 13.2.1
|    \--- io.github.openfeign:feign-slf4j:13.5 -> 13.2.1
|         +--- io.github.openfeign:feign-core:13.2.1
|         \--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
+--- org.springframework.retry:spring-retry -> 2.0.10
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2
|    +--- org.springframework.cloud:spring-cloud-starter:4.1.2 (*)
|    +--- org.springframework.cloud:spring-cloud-loadbalancer:4.1.2
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    +--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12
|    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    \--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2
|    |         \--- io.projectreactor:reactor-core:3.5.20 -> 3.6.12 (*)
|    +--- org.springframework.boot:spring-boot-starter-cache:3.2.4 -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    \--- org.springframework:spring-context-support:6.1.15
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    \--- com.stoyanr:evictor:1.0.0
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2
|    +--- org.springframework.boot:spring-boot-starter-aop:3.2.9 -> 3.3.6 (*)
|    \--- org.apache.seata:seata-spring-boot-starter:2.1.0 -> 2.3.0
|         +--- org.apache.seata:seata-spring-autoconfigure-client:2.3.0
|         |    \--- org.apache.seata:seata-spring-autoconfigure-core:2.3.0
|         \--- org.apache.seata:seata-all:2.3.0
|              +--- org.springframework:spring-context:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-core:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-beans:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-aop:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-webmvc:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-tx:5.3.39 -> 6.1.15 (*)
|              +--- io.netty:netty-all:4.1.101.Final -> 4.1.115.Final
|              |    +--- io.netty:netty-buffer:4.1.115.Final
|              |    |    \--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-codec:4.1.115.Final
|              |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    \--- io.netty:netty-transport:4.1.115.Final
|              |    |         +--- io.netty:netty-common:4.1.115.Final
|              |    |         +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |         \--- io.netty:netty-resolver:4.1.115.Final
|              |    |              \--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-codec-dns:4.1.115.Final
|              |    +--- io.netty:netty-codec-haproxy:4.1.115.Final
|              |    +--- io.netty:netty-codec-http:4.1.115.Final
|              |    +--- io.netty:netty-codec-http2:4.1.115.Final
|              |    +--- io.netty:netty-codec-memcache:4.1.115.Final
|              |    +--- io.netty:netty-codec-mqtt:4.1.115.Final
|              |    +--- io.netty:netty-codec-redis:4.1.115.Final
|              |    +--- io.netty:netty-codec-smtp:4.1.115.Final
|              |    +--- io.netty:netty-codec-socks:4.1.115.Final
|              |    +--- io.netty:netty-codec-stomp:4.1.115.Final
|              |    +--- io.netty:netty-codec-xml:4.1.115.Final
|              |    +--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-handler:4.1.115.Final
|              |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final
|              |    |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    |    \--- io.netty:netty-transport:4.1.115.Final (*)
|              |    |    \--- io.netty:netty-codec:4.1.115.Final (*)
|              |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|              |    +--- io.netty:netty-handler-proxy:4.1.115.Final
|              |    +--- io.netty:netty-handler-ssl-ocsp:4.1.115.Final
|              |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|              |    +--- io.netty:netty-resolver-dns:4.1.115.Final
|              |    +--- io.netty:netty-transport:4.1.115.Final (*)
|              |    +--- io.netty:netty-transport-rxtx:4.1.115.Final
|              |    +--- io.netty:netty-transport-sctp:4.1.115.Final
|              |    +--- io.netty:netty-transport-udt:4.1.115.Final
|              |    +--- io.netty:netty-transport-classes-epoll:4.1.115.Final
|              |    +--- io.netty:netty-transport-classes-kqueue:4.1.115.Final
|              |    \--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final
|              +--- org.antlr:antlr4:4.8
|              |    +--- org.antlr:antlr4-runtime:4.8
|              |    +--- org.antlr:antlr-runtime:3.5.2
|              |    +--- org.antlr:ST4:4.3
|              |    |    \--- org.antlr:antlr-runtime:3.5.2
|              |    +--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
|              |    +--- org.glassfish:javax.json:1.0.4
|              |    \--- com.ibm.icu:icu4j:61.1
|              +--- com.alibaba:fastjson:1.2.83
|              +--- com.alibaba:druid:1.2.20 -> 1.2.22
|              +--- com.typesafe:config:1.2.1
|              +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|              +--- commons-lang:commons-lang:2.6
|              +--- org.apache.commons:commons-pool2:2.11.1 -> 2.12.0
|              +--- commons-pool:commons-pool:1.6
|              +--- aopalliance:aopalliance:1.0
|              +--- com.google.guava:guava:32.1.3-jre -> 20.0
|              +--- com.github.ben-manes.caffeine:caffeine:2.9.3 -> 3.1.8
|              |    +--- org.checkerframework:checker-qual:3.37.0
|              |    \--- com.google.errorprone:error_prone_annotations:2.21.1
|              \--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
+--- org.apache.seata:seata-all -> 2.3.0 (*)
+--- org.apache.dubbo.extensions:dubbo-filter-seata -> 3.3.1
+--- com.taobao.arthas:arthas-spring-boot-starter -> 4.0.4
|    +--- com.taobao.arthas:arthas-agent-attach:4.0.4
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.11 -> 1.14.19
|    |    \--- org.zeroturnaround:zt-zip:1.16
|    |         \--- org.slf4j:slf4j-api:1.6.6 -> 2.0.16
|    \--- com.taobao.arthas:arthas-packaging:4.0.4
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0
|    +--- com.alibaba.csp:sentinel-transport-simple-http:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-transport-common:1.8.8
|    |         +--- com.alibaba.csp:sentinel-datasource-extension:1.8.8
|    |         |    \--- com.alibaba.csp:sentinel-core:1.8.8
|    |         \--- com.alibaba:fastjson:1.2.83_noneautotype -> 1.2.83
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- org.aspectj:aspectjweaver:1.9.2 -> ********
|    +--- com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2023.0.1.0
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6
|    |         \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    +--- com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6 (*)
|    +--- com.alibaba.csp:sentinel-spring-webmvc-6x-adapter:1.8.6
|    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-cluster-server-default:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6
|    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6 (*)
|    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    +--- com.alibaba.csp:sentinel-cluster-client-default:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6 (*)
|    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2023.0.1.0
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
\--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7
     +--- com.alibaba.csp:sentinel-datasource-extension:1.8.7 -> 1.8.8 (*)
     \--- com.alibaba.nacos:nacos-client:1.4.2 -> 2.4.3
          +--- com.alibaba.nacos:nacos-auth-plugin:2.4.3
          +--- com.alibaba.nacos:nacos-encryption-plugin:2.4.3
          +--- com.alibaba.nacos:nacos-logback-adapter-12:2.4.3
          +--- com.alibaba.nacos:logback-adapter:1.1.3
          +--- com.alibaba.nacos:nacos-log4j2-adapter:2.4.3
          +--- commons-codec:commons-codec:1.15 -> 1.16.1
          +--- com.fasterxml.jackson.core:jackson-core:2.13.5 -> 2.17.3 (*)
          +--- com.fasterxml.jackson.core:jackson-databind:2.13.5 -> 2.17.3 (*)
          +--- org.apache.httpcomponents:httpasyncclient:4.1.5
          |    +--- org.apache.httpcomponents:httpcore:4.4.15 -> 4.4.16
          |    +--- org.apache.httpcomponents:httpcore-nio:4.4.15 -> 4.4.16
          |    |    \--- org.apache.httpcomponents:httpcore:4.4.16
          |    \--- org.apache.httpcomponents:httpclient:4.5.13
          |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
          |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
          +--- org.apache.httpcomponents:httpcore:4.4.16
          +--- io.prometheus:simpleclient:0.15.0 -> 0.16.0
          |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0
          |    |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
          |    \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
          |         \--- io.prometheus:simpleclient_tracer_common:0.16.0
          +--- org.yaml:snakeyaml:2.0 -> 2.2
          \--- io.micrometer:micrometer-core:1.9.17 -> 1.13.8 (*)

compileOnly - Compile-only dependencies for the 'main' feature. (n)
No dependencies

default - Configuration for default artifacts. (n)
No dependencies

developmentOnly - Configuration for development-only dependencies such as Spring Boot's DevTools.
No dependencies

implementation - Implementation dependencies for the 'main' feature. (n)
+--- project camunda-server-nacos-support (n)
+--- project camunda-pulsar-connector-plugin (n)
+--- project camunda-bpm-service (n)
+--- org.springframework.boot:spring-boot-starter-web (n)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter (n)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest (n)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp (n)
+--- org.springframework.boot:spring-boot-starter-jdbc (n)
+--- org.postgresql:postgresql (n)
+--- org.springframework.boot:spring-boot-starter-actuator (n)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring (n)
+--- org.springframework.cloud:spring-cloud-starter-openfeign (n)
+--- org.springframework.retry:spring-retry (n)
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer (n)
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata (n)
+--- org.apache.seata:seata-all (n)
+--- org.apache.dubbo.extensions:dubbo-filter-seata (n)
+--- com.taobao.arthas:arthas-spring-boot-starter (n)
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel (n)
+--- com.alibaba.csp:sentinel-datasource-nacos (n)
+--- org.codehaus.groovy:groovy:3.0.19 (n)
+--- org.codehaus.groovy:groovy-xml:3.0.19 (n)
+--- org.codehaus.groovy:groovy-jsr223:3.0.19 (n)
+--- org.codehaus.groovy:groovy-json:3.0.19 (n)
+--- org.codehaus.groovy:groovy-templates:3.0.19 (n)
+--- org.codehaus.groovy:groovy-nio:3.0.19 (n)
+--- org.codehaus.groovy:groovy-swing:3.0.19 (n)
+--- org.codehaus.groovy:groovy-console:3.0.19 (n)
+--- org.codehaus.groovy:groovy-groovydoc:3.0.19 (n)
+--- org.codehaus.groovy:groovy-docgenerator:3.0.19 (n)
+--- org.codehaus.groovy:groovy-cli-picocli:3.0.19 (n)
+--- org.codehaus.groovy:groovy-ant:3.0.19 (n)
+--- org.codehaus.groovy:groovy-astbuilder:3.0.19 (n)
+--- org.codehaus.groovy:groovy-datetime:3.0.19 (n)
+--- org.codehaus.groovy:groovy-groovysh:3.0.19 (n)
+--- org.codehaus.groovy:groovy-jmx:3.0.19 (n)
+--- org.codehaus.groovy:groovy-macro:3.0.19 (n)
+--- org.codehaus.groovy:groovy-servlet:3.0.19 (n)
+--- org.codehaus.groovy:groovy-sql:3.0.19 (n)
+--- org.codehaus.groovy:groovy-test:3.0.19 (n)
+--- org.codehaus.groovy:groovy-test-junit5:3.0.19 (n)
\--- org.codehaus.groovy:groovy-testng:3.0.19 (n)

mainSourceElements - List of source directories contained in the Main SourceSet. (n)
No dependencies

productionRuntimeClasspath
+--- project :camunda-server-nacos-support
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-auth-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-encryption-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-logback-adapter-12:2.4.3
|    |    |    +--- com.alibaba.nacos:logback-adapter:1.1.3
|    |    |    +--- com.alibaba.nacos:nacos-log4j2-adapter:2.4.3
|    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.13.5 -> 2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.13.5 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.15 -> 4.4.16
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.15 -> 4.4.16
|    |    |    |    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13
|    |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
|    |    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |    |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    +--- io.prometheus:simpleclient:0.15.0 -> 0.16.0
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |    |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |    \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    +--- org.yaml:snakeyaml:2.0 -> 2.2
|    |    |    \--- io.micrometer:micrometer-core:1.9.17 -> 1.13.8
|    |    |         +--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- io.micrometer:micrometer-observation:1.13.8
|    |    |         |    \--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- org.hdrhistogram:HdrHistogram:2.2.2
|    |    |         \--- org.latencyutils:LatencyUtils:2.0.3
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    \--- org.springframework.cloud:spring-cloud-context:4.1.2
|    |         \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3 (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    +--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    \--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    +--- org.springframework.boot:spring-boot-starter -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot:3.3.6
|    |    |    +--- org.springframework:spring-core:6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.6
|    |    |    +--- ch.qos.logback:logback-classic:1.5.12
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.12
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0
|    |    +--- org.camunda.bpm:camunda-engine-spring-6:7.23.0
|    |    |    \--- org.camunda.bpm:camunda-engine:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-bpmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-cmmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.bpm.dmn:camunda-engine-dmn:7.23.0
|    |    |         |    +--- org.camunda.commons:camunda-commons-utils:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0
|    |    |         |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.bpm.model:camunda-dmn-model:7.23.0
|    |    |         |    |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-juel:7.23.0
|    |    |         |    |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-scala:7.23.0
|    |    |         |    |    +--- org.camunda.feel:feel-engine:1.19.1
|    |    |         |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |         |    |    |    \--- com.fasterxml.uuid:java-uuid-generator:5.1.0
|    |    |         |    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |         |    |    \--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    +--- org.camunda.feel:feel-engine:1.19.1 (*)
|    |    |         |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         +--- org.mybatis:mybatis:3.5.15
|    |    |         +--- org.springframework:spring-beans:6.2.4 -> 6.1.15 (*)
|    |    |         \--- joda-time:joda-time:2.12.5 -> 2.12.7
|    |    +--- org.springframework:spring-context:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-jdbc:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    +--- org.springframework:spring-tx:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-orm:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.4.4 -> 3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter:3.4.4 -> 3.3.6 (*)
|    |    +--- org.apache.commons:commons-lang3:3.17.0 -> 3.14.0
|    |    +--- com.fasterxml.uuid:java-uuid-generator:4.3.0 -> 5.1.0 (*)
|    |    \--- com.sun.xml.bind:jaxb-impl:4.0.5
|    |         \--- com.sun.xml.bind:jaxb-core:4.0.5
|    |              +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |              |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    |              \--- org.eclipse.angus:angus-activation:2.0.2
|    |                   \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- org.camunda.bpm:camunda-engine -> 7.23.0 (*)
|    +--- com.alibaba.nacos:logback-adapter -> 1.1.3
|    \--- org.slf4j:slf4j-api -> 2.0.16
+--- project :camunda-pulsar-connector-plugin
|    \--- org.springframework.boot:spring-boot-starter-web -> 3.3.6
|         +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-json:3.3.6
|         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         |    +--- org.springframework:spring-web:6.1.15
|         |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|         |    |    +--- org.springframework:spring-core:6.1.15 (*)
|         |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|         |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3
|         |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |         \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6
|         |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|         |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.33
|         |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         +--- org.springframework:spring-web:6.1.15 (*)
|         \--- org.springframework:spring-webmvc:6.1.15
|              +--- org.springframework:spring-aop:6.1.15 (*)
|              +--- org.springframework:spring-beans:6.1.15 (*)
|              +--- org.springframework:spring-context:6.1.15 (*)
|              +--- org.springframework:spring-core:6.1.15 (*)
|              +--- org.springframework:spring-expression:6.1.15 (*)
|              \--- org.springframework:spring-web:6.1.15 (*)
+--- project :camunda-bpm-service
|    +--- project :camunda-bpm-api
|    |    +--- com.open-care:api-jsonschema-definition -> 3.4.7
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.1.1 -> 4.2.0
|    |    |    |    +--- org.springframework.cloud:spring-cloud-starter:4.2.0 -> 4.1.2
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    |    |    \--- org.springframework.security:spring-security-rsa:1.1.2
|    |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.77 -> 1.78.1
|    |    |    |    +--- org.springframework.cloud:spring-cloud-openfeign-core:4.2.0 -> 4.1.1
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.4 -> 3.3.6
|    |    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    |    \--- org.aspectj:aspectjweaver:********
|    |    |    |    |    +--- io.github.openfeign.form:feign-form-spring:3.8.0
|    |    |    |    |    |    +--- io.github.openfeign.form:feign-form:3.8.0
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    |    +--- org.springframework:spring-web:5.1.5.RELEASE -> 6.1.15 (*)
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    \--- commons-fileupload:commons-fileupload:1.5
|    |    |    |    |         \--- commons-io:commons-io:2.11.0 -> 2.16.1
|    |    |    |    +--- org.springframework:spring-web:6.2.0 -> 6.1.15 (*)
|    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.2.0 -> 4.1.2 (*)
|    |    |    |    +--- io.github.openfeign:feign-core:13.5 -> 13.2.1
|    |    |    |    \--- io.github.openfeign:feign-slf4j:13.5 -> 13.2.1
|    |    |    |         +--- io.github.openfeign:feign-core:13.2.1
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    |    +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-revised:1.3120.20220318T16
|    |    |    |    |    |    \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    \--- com.open-care:method-name-getter:1.3120.20220318T16
|    |    |    |    \--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16
|    |    |    |         \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT
|    |    |    |         |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         |    \--- com.google.code.gson:gson:2.8.5 -> 2.10.1
|    |    |    |         +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |         +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    \--- com.open-care:api-jsonschema-definition-common:3.4.7
|    |    |         +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |         \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- cn.hutool:hutool-all -> 5.8.27
|    |    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
|    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2
|    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-core:2.1.2
|    |    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-eventbus:2.1.2
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.1 -> 1.0.4
|    |    |    |    +--- org.eclipse.collections:eclipse-collections:9.0.0
|    |    |    |    |    \--- org.eclipse.collections:eclipse-collections-api:9.0.0
|    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.8.10 -> 2.17.3 (*)
|    |    |    |    +--- com.lmax:disruptor:3.3.7
|    |    |    |    +--- io.vavr:vavr:0.9.2
|    |    |    |    |    \--- io.vavr:vavr-match:0.9.2
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    +--- org.camunda.bpm:camunda-engine-plugin-spin -> 7.23.0
|    |    \--- org.camunda.spin:camunda-spin-core:7.23.0
|    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         \--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    +--- org.camunda.spin:camunda-spin-dataformat-json-jackson -> 7.23.0
|    |    +--- org.camunda.spin:camunda-spin-core:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.15.2 -> 2.17.3 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    \--- com.jayway.jsonpath:json-path:2.9.0
|    |         +--- net.minidev:json-smart:2.5.0 -> 2.5.1
|    |         |    \--- net.minidev:accessors-smart:2.5.1
|    |         \--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0
|    |    |    +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |    |    +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6
|    |         +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-validation:3.3.6
|    |         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|    |         |    \--- org.hibernate.validator:hibernate-validator:8.0.1.Final
|    |         |         +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |         +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |         |         \--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |         +--- org.springframework:spring-web:6.1.15 (*)
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- org.glassfish.hk2:osgi-resource-locator:1.0.3
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.9
|    |         |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.9
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |         |    +--- org.jboss.logging:jboss-logging:3.6.0.Final -> 3.5.3.Final
|    |         |    +--- org.glassfish.expressly:expressly:5.0.0
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.ext:jersey-spring6:3.1.9
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6
|    |         |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    +--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6
|    |         |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6
|    |         |    |    |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    \--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.hk2:hk2:3.0.6
|    |         |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-core:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-runlevel:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    \--- org.glassfish.hk2:class-model:3.0.6
|    |         |    |         \--- org.ow2.asm:asm-commons:9.6
|    |         |    +--- org.glassfish.hk2:spring-bridge:3.0.6
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         \--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.9
|    |              +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |              +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.9
|    |              |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |              +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (*)
|    |              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta -> 7.23.0 (*)
|    +--- org.camunda.bpm:camunda-engine-rest:7.23.0
|    |    \--- org.camunda.bpm:camunda-engine-rest-core:7.23.0
|    |         +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |         +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.15.2 -> 2.17.3
|    |         |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |         \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    +--- jakarta.ws.rs:jakarta.ws.rs-api -> 3.1.0
|    +--- org.springframework.boot:spring-boot-starter-data-jpa -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- com.zaxxer:HikariCP:5.1.0
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    \--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    +--- org.hibernate.orm:hibernate-core:6.5.3.Final
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final
|    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final
|    |    |    +--- io.smallrye:jandex:3.1.2
|    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (*)
|    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5
|    |    |    |    \--- org.glassfish.jaxb:jaxb-core:4.0.5
|    |    |    |         +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    |    |    |         +--- jakarta.activation:jakarta.activation-api:2.1.3
|    |    |    |         +--- org.eclipse.angus:angus-activation:2.0.2 (*)
|    |    |    |         +--- org.glassfish.jaxb:txw2:4.0.5
|    |    |    |         \--- com.sun.istack:istack-commons-runtime:4.1.2
|    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |    |    \--- org.antlr:antlr4-runtime:4.13.0
|    |    +--- org.springframework.data:spring-data-jpa:3.3.6
|    |    |    +--- org.springframework.data:spring-data-commons:3.3.6
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |    +--- org.springframework:spring-orm:6.1.15 (*)
|    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.antlr:antlr4-runtime:4.13.0
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.0.0 -> 2.1.1
|    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    \--- org.springframework:spring-aspects:6.1.15
|    |         \--- org.aspectj:aspectjweaver:********
|    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2
|    |    +--- org.springframework.cloud:spring-cloud-starter:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:4.1.2
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    +--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2
|    |    |         \--- io.projectreactor:reactor-core:3.5.20 -> 3.6.12 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:3.2.4 -> 3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    \--- org.springframework:spring-context-support:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    +--- org.springframework.boot:spring-boot-configuration-processor -> 3.3.6
|    +--- org.springframework.retry:spring-retry -> 2.0.10
|    +--- jakarta.persistence:jakarta.persistence-api -> 3.1.0
|    +--- jakarta.servlet:jakarta.servlet-api -> 6.0.0
|    +--- jakarta.annotation:jakarta.annotation-api -> 2.1.1
|    +--- jakarta.validation:jakarta.validation-api -> 3.0.2
|    +--- org.hibernate:hibernate-core -> 5.6.15.Final
|    |    +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |    +--- javax.persistence:javax.persistence-api:2.2
|    |    +--- net.bytebuddy:byte-buddy:1.12.18 -> 1.14.19
|    |    +--- antlr:antlr:2.7.7
|    |    +--- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:1.1.1.Final
|    |    +--- org.jboss:jandex:2.4.2.Final
|    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    +--- javax.activation:javax.activation-api:1.2.0
|    |    +--- org.hibernate.common:hibernate-commons-annotations:5.1.2.Final -> 6.0.6.Final
|    |    +--- javax.xml.bind:jaxb-api:2.3.1 -> 2.2.7
|    |    \--- org.glassfish.jaxb:jaxb-runtime:2.3.1 -> 4.0.5 (*)
|    +--- org.dom4j:dom4j -> 2.1.4
|    |    +--- jaxen:jaxen:1.1.6 -> 2.0.0
|    |    +--- javax.xml.stream:stax-api:1.0-2
|    |    +--- net.java.dev.msv:xsdlib:2013.6.1
|    |    |    \--- relaxngDatatype:relaxngDatatype:20020414
|    |    +--- javax.xml.bind:jaxb-api:2.2.12 -> 2.2.7
|    |    +--- pull-parser:pull-parser:2.1.10
|    |    \--- xpp3:xpp3:1.1.4c
|    +--- com.open-care:open-care-json-converter -> 1.286.20240806-SNAPSHOT-JSOG (*)
|    +--- com.open-care:api-jsonschema-definition -> 3.4.7 (*)
|    +--- com.open-care:open-care-core-util -> 2.7
|    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    +--- com.open-care:open-care-common-util -> 3.5-SNAPSHOT
|    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    +--- com.open-care:open-care-common-util-core:3.5-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- com.open-care:api-jsonschema-definition-common:3.4.7 (*)
|    |    |    +--- com.open-care:open-care-core-component:2.7
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    |    \--- org.projectlombok:lombok:1.18.34 -> 1.18.36
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    \--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    +--- com.open-care:open-care-core-component:2.7 (*)
|    |    +--- com.open-care:open-care-core-util:2.7 (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.2.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.5
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |    |    |    \--- io.micrometer:micrometer-observation:1.12.13 -> 1.13.8 (*)
|    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-web:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |         \--- org.springframework:spring-web:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    \--- org.springframework:spring-web:6.1.15 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4
|    |    |              \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- com.nimbusds:oauth2-oidc-sdk:9.43.4
|    |    |    |         +--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    |    |         +--- com.nimbusds:content-type:2.2
|    |    |    |         +--- net.minidev:json-smart:[1.3.3,2.4.10] -> 2.5.1 (*)
|    |    |    |         +--- com.nimbusds:lang-tag:1.7
|    |    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE
|    |    |    |    +--- io.netty:netty-common:4.1.107.Final -> 4.1.115.Final
|    |    |    |    +--- io.netty:netty-handler:4.1.107.Final -> 4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-transport:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-resolver:4.1.115.Final (*)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    |    \--- io.netty:netty-codec:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |         \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.107.Final -> 4.1.115.Final (*)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12 (*)
|    |    |    \--- org.springframework.data:spring-data-redis:3.3.6
|    |    |         +--- org.springframework.data:spring-data-keyvalue:3.3.6
|    |    |         |    +--- org.springframework.data:spring-data-commons:3.3.6 (*)
|    |    |         |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |         +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         +--- org.springframework:spring-oxm:6.1.15
|    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
|    |    |         |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context-support:6.1.15 (*)
|    |    |         \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:4.1.2 (*)
|    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    +--- com.googlecode.aviator:aviator:4.2.0
|    |    |    \--- commons-beanutils:commons-beanutils:1.9.3
|    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |         \--- commons-collections:commons-collections:3.2.2
|    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    +--- io.projectreactor.addons:reactor-extra:3.4.6 -> 3.5.2 (*)
|    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    +--- org.apache.commons:commons-collections4:4.5.0-M1
|    |    +--- commons-beanutils:commons-beanutils:1.9.3 (*)
|    |    +--- org.apache.commons:commons-pool2:2.12.0
|    |    +--- commons-dbutils:commons-dbutils:1.7
|    |    +--- commons-io:commons-io:2.16.1
|    |    +--- commons-lang:commons-lang:2.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 (*)
|    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.33
|    |    |    \--- org.apache.tomcat:tomcat-juli:10.1.33
|    |    +--- org.apache.poi:poi:4.1.0
|    |    |    +--- commons-codec:commons-codec:1.12 -> 1.16.1
|    |    |    +--- org.apache.commons:commons-collections4:4.3 -> 4.5.0-M1
|    |    |    \--- org.apache.commons:commons-math3:3.6.1
|    |    +--- org.apache.poi:poi-ooxml:4.1.2
|    |    |    +--- org.apache.poi:poi:4.1.2 -> 4.1.0 (*)
|    |    |    +--- org.apache.poi:poi-ooxml-schemas:4.1.2
|    |    |    |    \--- org.apache.xmlbeans:xmlbeans:3.1.0
|    |    |    +--- org.apache.commons:commons-compress:1.19 -> 1.27.1
|    |    |    |    +--- commons-codec:commons-codec:1.17.1 -> 1.16.1
|    |    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    |    \--- org.apache.commons:commons-lang3:3.16.0 -> 3.14.0
|    |    |    \--- com.github.virtuald:curvesapi:1.06
|    |    +--- org.apache.pdfbox:pdfbox:3.0.2
|    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    +--- org.apache.pdfbox:fontbox:3.0.2
|    |    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2 (*)
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG
|    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT (*)
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    +--- org.hibernate.orm:hibernate-core:6.4.4.Final -> 6.5.3.Final (*)
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.2.0 -> 3.3.2
|    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-core:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    +--- com.fasterxml:classmate:1.7.0 (c)
|    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    +--- com.zaxxer:HikariCP:5.1.0 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.3.Final (c)
|    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    +--- com.open-care:api-jsonschema-definition:3.4.7 (*)
|    |    +--- com.github.behaim:behaim:2.0.20240807
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- ch.qos.logback:logback-classic:0.9.21 -> 1.5.12 (*)
|    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    +--- com.google.guava:guava:31.1-jre -> 20.0
|    |    |    \--- com.open-care:open-care-generics-utils:1.0.20230118T16
|    |    |         +--- org.slf4j:slf4j-api:1.7.13 -> 2.0.16
|    |    |         \--- com.googlecode.gentyref:gentyref:1.2.0
|    |    +--- com.github.behaim:behaim_with-hibernate-proxy:2.0.20240807
|    |    |    +--- org.hibernate:hibernate-core:6.1.7.Final -> 5.6.15.Final (*)
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.github.behaim:behaim_target-value-converters-for-date-type:2.0.20240807
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3
|    |    |    |    |    +--- com.graphql-java:java-dataloader:3.3.0
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
|    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3 -> 1.0.4
|    |    |    |    \--- com.graphql-java:graphql-java-extended-scalars:22.0
|    |    |    |         \--- com.graphql-java:graphql-java:22.0 -> 22.3 (*)
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (*)
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (*)
|    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    +--- com.graphql-java:graphql-java:22.3 (*)
|    |    +--- org.atteo:evo-inflector:1.2.2
|    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    +--- com.open-care:graphql-jpa-mutation-autoconfigure:1.2.8.20240819T17
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |    |    |    \--- org.springframework.graphql:spring-graphql:1.3.3
|    |    |    |         +--- io.micrometer:context-propagation:1.1.2
|    |    |    |         +--- com.graphql-java:graphql-java:22.3 (*)
|    |    |    |         +--- io.projectreactor:reactor-core:3.6.11 -> 3.6.12 (*)
|    |    |    |         \--- org.springframework:spring-context:6.1.14 -> 6.1.15 (*)
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10
|    |    |    |    +--- org.hibernate:hibernate-core:6.4.4.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    \--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10
|    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |         \--- org.jgrapht:jgrapht-core:1.3.0
|    |    |    |              \--- org.jheaps:jheaps:0.9
|    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807 (*)
|    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    +--- com.open-care:interceptor-for-code-manipulation-on-graphql-jpa-query-schema:1.2.8.20240819T17
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |    \--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17
|    |    |    |    |         \--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    \--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (*)
|    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- joda-time:joda-time:2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0
|    |    |    |    |    \--- jakarta.annotation:jakarta.annotation-api:3.0.0 -> 2.1.1
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    |    \--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- com.open-care:graphql-jpa-query-schema-code-manipulated:1.2.8.20240819T17
|    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    +--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final (c)
|    |    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    |    +--- org.antlr:antlr4:4.13.0 (c)
|    |    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- io.smallrye:jandex:3.1.2 (c)
|    |    |    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (c)
|    |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    |    +--- com.zaxxer:HikariCP:3.2.0 -> 5.1.0 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-core:1.10.4 -> 1.13.8 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (*)
|    |    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17
|    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (*)
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:gson-jodatime-serialisers:1.6.0-SNAPSHOT (*)
|    |    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-query-transformer-with-supporting-managedtype:1.2.8.20240819T17
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |    \--- com.open-care:graphql-query-transformer:1.2.8.20240819T17
|    |    |    |    |         +--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10 (*)
|    |    |    |    |         +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |         +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    |    |    |         +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |         +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |         +--- com.open-care:open-care-generics-utils:1.0.20230118T16 (*)
|    |    |    |    |         +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |         \--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17 (*)
|    |    |    |    \--- com.open-care:graphql-query-result-general-model-common:1.2.8.20240819T17
|    |    |    \--- com.open-care:graphql-jpa-query-autoconfigure-code-manipulated:1.2.8.20240819T17
|    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17 (*)
|    |    +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    +--- org.crazycake:camel-name-utils:1.0.0-RELEASE
|    |    +--- com.alibaba:druid:1.2.22
|    |    +--- com.open-care:kingbase-hibernate-dialect:6.2
|    |    +--- io.github.kostaskougios:cloning:1.10.3
|    |    |    \--- org.objenesis:objenesis:3.0.1
|    |    +--- org.reflections:reflections:0.10.2
|    |    |    +--- org.javassist:javassist:3.28.0-GA -> 3.30.2-GA
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    \--- org.slf4j:slf4j-api:1.7.32 -> 2.0.16
|    |    +--- com.github.jsqlparser:jsqlparser:1.2
|    |    +--- com.mysql:mysql-connector-j:8.3.0
|    |    +--- org.postgresql:postgresql:42.7.4
|    |    |    \--- org.checkerframework:checker-qual:3.42.0
|    |    +--- org.yaml:snakeyaml:1.26 -> 2.2
|    |    +--- com.ibm.icu:icu4j:71.1 -> 72.1
|    |    +--- com.google.code.findbugs:annotations:3.0.1
|    |    |    +--- net.jcip:jcip-annotations:1.0
|    |    |    \--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    +--- com.open-care:auth-core:1.0-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- org.bouncycastle:bcprov-jdk15to18:1.78.1
|    |    |    +--- cn.dev33:sa-token-spring-boot3-starter:1.40.0
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.0.1 -> 3.3.6 (*)
|    |    |    |    +--- cn.dev33:sa-token-jakarta-servlet:1.40.0
|    |    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    |    \--- jakarta.servlet:jakarta.servlet-api:6.0.0
|    |    |    |    \--- cn.dev33:sa-token-spring-boot-autoconfig:1.40.0
|    |    |    +--- cn.dev33:sa-token-spring-aop:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- org.springframework.boot:spring-boot-starter-aop:2.5.15 -> 3.3.6 (*)
|    |    |    +--- cn.dev33:sa-token-sso:1.40.0
|    |    |    |    \--- cn.dev33:sa-token-core:1.40.0
|    |    |    +--- cn.dev33:sa-token-jwt:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- cn.hutool:hutool-jwt:5.8.20
|    |    |    |         +--- cn.hutool:hutool-json:5.8.20
|    |    |    |         |    \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    |         \--- cn.hutool:hutool-crypto:5.8.20
|    |    |    |              \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    |    \--- com.open-care:rpc-core:3.3.2-SNAPSHOT
|    |    |         \--- org.mapstruct:mapstruct:1.6.3
|    |    +--- cn.hutool:hutool-all:5.8.27
|    |    \--- joda-time:joda-time:2.12.7
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0
|    |    +--- com.alibaba.csp:sentinel-transport-simple-http:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-transport-common:1.8.8
|    |    |         +--- com.alibaba.csp:sentinel-datasource-extension:1.8.8
|    |    |         |    \--- com.alibaba.csp:sentinel-core:1.8.8
|    |    |         \--- com.alibaba:fastjson:1.2.83_noneautotype -> 1.2.83
|    |    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2 -> ********
|    |    +--- com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2023.0.1.0
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6
|    |    |         \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6 (*)
|    |    +--- com.alibaba.csp:sentinel-spring-webmvc-6x-adapter:1.8.6
|    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    |    +--- com.alibaba.csp:sentinel-cluster-server-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6
|    |    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    +--- com.alibaba.csp:sentinel-cluster-client-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2023.0.1.0
|    |         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    +--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7
|    |    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.7 -> 1.8.8 (*)
|    |    \--- com.alibaba.nacos:nacos-client:1.4.2 -> 2.4.3 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.9 -> 3.3.6 (*)
|    |    \--- org.apache.seata:seata-spring-boot-starter:2.1.0 -> 2.3.0
|    |         +--- org.apache.seata:seata-spring-autoconfigure-client:2.3.0
|    |         |    \--- org.apache.seata:seata-spring-autoconfigure-core:2.3.0
|    |         \--- org.apache.seata:seata-all:2.3.0
|    |              +--- org.springframework:spring-context:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-core:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-beans:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-aop:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-webmvc:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-tx:5.3.39 -> 6.1.15 (*)
|    |              +--- io.netty:netty-all:4.1.101.Final -> 4.1.115.Final
|    |              |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-haproxy:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http2:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-memcache:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-mqtt:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-redis:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-smtp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-socks:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-stomp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-xml:4.1.115.Final
|    |              |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    +--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-handler-proxy:4.1.115.Final
|    |              |    +--- io.netty:netty-handler-ssl-ocsp:4.1.115.Final
|    |              |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-rxtx:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-sctp:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-udt:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-classes-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-classes-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-resolver-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-epoll:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-kqueue:4.1.115.Final (*)
|    |              |    \--- io.netty:netty-resolver-dns-native-macos:4.1.115.Final
|    |              |         \--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final (*)
|    |              +--- org.antlr:antlr4:4.8 -> 4.13.0
|    |              |    +--- org.antlr:antlr4-runtime:4.13.0
|    |              |    +--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.antlr:ST4:4.3.4
|    |              |    |    \--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
|    |              |    \--- com.ibm.icu:icu4j:72.1
|    |              +--- com.alibaba:fastjson:1.2.83
|    |              +--- com.alibaba:druid:1.2.20 -> 1.2.22
|    |              +--- com.typesafe:config:1.2.1
|    |              +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |              +--- commons-lang:commons-lang:2.6
|    |              +--- org.apache.commons:commons-pool2:2.11.1 -> 2.12.0
|    |              +--- commons-pool:commons-pool:1.6
|    |              +--- org.apache.dubbo.extensions:dubbo-filter-seata:1.0.2 -> 3.3.1
|    |              +--- aopalliance:aopalliance:1.0
|    |              +--- com.google.guava:guava:32.1.3-jre -> 20.0
|    |              +--- com.github.ben-manes.caffeine:caffeine:2.9.3 -> 3.1.8
|    |              |    +--- org.checkerframework:checker-qual:3.37.0 -> 3.42.0
|    |              |    \--- com.google.errorprone:error_prone_annotations:2.21.1
|    |              \--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    +--- com.nimbusds:nimbus-jose-jwt -> 9.24.4 (*)
|    +--- net.logstash.logback:logstash-logback-encoder -> 5.1
|    |    +--- ch.qos.logback:logback-core:1.2.3 -> 1.5.12
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.5 -> 2.17.3 (*)
|    +--- org.reflections:reflections -> 0.10.2 (*)
|    +--- uk.com.robust-it:cloning -> 1.9.2
|    |    \--- org.objenesis:objenesis:2.1 -> 3.0.1
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.3 (*)
|    +--- com.fasterxml.jackson.core:jackson-core -> 2.17.3 (*)
|    +--- com.google.code.gson:gson -> 2.10.1
|    +--- cn.hutool:hutool-all -> 5.8.27
|    +--- com.belerweb:pinyin4j -> 2.5.1
|    \--- org.mapstruct:mapstruct -> 1.6.3
+--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp-core:7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0 (*)
|    |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.4.4 -> 3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6 (*)
|    \--- org.camunda.bpm.webapp:camunda-webapp-webjar:7.23.0
|         \--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 3.3.6 (*)
+--- org.postgresql:postgresql -> 42.7.4 (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.6
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (*)
|    |    +--- org.springframework.boot:spring-boot-actuator:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
|    +--- io.micrometer:micrometer-observation:1.13.8 (*)
|    \--- io.micrometer:micrometer-jakarta9:1.13.8
|         +--- io.micrometer:micrometer-core:1.13.8 (*)
|         +--- io.micrometer:micrometer-commons:1.13.8
|         \--- io.micrometer:micrometer-observation:1.13.8 (*)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2 (*)
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
+--- org.springframework.retry:spring-retry -> 2.0.10
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2 (*)
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2 (*)
+--- org.apache.seata:seata-all -> 2.3.0 (*)
+--- org.apache.dubbo.extensions:dubbo-filter-seata -> 3.3.1
+--- com.taobao.arthas:arthas-spring-boot-starter -> 4.0.4
|    +--- com.taobao.arthas:arthas-agent-attach:4.0.4
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.11 -> 1.14.19
|    |    \--- org.zeroturnaround:zt-zip:1.16
|    |         \--- org.slf4j:slf4j-api:1.6.6 -> 2.0.16
|    \--- com.taobao.arthas:arthas-packaging:4.0.4
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0 (*)
\--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7 (*)

runtimeClasspath - Runtime classpath of source set 'main'.
+--- project :camunda-server-nacos-support
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-auth-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-encryption-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-logback-adapter-12:2.4.3
|    |    |    +--- com.alibaba.nacos:logback-adapter:1.1.3
|    |    |    +--- com.alibaba.nacos:nacos-log4j2-adapter:2.4.3
|    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.13.5 -> 2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.13.5 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.15 -> 4.4.16
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.15 -> 4.4.16
|    |    |    |    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13
|    |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
|    |    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |    |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    +--- io.prometheus:simpleclient:0.15.0 -> 0.16.0
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |    |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |    \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    +--- org.yaml:snakeyaml:2.0 -> 2.2
|    |    |    \--- io.micrometer:micrometer-core:1.9.17 -> 1.13.8
|    |    |         +--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- io.micrometer:micrometer-observation:1.13.8
|    |    |         |    \--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- org.hdrhistogram:HdrHistogram:2.2.2
|    |    |         \--- org.latencyutils:LatencyUtils:2.0.3
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    \--- org.springframework.cloud:spring-cloud-context:4.1.2
|    |         \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3 (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    +--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    \--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    +--- org.springframework.boot:spring-boot-starter -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot:3.3.6
|    |    |    +--- org.springframework:spring-core:6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.6
|    |    |    +--- ch.qos.logback:logback-classic:1.5.12
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.12
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0
|    |    +--- org.camunda.bpm:camunda-engine-spring-6:7.23.0
|    |    |    \--- org.camunda.bpm:camunda-engine:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-bpmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-cmmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.bpm.dmn:camunda-engine-dmn:7.23.0
|    |    |         |    +--- org.camunda.commons:camunda-commons-utils:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0
|    |    |         |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.bpm.model:camunda-dmn-model:7.23.0
|    |    |         |    |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-juel:7.23.0
|    |    |         |    |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-scala:7.23.0
|    |    |         |    |    +--- org.camunda.feel:feel-engine:1.19.1
|    |    |         |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |         |    |    |    \--- com.fasterxml.uuid:java-uuid-generator:5.1.0
|    |    |         |    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |         |    |    \--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    +--- org.camunda.feel:feel-engine:1.19.1 (*)
|    |    |         |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         +--- org.mybatis:mybatis:3.5.15
|    |    |         +--- org.springframework:spring-beans:6.2.4 -> 6.1.15 (*)
|    |    |         \--- joda-time:joda-time:2.12.5 -> 2.12.7
|    |    +--- org.springframework:spring-context:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-jdbc:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    +--- org.springframework:spring-tx:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-orm:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.4.4 -> 3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter:3.4.4 -> 3.3.6 (*)
|    |    +--- org.apache.commons:commons-lang3:3.17.0 -> 3.14.0
|    |    +--- com.fasterxml.uuid:java-uuid-generator:4.3.0 -> 5.1.0 (*)
|    |    \--- com.sun.xml.bind:jaxb-impl:4.0.5
|    |         \--- com.sun.xml.bind:jaxb-core:4.0.5
|    |              +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |              |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    |              \--- org.eclipse.angus:angus-activation:2.0.2
|    |                   \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- org.camunda.bpm:camunda-engine -> 7.23.0 (*)
|    +--- com.alibaba.nacos:logback-adapter -> 1.1.3
|    \--- org.slf4j:slf4j-api -> 2.0.16
+--- project :camunda-pulsar-connector-plugin
|    \--- org.springframework.boot:spring-boot-starter-web -> 3.3.6
|         +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-json:3.3.6
|         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         |    +--- org.springframework:spring-web:6.1.15
|         |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|         |    |    +--- org.springframework:spring-core:6.1.15 (*)
|         |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|         |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3
|         |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |         \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6
|         |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|         |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.33
|         |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         +--- org.springframework:spring-web:6.1.15 (*)
|         \--- org.springframework:spring-webmvc:6.1.15
|              +--- org.springframework:spring-aop:6.1.15 (*)
|              +--- org.springframework:spring-beans:6.1.15 (*)
|              +--- org.springframework:spring-context:6.1.15 (*)
|              +--- org.springframework:spring-core:6.1.15 (*)
|              +--- org.springframework:spring-expression:6.1.15 (*)
|              \--- org.springframework:spring-web:6.1.15 (*)
+--- project :camunda-bpm-service
|    +--- project :camunda-bpm-api
|    |    +--- com.open-care:api-jsonschema-definition -> 3.4.7
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.1.1 -> 4.2.0
|    |    |    |    +--- org.springframework.cloud:spring-cloud-starter:4.2.0 -> 4.1.2
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    |    |    \--- org.springframework.security:spring-security-rsa:1.1.2
|    |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.77 -> 1.78.1
|    |    |    |    +--- org.springframework.cloud:spring-cloud-openfeign-core:4.2.0 -> 4.1.1
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.4 -> 3.3.6
|    |    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    |    \--- org.aspectj:aspectjweaver:********
|    |    |    |    |    +--- io.github.openfeign.form:feign-form-spring:3.8.0
|    |    |    |    |    |    +--- io.github.openfeign.form:feign-form:3.8.0
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    |    +--- org.springframework:spring-web:5.1.5.RELEASE -> 6.1.15 (*)
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    \--- commons-fileupload:commons-fileupload:1.5
|    |    |    |    |         \--- commons-io:commons-io:2.11.0 -> 2.16.1
|    |    |    |    +--- org.springframework:spring-web:6.2.0 -> 6.1.15 (*)
|    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.2.0 -> 4.1.2 (*)
|    |    |    |    +--- io.github.openfeign:feign-core:13.5 -> 13.2.1
|    |    |    |    \--- io.github.openfeign:feign-slf4j:13.5 -> 13.2.1
|    |    |    |         +--- io.github.openfeign:feign-core:13.2.1
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    |    +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-revised:1.3120.20220318T16
|    |    |    |    |    |    \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    \--- com.open-care:method-name-getter:1.3120.20220318T16
|    |    |    |    \--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16
|    |    |    |         \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT
|    |    |    |         |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         |    \--- com.google.code.gson:gson:2.8.5 -> 2.10.1
|    |    |    |         +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |         +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    \--- com.open-care:api-jsonschema-definition-common:3.4.7
|    |    |         +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |         \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- cn.hutool:hutool-all -> 5.8.27
|    |    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
|    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2
|    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-core:2.1.2
|    |    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-eventbus:2.1.2
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.1 -> 1.0.4
|    |    |    |    +--- org.eclipse.collections:eclipse-collections:9.0.0
|    |    |    |    |    \--- org.eclipse.collections:eclipse-collections-api:9.0.0
|    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.8.10 -> 2.17.3 (*)
|    |    |    |    +--- com.lmax:disruptor:3.3.7
|    |    |    |    +--- io.vavr:vavr:0.9.2
|    |    |    |    |    \--- io.vavr:vavr-match:0.9.2
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    +--- org.camunda.bpm:camunda-engine-plugin-spin -> 7.23.0
|    |    \--- org.camunda.spin:camunda-spin-core:7.23.0
|    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         \--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    +--- org.camunda.spin:camunda-spin-dataformat-json-jackson -> 7.23.0
|    |    +--- org.camunda.spin:camunda-spin-core:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.15.2 -> 2.17.3 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    \--- com.jayway.jsonpath:json-path:2.9.0
|    |         +--- net.minidev:json-smart:2.5.0 -> 2.5.1
|    |         |    \--- net.minidev:accessors-smart:2.5.1
|    |         \--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0
|    |    |    +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |    |    +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6
|    |         +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-validation:3.3.6
|    |         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|    |         |    \--- org.hibernate.validator:hibernate-validator:8.0.1.Final
|    |         |         +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |         +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |         |         \--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |         +--- org.springframework:spring-web:6.1.15 (*)
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- org.glassfish.hk2:osgi-resource-locator:1.0.3
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.9
|    |         |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.9
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |         |    +--- org.jboss.logging:jboss-logging:3.6.0.Final -> 3.5.3.Final
|    |         |    +--- org.glassfish.expressly:expressly:5.0.0
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.ext:jersey-spring6:3.1.9
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6
|    |         |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    +--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6
|    |         |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6
|    |         |    |    |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    \--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.hk2:hk2:3.0.6
|    |         |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-core:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-runlevel:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    \--- org.glassfish.hk2:class-model:3.0.6
|    |         |    |         \--- org.ow2.asm:asm-commons:9.6
|    |         |    +--- org.glassfish.hk2:spring-bridge:3.0.6
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         \--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.9
|    |              +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |              +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.9
|    |              |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |              +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (*)
|    |              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta -> 7.23.0 (*)
|    +--- org.camunda.bpm:camunda-engine-rest:7.23.0
|    |    \--- org.camunda.bpm:camunda-engine-rest-core:7.23.0
|    |         +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |         +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.15.2 -> 2.17.3
|    |         |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |         \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    +--- jakarta.ws.rs:jakarta.ws.rs-api -> 3.1.0
|    +--- org.springframework.boot:spring-boot-starter-data-jpa -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- com.zaxxer:HikariCP:5.1.0
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    \--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    +--- org.hibernate.orm:hibernate-core:6.5.3.Final
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final
|    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final
|    |    |    +--- io.smallrye:jandex:3.1.2
|    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (*)
|    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5
|    |    |    |    \--- org.glassfish.jaxb:jaxb-core:4.0.5
|    |    |    |         +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    |    |    |         +--- jakarta.activation:jakarta.activation-api:2.1.3
|    |    |    |         +--- org.eclipse.angus:angus-activation:2.0.2 (*)
|    |    |    |         +--- org.glassfish.jaxb:txw2:4.0.5
|    |    |    |         \--- com.sun.istack:istack-commons-runtime:4.1.2
|    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |    |    \--- org.antlr:antlr4-runtime:4.13.0
|    |    +--- org.springframework.data:spring-data-jpa:3.3.6
|    |    |    +--- org.springframework.data:spring-data-commons:3.3.6
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |    +--- org.springframework:spring-orm:6.1.15 (*)
|    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.antlr:antlr4-runtime:4.13.0
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.0.0 -> 2.1.1
|    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    \--- org.springframework:spring-aspects:6.1.15
|    |         \--- org.aspectj:aspectjweaver:********
|    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2
|    |    +--- org.springframework.cloud:spring-cloud-starter:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:4.1.2
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    +--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2
|    |    |         \--- io.projectreactor:reactor-core:3.5.20 -> 3.6.12 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:3.2.4 -> 3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    \--- org.springframework:spring-context-support:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    +--- org.springframework.boot:spring-boot-configuration-processor -> 3.3.6
|    +--- org.springframework.retry:spring-retry -> 2.0.10
|    +--- jakarta.persistence:jakarta.persistence-api -> 3.1.0
|    +--- jakarta.servlet:jakarta.servlet-api -> 6.0.0
|    +--- jakarta.annotation:jakarta.annotation-api -> 2.1.1
|    +--- jakarta.validation:jakarta.validation-api -> 3.0.2
|    +--- org.hibernate:hibernate-core -> 5.6.15.Final
|    |    +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |    +--- javax.persistence:javax.persistence-api:2.2
|    |    +--- net.bytebuddy:byte-buddy:1.12.18 -> 1.14.19
|    |    +--- antlr:antlr:2.7.7
|    |    +--- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:1.1.1.Final
|    |    +--- org.jboss:jandex:2.4.2.Final
|    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    +--- javax.activation:javax.activation-api:1.2.0
|    |    +--- org.hibernate.common:hibernate-commons-annotations:5.1.2.Final -> 6.0.6.Final
|    |    +--- javax.xml.bind:jaxb-api:2.3.1 -> 2.2.7
|    |    \--- org.glassfish.jaxb:jaxb-runtime:2.3.1 -> 4.0.5 (*)
|    +--- org.dom4j:dom4j -> 2.1.4
|    |    +--- jaxen:jaxen:1.1.6 -> 2.0.0
|    |    +--- javax.xml.stream:stax-api:1.0-2
|    |    +--- net.java.dev.msv:xsdlib:2013.6.1
|    |    |    \--- relaxngDatatype:relaxngDatatype:20020414
|    |    +--- javax.xml.bind:jaxb-api:2.2.12 -> 2.2.7
|    |    +--- pull-parser:pull-parser:2.1.10
|    |    \--- xpp3:xpp3:1.1.4c
|    +--- com.open-care:open-care-json-converter -> 1.286.20240806-SNAPSHOT-JSOG (*)
|    +--- com.open-care:api-jsonschema-definition -> 3.4.7 (*)
|    +--- com.open-care:open-care-core-util -> 2.7
|    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    +--- com.open-care:open-care-common-util -> 3.5-SNAPSHOT
|    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    +--- com.open-care:open-care-common-util-core:3.5-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- com.open-care:api-jsonschema-definition-common:3.4.7 (*)
|    |    |    +--- com.open-care:open-care-core-component:2.7
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    |    \--- org.projectlombok:lombok:1.18.34 -> 1.18.36
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    \--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    +--- com.open-care:open-care-core-component:2.7 (*)
|    |    +--- com.open-care:open-care-core-util:2.7 (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.2.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.5
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |    |    |    \--- io.micrometer:micrometer-observation:1.12.13 -> 1.13.8 (*)
|    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-web:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |         \--- org.springframework:spring-web:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    \--- org.springframework:spring-web:6.1.15 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4
|    |    |              \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- com.nimbusds:oauth2-oidc-sdk:9.43.4
|    |    |    |         +--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    |    |         +--- com.nimbusds:content-type:2.2
|    |    |    |         +--- net.minidev:json-smart:[1.3.3,2.4.10] -> 2.5.1 (*)
|    |    |    |         +--- com.nimbusds:lang-tag:1.7
|    |    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE
|    |    |    |    +--- io.netty:netty-common:4.1.107.Final -> 4.1.115.Final
|    |    |    |    +--- io.netty:netty-handler:4.1.107.Final -> 4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-transport:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-resolver:4.1.115.Final (*)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    |    \--- io.netty:netty-codec:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |         \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.107.Final -> 4.1.115.Final (*)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12 (*)
|    |    |    \--- org.springframework.data:spring-data-redis:3.3.6
|    |    |         +--- org.springframework.data:spring-data-keyvalue:3.3.6
|    |    |         |    +--- org.springframework.data:spring-data-commons:3.3.6 (*)
|    |    |         |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |         +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         +--- org.springframework:spring-oxm:6.1.15
|    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
|    |    |         |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context-support:6.1.15 (*)
|    |    |         \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:4.1.2 (*)
|    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    +--- com.googlecode.aviator:aviator:4.2.0
|    |    |    \--- commons-beanutils:commons-beanutils:1.9.3
|    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |         \--- commons-collections:commons-collections:3.2.2
|    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    +--- io.projectreactor.addons:reactor-extra:3.4.6 -> 3.5.2 (*)
|    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    +--- org.apache.commons:commons-collections4:4.5.0-M1
|    |    +--- commons-beanutils:commons-beanutils:1.9.3 (*)
|    |    +--- org.apache.commons:commons-pool2:2.12.0
|    |    +--- commons-dbutils:commons-dbutils:1.7
|    |    +--- commons-io:commons-io:2.16.1
|    |    +--- commons-lang:commons-lang:2.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 (*)
|    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.33
|    |    |    \--- org.apache.tomcat:tomcat-juli:10.1.33
|    |    +--- org.apache.poi:poi:4.1.0
|    |    |    +--- commons-codec:commons-codec:1.12 -> 1.16.1
|    |    |    +--- org.apache.commons:commons-collections4:4.3 -> 4.5.0-M1
|    |    |    \--- org.apache.commons:commons-math3:3.6.1
|    |    +--- org.apache.poi:poi-ooxml:4.1.2
|    |    |    +--- org.apache.poi:poi:4.1.2 -> 4.1.0 (*)
|    |    |    +--- org.apache.poi:poi-ooxml-schemas:4.1.2
|    |    |    |    \--- org.apache.xmlbeans:xmlbeans:3.1.0
|    |    |    +--- org.apache.commons:commons-compress:1.19 -> 1.27.1
|    |    |    |    +--- commons-codec:commons-codec:1.17.1 -> 1.16.1
|    |    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    |    \--- org.apache.commons:commons-lang3:3.16.0 -> 3.14.0
|    |    |    \--- com.github.virtuald:curvesapi:1.06
|    |    +--- org.apache.pdfbox:pdfbox:3.0.2
|    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    +--- org.apache.pdfbox:fontbox:3.0.2
|    |    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2 (*)
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG
|    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT (*)
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    +--- org.hibernate.orm:hibernate-core:6.4.4.Final -> 6.5.3.Final (*)
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.2.0 -> 3.3.2
|    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-core:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    +--- com.fasterxml:classmate:1.7.0 (c)
|    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    +--- com.zaxxer:HikariCP:5.1.0 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.3.Final (c)
|    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    +--- com.open-care:api-jsonschema-definition:3.4.7 (*)
|    |    +--- com.github.behaim:behaim:2.0.20240807
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- ch.qos.logback:logback-classic:0.9.21 -> 1.5.12 (*)
|    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    +--- com.google.guava:guava:31.1-jre -> 20.0
|    |    |    \--- com.open-care:open-care-generics-utils:1.0.20230118T16
|    |    |         +--- org.slf4j:slf4j-api:1.7.13 -> 2.0.16
|    |    |         \--- com.googlecode.gentyref:gentyref:1.2.0
|    |    +--- com.github.behaim:behaim_with-hibernate-proxy:2.0.20240807
|    |    |    +--- org.hibernate:hibernate-core:6.1.7.Final -> 5.6.15.Final (*)
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.github.behaim:behaim_target-value-converters-for-date-type:2.0.20240807
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3
|    |    |    |    |    +--- com.graphql-java:java-dataloader:3.3.0
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
|    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3 -> 1.0.4
|    |    |    |    \--- com.graphql-java:graphql-java-extended-scalars:22.0
|    |    |    |         \--- com.graphql-java:graphql-java:22.0 -> 22.3 (*)
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (*)
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (*)
|    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    +--- com.graphql-java:graphql-java:22.3 (*)
|    |    +--- org.atteo:evo-inflector:1.2.2
|    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    +--- com.open-care:graphql-jpa-mutation-autoconfigure:1.2.8.20240819T17
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |    |    |    \--- org.springframework.graphql:spring-graphql:1.3.3
|    |    |    |         +--- io.micrometer:context-propagation:1.1.2
|    |    |    |         +--- com.graphql-java:graphql-java:22.3 (*)
|    |    |    |         +--- io.projectreactor:reactor-core:3.6.11 -> 3.6.12 (*)
|    |    |    |         \--- org.springframework:spring-context:6.1.14 -> 6.1.15 (*)
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10
|    |    |    |    +--- org.hibernate:hibernate-core:6.4.4.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    \--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10
|    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |         \--- org.jgrapht:jgrapht-core:1.3.0
|    |    |    |              \--- org.jheaps:jheaps:0.9
|    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807 (*)
|    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    +--- com.open-care:interceptor-for-code-manipulation-on-graphql-jpa-query-schema:1.2.8.20240819T17
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |    \--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17
|    |    |    |    |         \--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    \--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (*)
|    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- joda-time:joda-time:2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0
|    |    |    |    |    \--- jakarta.annotation:jakarta.annotation-api:3.0.0 -> 2.1.1
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    |    \--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- com.open-care:graphql-jpa-query-schema-code-manipulated:1.2.8.20240819T17
|    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    +--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final (c)
|    |    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    |    +--- org.antlr:antlr4:4.13.0 (c)
|    |    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- io.smallrye:jandex:3.1.2 (c)
|    |    |    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (c)
|    |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    |    +--- com.zaxxer:HikariCP:3.2.0 -> 5.1.0 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-core:1.10.4 -> 1.13.8 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (*)
|    |    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17
|    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (*)
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:gson-jodatime-serialisers:1.6.0-SNAPSHOT (*)
|    |    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-query-transformer-with-supporting-managedtype:1.2.8.20240819T17
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |    \--- com.open-care:graphql-query-transformer:1.2.8.20240819T17
|    |    |    |    |         +--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10 (*)
|    |    |    |    |         +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |         +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    |    |    |         +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |         +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |         +--- com.open-care:open-care-generics-utils:1.0.20230118T16 (*)
|    |    |    |    |         +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |         \--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17 (*)
|    |    |    |    \--- com.open-care:graphql-query-result-general-model-common:1.2.8.20240819T17
|    |    |    \--- com.open-care:graphql-jpa-query-autoconfigure-code-manipulated:1.2.8.20240819T17
|    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17 (*)
|    |    +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    +--- org.crazycake:camel-name-utils:1.0.0-RELEASE
|    |    +--- com.alibaba:druid:1.2.22
|    |    +--- com.open-care:kingbase-hibernate-dialect:6.2
|    |    +--- io.github.kostaskougios:cloning:1.10.3
|    |    |    \--- org.objenesis:objenesis:3.0.1
|    |    +--- org.reflections:reflections:0.10.2
|    |    |    +--- org.javassist:javassist:3.28.0-GA -> 3.30.2-GA
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    \--- org.slf4j:slf4j-api:1.7.32 -> 2.0.16
|    |    +--- com.github.jsqlparser:jsqlparser:1.2
|    |    +--- com.mysql:mysql-connector-j:8.3.0
|    |    +--- org.postgresql:postgresql:42.7.4
|    |    |    \--- org.checkerframework:checker-qual:3.42.0
|    |    +--- org.yaml:snakeyaml:1.26 -> 2.2
|    |    +--- com.ibm.icu:icu4j:71.1 -> 72.1
|    |    +--- com.google.code.findbugs:annotations:3.0.1
|    |    |    +--- net.jcip:jcip-annotations:1.0
|    |    |    \--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    +--- com.open-care:auth-core:1.0-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- org.bouncycastle:bcprov-jdk15to18:1.78.1
|    |    |    +--- cn.dev33:sa-token-spring-boot3-starter:1.40.0
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.0.1 -> 3.3.6 (*)
|    |    |    |    +--- cn.dev33:sa-token-jakarta-servlet:1.40.0
|    |    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    |    \--- jakarta.servlet:jakarta.servlet-api:6.0.0
|    |    |    |    \--- cn.dev33:sa-token-spring-boot-autoconfig:1.40.0
|    |    |    +--- cn.dev33:sa-token-spring-aop:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- org.springframework.boot:spring-boot-starter-aop:2.5.15 -> 3.3.6 (*)
|    |    |    +--- cn.dev33:sa-token-sso:1.40.0
|    |    |    |    \--- cn.dev33:sa-token-core:1.40.0
|    |    |    +--- cn.dev33:sa-token-jwt:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- cn.hutool:hutool-jwt:5.8.20
|    |    |    |         +--- cn.hutool:hutool-json:5.8.20
|    |    |    |         |    \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    |         \--- cn.hutool:hutool-crypto:5.8.20
|    |    |    |              \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    |    \--- com.open-care:rpc-core:3.3.2-SNAPSHOT
|    |    |         \--- org.mapstruct:mapstruct:1.6.3
|    |    +--- cn.hutool:hutool-all:5.8.27
|    |    \--- joda-time:joda-time:2.12.7
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0
|    |    +--- com.alibaba.csp:sentinel-transport-simple-http:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-transport-common:1.8.8
|    |    |         +--- com.alibaba.csp:sentinel-datasource-extension:1.8.8
|    |    |         |    \--- com.alibaba.csp:sentinel-core:1.8.8
|    |    |         \--- com.alibaba:fastjson:1.2.83_noneautotype -> 1.2.83
|    |    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2 -> ********
|    |    +--- com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2023.0.1.0
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6
|    |    |         \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6 (*)
|    |    +--- com.alibaba.csp:sentinel-spring-webmvc-6x-adapter:1.8.6
|    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    |    +--- com.alibaba.csp:sentinel-cluster-server-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6
|    |    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    +--- com.alibaba.csp:sentinel-cluster-client-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2023.0.1.0
|    |         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    +--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7
|    |    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.7 -> 1.8.8 (*)
|    |    \--- com.alibaba.nacos:nacos-client:1.4.2 -> 2.4.3 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.9 -> 3.3.6 (*)
|    |    \--- org.apache.seata:seata-spring-boot-starter:2.1.0 -> 2.3.0
|    |         +--- org.apache.seata:seata-spring-autoconfigure-client:2.3.0
|    |         |    \--- org.apache.seata:seata-spring-autoconfigure-core:2.3.0
|    |         \--- org.apache.seata:seata-all:2.3.0
|    |              +--- org.springframework:spring-context:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-core:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-beans:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-aop:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-webmvc:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-tx:5.3.39 -> 6.1.15 (*)
|    |              +--- io.netty:netty-all:4.1.101.Final -> 4.1.115.Final
|    |              |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-haproxy:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http2:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-memcache:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-mqtt:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-redis:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-smtp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-socks:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-stomp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-xml:4.1.115.Final
|    |              |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    +--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-handler-proxy:4.1.115.Final
|    |              |    +--- io.netty:netty-handler-ssl-ocsp:4.1.115.Final
|    |              |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-rxtx:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-sctp:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-udt:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-classes-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-classes-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-resolver-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-epoll:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-kqueue:4.1.115.Final (*)
|    |              |    \--- io.netty:netty-resolver-dns-native-macos:4.1.115.Final
|    |              |         \--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final (*)
|    |              +--- org.antlr:antlr4:4.8 -> 4.13.0
|    |              |    +--- org.antlr:antlr4-runtime:4.13.0
|    |              |    +--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.antlr:ST4:4.3.4
|    |              |    |    \--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
|    |              |    \--- com.ibm.icu:icu4j:72.1
|    |              +--- com.alibaba:fastjson:1.2.83
|    |              +--- com.alibaba:druid:1.2.20 -> 1.2.22
|    |              +--- com.typesafe:config:1.2.1
|    |              +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |              +--- commons-lang:commons-lang:2.6
|    |              +--- org.apache.commons:commons-pool2:2.11.1 -> 2.12.0
|    |              +--- commons-pool:commons-pool:1.6
|    |              +--- org.apache.dubbo.extensions:dubbo-filter-seata:1.0.2 -> 3.3.1
|    |              +--- aopalliance:aopalliance:1.0
|    |              +--- com.google.guava:guava:32.1.3-jre -> 20.0
|    |              +--- com.github.ben-manes.caffeine:caffeine:2.9.3 -> 3.1.8
|    |              |    +--- org.checkerframework:checker-qual:3.37.0 -> 3.42.0
|    |              |    \--- com.google.errorprone:error_prone_annotations:2.21.1
|    |              \--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    +--- com.nimbusds:nimbus-jose-jwt -> 9.24.4 (*)
|    +--- net.logstash.logback:logstash-logback-encoder -> 5.1
|    |    +--- ch.qos.logback:logback-core:1.2.3 -> 1.5.12
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.5 -> 2.17.3 (*)
|    +--- org.reflections:reflections -> 0.10.2 (*)
|    +--- uk.com.robust-it:cloning -> 1.9.2
|    |    \--- org.objenesis:objenesis:2.1 -> 3.0.1
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.3 (*)
|    +--- com.fasterxml.jackson.core:jackson-core -> 2.17.3 (*)
|    +--- com.google.code.gson:gson -> 2.10.1
|    +--- cn.hutool:hutool-all -> 5.8.27
|    +--- com.belerweb:pinyin4j -> 2.5.1
|    \--- org.mapstruct:mapstruct -> 1.6.3
+--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp-core:7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0 (*)
|    |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.4.4 -> 3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6 (*)
|    \--- org.camunda.bpm.webapp:camunda-webapp-webjar:7.23.0
|         \--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 3.3.6 (*)
+--- org.postgresql:postgresql -> 42.7.4 (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.6
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (*)
|    |    +--- org.springframework.boot:spring-boot-actuator:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
|    +--- io.micrometer:micrometer-observation:1.13.8 (*)
|    \--- io.micrometer:micrometer-jakarta9:1.13.8
|         +--- io.micrometer:micrometer-core:1.13.8 (*)
|         +--- io.micrometer:micrometer-commons:1.13.8
|         \--- io.micrometer:micrometer-observation:1.13.8 (*)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2 (*)
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
+--- org.springframework.retry:spring-retry -> 2.0.10
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2 (*)
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2 (*)
+--- org.apache.seata:seata-all -> 2.3.0 (*)
+--- org.apache.dubbo.extensions:dubbo-filter-seata -> 3.3.1
+--- com.taobao.arthas:arthas-spring-boot-starter -> 4.0.4
|    +--- com.taobao.arthas:arthas-agent-attach:4.0.4
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.11 -> 1.14.19
|    |    \--- org.zeroturnaround:zt-zip:1.16
|    |         \--- org.slf4j:slf4j-api:1.6.6 -> 2.0.16
|    \--- com.taobao.arthas:arthas-packaging:4.0.4
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0 (*)
\--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7 (*)

runtimeElements - Runtime elements for the 'main' feature. (n)
No dependencies

runtimeOnly - Runtime-only dependencies for the 'main' feature. (n)
No dependencies

testAndDevelopmentOnly - Configuration for test and development-only dependencies such as Spring Boot's DevTools.
No dependencies

testAnnotationProcessor - Annotation processors and their dependencies for source set 'test'.
No dependencies

testCompileClasspath - Compile classpath for source set 'test'.
+--- project :camunda-server-nacos-support
+--- project :camunda-pulsar-connector-plugin
+--- project :camunda-bpm-service
+--- org.springframework.boot:spring-boot-starter-web -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6
|    |    +--- org.springframework.boot:spring-boot:3.3.6
|    |    |    +--- org.springframework:spring-core:6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8
|    |    |              \--- io.micrometer:micrometer-commons:1.13.8
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.6
|    |    |    +--- ch.qos.logback:logback-classic:1.5.12
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.12
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- org.springframework.boot:spring-boot-starter-json:3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    +--- org.springframework:spring-web:6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3
|    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|    |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.33
|    |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|    +--- org.springframework:spring-web:6.1.15 (*)
|    \--- org.springframework:spring-webmvc:6.1.15
|         +--- org.springframework:spring-aop:6.1.15 (*)
|         +--- org.springframework:spring-beans:6.1.15 (*)
|         +--- org.springframework:spring-context:6.1.15 (*)
|         +--- org.springframework:spring-core:6.1.15 (*)
|         +--- org.springframework:spring-expression:6.1.15 (*)
|         \--- org.springframework:spring-web:6.1.15 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0
|    +--- org.camunda.bpm:camunda-engine-spring-6:7.23.0
|    |    \--- org.camunda.bpm:camunda-engine:7.23.0
|    |         +--- org.camunda.bpm.model:camunda-bpmn-model:7.23.0
|    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         +--- org.camunda.bpm.model:camunda-cmmn-model:7.23.0
|    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         +--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         +--- org.camunda.bpm.dmn:camunda-engine-dmn:7.23.0
|    |         |    +--- org.camunda.commons:camunda-commons-utils:7.23.0
|    |         |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0
|    |         |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |         |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0
|    |         |    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |         |    +--- org.camunda.bpm.model:camunda-dmn-model:7.23.0
|    |         |    |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0
|    |         |    |    \--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-juel:7.23.0
|    |         |    |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         |    |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         |    |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-scala:7.23.0
|    |         |    |    +--- org.camunda.feel:feel-engine:1.19.1
|    |         |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |         |    |    |    \--- com.fasterxml.uuid:java-uuid-generator:5.1.0
|    |         |    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |         |    |    \--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |         |    +--- org.camunda.feel:feel-engine:1.19.1 (*)
|    |         |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |         +--- org.mybatis:mybatis:3.5.15
|    |         +--- org.springframework:spring-beans:6.2.4 -> 6.1.15 (*)
|    |         \--- joda-time:joda-time:2.12.5 -> 2.12.7
|    +--- org.springframework:spring-context:6.2.5 -> 6.1.15 (*)
|    +--- org.springframework:spring-jdbc:6.2.5 -> 6.1.15
|    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.springframework:spring-tx:6.1.15
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    +--- org.springframework:spring-tx:6.2.5 -> 6.1.15 (*)
|    +--- org.springframework:spring-orm:6.2.5 -> 6.1.15
|    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    +--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    \--- org.springframework:spring-tx:6.1.15 (*)
|    +--- org.springframework.boot:spring-boot-autoconfigure:3.4.4 -> 3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-starter:3.4.4 -> 3.3.6 (*)
|    +--- org.apache.commons:commons-lang3:3.17.0 -> 3.14.0
|    +--- com.fasterxml.uuid:java-uuid-generator:4.3.0 -> 5.1.0 (*)
|    \--- com.sun.xml.bind:jaxb-impl:4.0.5
|         \--- com.sun.xml.bind:jaxb-core:4.0.5
|              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|                   \--- jakarta.activation:jakarta.activation-api:2.1.3
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0
|    |    +--- commons-fileupload:commons-fileupload:1.5
|    |    |    \--- commons-io:commons-io:2.11.0 -> 2.16.1
|    |    +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6
|         +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-validation:3.3.6
|         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|         |    \--- org.hibernate.validator:hibernate-validator:8.0.1.Final
|         |         +--- jakarta.validation:jakarta.validation-api:3.0.2
|         |         +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|         |         \--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|         +--- org.springframework:spring-web:6.1.15 (*)
|         +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9
|         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9
|         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    \--- org.glassfish.hk2:osgi-resource-locator:1.0.3
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9
|         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.9
|         |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    \--- jakarta.validation:jakarta.validation-api:3.0.2
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.9
|         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.9
|         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|         |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|         |    +--- org.jboss.logging:jboss-logging:3.6.0.Final -> 3.5.3.Final
|         |    +--- org.glassfish.expressly:expressly:5.0.0
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         +--- org.glassfish.jersey.ext:jersey-spring6:3.1.9
|         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|         |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.9
|         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6
|         |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    +--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6
|         |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6
|         |    |    |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|         |    |    |    |    \--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    |    \--- org.javassist:javassist:3.30.2-GA
|         |    |    \--- org.javassist:javassist:3.30.2-GA
|         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|         |    +--- org.glassfish.hk2:hk2:3.0.6
|         |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-core:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    |    \--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    +--- org.glassfish.hk2:hk2-runlevel:3.0.6
|         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|         |    |    |    \--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|         |    |    \--- org.glassfish.hk2:class-model:3.0.6
|         |    |         \--- org.ow2.asm:asm-commons:9.6
|         |    +--- org.glassfish.hk2:spring-bridge:3.0.6
|         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|         \--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.9
|              +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|              +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.9
|              |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|              +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (*)
|              +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|              +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (*)
|              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp-core:7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0 (*)
|    |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.4.4 -> 3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6 (*)
|    \--- org.camunda.bpm.webapp:camunda-webapp-webjar:7.23.0
|         \--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- com.zaxxer:HikariCP:5.1.0
|    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    \--- org.springframework:spring-jdbc:6.1.15 (*)
+--- org.postgresql:postgresql -> 42.7.4
+--- org.springframework.boot:spring-boot-starter-actuator -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.6
|    |    +--- org.springframework.boot:spring-boot-actuator:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
|    +--- io.micrometer:micrometer-observation:1.13.8 (*)
|    \--- io.micrometer:micrometer-jakarta9:1.13.8
|         +--- io.micrometer:micrometer-core:1.13.8
|         |    +--- io.micrometer:micrometer-commons:1.13.8
|         |    \--- io.micrometer:micrometer-observation:1.13.8 (*)
|         +--- io.micrometer:micrometer-commons:1.13.8
|         \--- io.micrometer:micrometer-observation:1.13.8 (*)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2
|    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-core:2.1.2
|    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-eventbus:2.1.2
|    |    |    +--- org.reactivestreams:reactive-streams:1.0.1 -> 1.0.4
|    |    |    +--- org.eclipse.collections:eclipse-collections:9.0.0
|    |    |    |    \--- org.eclipse.collections:eclipse-collections-api:9.0.0
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.8.10 -> 2.17.3 (*)
|    |    |    +--- com.lmax:disruptor:3.3.7
|    |    |    +--- io.vavr:vavr:0.9.2
|    |    |    |    \--- io.vavr:vavr-match:0.9.2
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0
|    +--- org.springframework.cloud:spring-cloud-starter:4.2.0 -> 4.1.2
|    |    +--- org.springframework.boot:spring-boot-starter:3.2.4 -> 3.3.6 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    \--- org.springframework.security:spring-security-rsa:1.1.2
|    |         \--- org.bouncycastle:bcprov-jdk18on:1.77 -> 1.78.1
|    +--- org.springframework.cloud:spring-cloud-openfeign-core:4.2.0 -> 4.1.1
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.2.4 -> 3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.4 -> 3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    \--- org.aspectj:aspectjweaver:********
|    |    +--- io.github.openfeign.form:feign-form-spring:3.8.0
|    |    |    +--- io.github.openfeign.form:feign-form:3.8.0
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    +--- org.springframework:spring-web:5.1.5.RELEASE -> 6.1.15 (*)
|    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    \--- commons-fileupload:commons-fileupload:1.5 (*)
|    +--- org.springframework:spring-web:6.2.0 -> 6.1.15 (*)
|    +--- org.springframework.cloud:spring-cloud-commons:4.2.0 -> 4.1.2 (*)
|    +--- io.github.openfeign:feign-core:13.5 -> 13.2.1
|    \--- io.github.openfeign:feign-slf4j:13.5 -> 13.2.1
|         +--- io.github.openfeign:feign-core:13.2.1
|         \--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
+--- org.springframework.retry:spring-retry -> 2.0.10
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2
|    +--- org.springframework.cloud:spring-cloud-starter:4.1.2 (*)
|    +--- org.springframework.cloud:spring-cloud-loadbalancer:4.1.2
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    +--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12
|    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    \--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2
|    |         \--- io.projectreactor:reactor-core:3.5.20 -> 3.6.12 (*)
|    +--- org.springframework.boot:spring-boot-starter-cache:3.2.4 -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    \--- org.springframework:spring-context-support:6.1.15
|    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |         \--- org.springframework:spring-core:6.1.15 (*)
|    \--- com.stoyanr:evictor:1.0.0
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2
|    +--- org.springframework.boot:spring-boot-starter-aop:3.2.9 -> 3.3.6 (*)
|    \--- org.apache.seata:seata-spring-boot-starter:2.1.0 -> 2.3.0
|         +--- org.apache.seata:seata-spring-autoconfigure-client:2.3.0
|         |    \--- org.apache.seata:seata-spring-autoconfigure-core:2.3.0
|         \--- org.apache.seata:seata-all:2.3.0
|              +--- org.springframework:spring-context:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-core:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-beans:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-aop:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-webmvc:5.3.39 -> 6.1.15 (*)
|              +--- org.springframework:spring-tx:5.3.39 -> 6.1.15 (*)
|              +--- io.netty:netty-all:4.1.101.Final -> 4.1.115.Final
|              |    +--- io.netty:netty-buffer:4.1.115.Final
|              |    |    \--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-codec:4.1.115.Final
|              |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    \--- io.netty:netty-transport:4.1.115.Final
|              |    |         +--- io.netty:netty-common:4.1.115.Final
|              |    |         +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |         \--- io.netty:netty-resolver:4.1.115.Final
|              |    |              \--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-codec-dns:4.1.115.Final
|              |    +--- io.netty:netty-codec-haproxy:4.1.115.Final
|              |    +--- io.netty:netty-codec-http:4.1.115.Final
|              |    +--- io.netty:netty-codec-http2:4.1.115.Final
|              |    +--- io.netty:netty-codec-memcache:4.1.115.Final
|              |    +--- io.netty:netty-codec-mqtt:4.1.115.Final
|              |    +--- io.netty:netty-codec-redis:4.1.115.Final
|              |    +--- io.netty:netty-codec-smtp:4.1.115.Final
|              |    +--- io.netty:netty-codec-socks:4.1.115.Final
|              |    +--- io.netty:netty-codec-stomp:4.1.115.Final
|              |    +--- io.netty:netty-codec-xml:4.1.115.Final
|              |    +--- io.netty:netty-common:4.1.115.Final
|              |    +--- io.netty:netty-handler:4.1.115.Final
|              |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final
|              |    |    |    +--- io.netty:netty-common:4.1.115.Final
|              |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|              |    |    |    \--- io.netty:netty-transport:4.1.115.Final (*)
|              |    |    \--- io.netty:netty-codec:4.1.115.Final (*)
|              |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|              |    +--- io.netty:netty-handler-proxy:4.1.115.Final
|              |    +--- io.netty:netty-handler-ssl-ocsp:4.1.115.Final
|              |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|              |    +--- io.netty:netty-resolver-dns:4.1.115.Final
|              |    +--- io.netty:netty-transport:4.1.115.Final (*)
|              |    +--- io.netty:netty-transport-rxtx:4.1.115.Final
|              |    +--- io.netty:netty-transport-sctp:4.1.115.Final
|              |    +--- io.netty:netty-transport-udt:4.1.115.Final
|              |    +--- io.netty:netty-transport-classes-epoll:4.1.115.Final
|              |    +--- io.netty:netty-transport-classes-kqueue:4.1.115.Final
|              |    \--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final
|              +--- org.antlr:antlr4:4.8
|              |    +--- org.antlr:antlr4-runtime:4.8
|              |    +--- org.antlr:antlr-runtime:3.5.2
|              |    +--- org.antlr:ST4:4.3
|              |    |    \--- org.antlr:antlr-runtime:3.5.2
|              |    +--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
|              |    +--- org.glassfish:javax.json:1.0.4
|              |    \--- com.ibm.icu:icu4j:61.1
|              +--- com.alibaba:fastjson:1.2.83
|              +--- com.alibaba:druid:1.2.20 -> 1.2.22
|              +--- com.typesafe:config:1.2.1
|              +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|              +--- commons-lang:commons-lang:2.6
|              +--- org.apache.commons:commons-pool2:2.11.1 -> 2.12.0
|              +--- commons-pool:commons-pool:1.6
|              +--- aopalliance:aopalliance:1.0
|              +--- com.google.guava:guava:32.1.3-jre -> 20.0
|              +--- com.github.ben-manes.caffeine:caffeine:2.9.3 -> 3.1.8
|              |    +--- org.checkerframework:checker-qual:3.37.0
|              |    \--- com.google.errorprone:error_prone_annotations:2.21.1
|              \--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
+--- org.apache.seata:seata-all -> 2.3.0 (*)
+--- org.apache.dubbo.extensions:dubbo-filter-seata -> 3.3.1
+--- com.taobao.arthas:arthas-spring-boot-starter -> 4.0.4
|    +--- com.taobao.arthas:arthas-agent-attach:4.0.4
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.11 -> 1.14.19
|    |    \--- org.zeroturnaround:zt-zip:1.16
|    |         \--- org.slf4j:slf4j-api:1.6.6 -> 2.0.16
|    \--- com.taobao.arthas:arthas-packaging:4.0.4
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0
|    +--- com.alibaba.csp:sentinel-transport-simple-http:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-transport-common:1.8.8
|    |         +--- com.alibaba.csp:sentinel-datasource-extension:1.8.8
|    |         |    \--- com.alibaba.csp:sentinel-core:1.8.8
|    |         \--- com.alibaba:fastjson:1.2.83_noneautotype -> 1.2.83
|    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- org.aspectj:aspectjweaver:1.9.2 -> ********
|    +--- com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2023.0.1.0
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6
|    |         \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    +--- com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6 (*)
|    +--- com.alibaba.csp:sentinel-spring-webmvc-6x-adapter:1.8.6
|    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    +--- com.alibaba.csp:sentinel-cluster-server-default:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6
|    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6 (*)
|    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    +--- com.alibaba.csp:sentinel-cluster-client-default:1.8.6
|    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6 (*)
|    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2023.0.1.0
|         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
+--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7
|    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.7 -> 1.8.8 (*)
|    \--- com.alibaba.nacos:nacos-client:1.4.2 -> 2.4.3
|         +--- com.alibaba.nacos:nacos-auth-plugin:2.4.3
|         +--- com.alibaba.nacos:nacos-encryption-plugin:2.4.3
|         +--- com.alibaba.nacos:nacos-logback-adapter-12:2.4.3
|         +--- com.alibaba.nacos:logback-adapter:1.1.3
|         +--- com.alibaba.nacos:nacos-log4j2-adapter:2.4.3
|         +--- commons-codec:commons-codec:1.15 -> 1.16.1
|         +--- com.fasterxml.jackson.core:jackson-core:2.13.5 -> 2.17.3 (*)
|         +--- com.fasterxml.jackson.core:jackson-databind:2.13.5 -> 2.17.3 (*)
|         +--- org.apache.httpcomponents:httpasyncclient:4.1.5
|         |    +--- org.apache.httpcomponents:httpcore:4.4.15 -> 4.4.16
|         |    +--- org.apache.httpcomponents:httpcore-nio:4.4.15 -> 4.4.16
|         |    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|         |    \--- org.apache.httpcomponents:httpclient:4.5.13
|         |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
|         |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
|         +--- org.apache.httpcomponents:httpcore:4.4.16
|         +--- io.prometheus:simpleclient:0.15.0 -> 0.16.0
|         |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|         |    |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|         |    \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|         |         \--- io.prometheus:simpleclient_tracer_common:0.16.0
|         +--- org.yaml:snakeyaml:2.0 -> 2.2
|         \--- io.micrometer:micrometer-core:1.9.17 -> 1.13.8 (*)
\--- org.springframework.boot:spring-boot-starter-test -> 3.3.6
     +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
     +--- org.springframework.boot:spring-boot-test:3.3.6
     |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
     |    \--- org.springframework:spring-test:6.1.15
     |         \--- org.springframework:spring-core:6.1.15 (*)
     +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.6
     |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
     |    +--- org.springframework.boot:spring-boot-test:3.3.6 (*)
     |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
     +--- com.jayway.jsonpath:json-path:2.9.0
     +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
     +--- net.minidev:json-smart:2.5.1
     |    \--- net.minidev:accessors-smart:2.5.1
     |         \--- org.ow2.asm:asm:9.6
     +--- org.assertj:assertj-core:3.25.3
     |    \--- net.bytebuddy:byte-buddy:1.14.11 -> 1.14.19
     +--- org.awaitility:awaitility:4.2.2
     |    \--- org.hamcrest:hamcrest:2.1 -> 2.2
     +--- org.hamcrest:hamcrest:2.2
     +--- org.junit.jupiter:junit-jupiter:5.10.5
     |    +--- org.junit:junit-bom:5.10.5
     |    |    +--- org.junit.jupiter:junit-jupiter:5.10.5 (c)
     |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (c)
     |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5 (c)
     |    |    \--- org.junit.platform:junit-platform-commons:1.10.5 (c)
     |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5
     |    |    +--- org.junit:junit-bom:5.10.5 (*)
     |    |    +--- org.opentest4j:opentest4j:1.3.0
     |    |    +--- org.junit.platform:junit-platform-commons:1.10.5
     |    |    |    +--- org.junit:junit-bom:5.10.5 (*)
     |    |    |    \--- org.apiguardian:apiguardian-api:1.1.2
     |    |    \--- org.apiguardian:apiguardian-api:1.1.2
     |    \--- org.junit.jupiter:junit-jupiter-params:5.10.5
     |         +--- org.junit:junit-bom:5.10.5 (*)
     |         +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
     |         \--- org.apiguardian:apiguardian-api:1.1.2
     +--- org.mockito:mockito-core:5.11.0
     |    +--- net.bytebuddy:byte-buddy:1.14.12 -> 1.14.19
     |    \--- net.bytebuddy:byte-buddy-agent:1.14.12 -> 1.14.19
     +--- org.mockito:mockito-junit-jupiter:5.11.0
     |    \--- org.mockito:mockito-core:5.11.0 (*)
     +--- org.skyscreamer:jsonassert:1.5.3
     |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
     +--- org.springframework:spring-core:6.1.15 (*)
     +--- org.springframework:spring-test:6.1.15 (*)
     \--- org.xmlunit:xmlunit-core:2.9.1

testCompileOnly - Compile only dependencies for source set 'test'. (n)
No dependencies

testImplementation - Implementation only dependencies for source set 'test'. (n)
\--- org.springframework.boot:spring-boot-starter-test (n)

testRuntimeClasspath - Runtime classpath of source set 'test'.
+--- project :camunda-server-nacos-support
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-auth-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-encryption-plugin:2.4.3
|    |    |    +--- com.alibaba.nacos:nacos-logback-adapter-12:2.4.3
|    |    |    +--- com.alibaba.nacos:logback-adapter:1.1.3
|    |    |    +--- com.alibaba.nacos:nacos-log4j2-adapter:2.4.3
|    |    |    +--- commons-codec:commons-codec:1.15 -> 1.16.1
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.13.5 -> 2.17.3
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3 (c)
|    |    |    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3 (c)
|    |    |    |         \--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3 (c)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.13.5 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.15 -> 4.4.16
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.15 -> 4.4.16
|    |    |    |    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    |    \--- org.apache.httpcomponents:httpclient:4.5.13
|    |    |    |         +--- org.apache.httpcomponents:httpcore:4.4.13 -> 4.4.16
|    |    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |    |         \--- commons-codec:commons-codec:1.11 -> 1.16.1
|    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16
|    |    |    +--- io.prometheus:simpleclient:0.15.0 -> 0.16.0
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0
|    |    |    |    |    \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    |    \--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0
|    |    |    |         \--- io.prometheus:simpleclient_tracer_common:0.16.0
|    |    |    +--- org.yaml:snakeyaml:2.0 -> 2.2
|    |    |    \--- io.micrometer:micrometer-core:1.9.17 -> 1.13.8
|    |    |         +--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- io.micrometer:micrometer-observation:1.13.8
|    |    |         |    \--- io.micrometer:micrometer-commons:1.13.8
|    |    |         +--- org.hdrhistogram:HdrHistogram:2.2.2
|    |    |         \--- org.latencyutils:LatencyUtils:2.0.3
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2
|    |    |    \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    |    \--- org.springframework.cloud:spring-cloud-context:4.1.2
|    |         \--- org.springframework.security:spring-security-crypto:6.2.3 -> 6.3.5
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config -> 2023.0.1.0
|    |    +--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    |    +--- com.alibaba.spring:spring-context-support:1.0.11
|    |    +--- com.alibaba.nacos:nacos-client:2.3.2 -> 2.4.3 (*)
|    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    +--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    \--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    +--- org.springframework.boot:spring-boot-starter -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot:3.3.6
|    |    |    +--- org.springframework:spring-core:6.1.15
|    |    |    |    \--- org.springframework:spring-jcl:6.1.15
|    |    |    \--- org.springframework:spring-context:6.1.15
|    |    |         +--- org.springframework:spring-aop:6.1.15
|    |    |         |    +--- org.springframework:spring-beans:6.1.15
|    |    |         |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.6
|    |    |    +--- ch.qos.logback:logback-classic:1.5.12
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.12
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.15 -> 2.0.16
|    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.9 -> 2.0.16
|    |    |    \--- org.slf4j:jul-to-slf4j:2.0.16
|    |    |         \--- org.slf4j:slf4j-api:2.0.16
|    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- org.yaml:snakeyaml:2.2
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0
|    |    +--- org.camunda.bpm:camunda-engine-spring-6:7.23.0
|    |    |    \--- org.camunda.bpm:camunda-engine:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-bpmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.model:camunda-cmmn-model:7.23.0
|    |    |         |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         +--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.bpm.dmn:camunda-engine-dmn:7.23.0
|    |    |         |    +--- org.camunda.commons:camunda-commons-utils:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0
|    |    |         |    |         \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |         |    +--- org.camunda.bpm.model:camunda-dmn-model:7.23.0
|    |    |         |    |    \--- org.camunda.bpm.model:camunda-xml-model:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0
|    |    |         |    |    \--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-juel:7.23.0
|    |    |         |    |    +--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         |    |    +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         |    |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         |    +--- org.camunda.bpm.dmn:camunda-engine-feel-scala:7.23.0
|    |    |         |    |    +--- org.camunda.feel:feel-engine:1.19.1
|    |    |         |    |    |    +--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |         |    |    |    \--- com.fasterxml.uuid:java-uuid-generator:5.1.0
|    |    |         |    |    |         \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |         |    |    \--- org.camunda.bpm.dmn:camunda-engine-feel-api:7.23.0 (*)
|    |    |         |    +--- org.camunda.feel:feel-engine:1.19.1 (*)
|    |    |         |    \--- org.camunda.bpm.juel:camunda-juel:7.23.0
|    |    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    |         +--- org.camunda.commons:camunda-commons-typed-values:7.23.0 (*)
|    |    |         +--- org.mybatis:mybatis:3.5.15
|    |    |         +--- org.springframework:spring-beans:6.2.4 -> 6.1.15 (*)
|    |    |         \--- joda-time:joda-time:2.12.5 -> 2.12.7
|    |    +--- org.springframework:spring-context:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-jdbc:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    +--- org.springframework:spring-tx:6.2.5 -> 6.1.15 (*)
|    |    +--- org.springframework:spring-orm:6.2.5 -> 6.1.15
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    |    \--- org.springframework:spring-tx:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.4.4 -> 3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter:3.4.4 -> 3.3.6 (*)
|    |    +--- org.apache.commons:commons-lang3:3.17.0 -> 3.14.0
|    |    +--- com.fasterxml.uuid:java-uuid-generator:4.3.0 -> 5.1.0 (*)
|    |    \--- com.sun.xml.bind:jaxb-impl:4.0.5
|    |         \--- com.sun.xml.bind:jaxb-core:4.0.5
|    |              +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2
|    |              |    \--- jakarta.activation:jakarta.activation-api:2.1.3
|    |              \--- org.eclipse.angus:angus-activation:2.0.2
|    |                   \--- jakarta.activation:jakarta.activation-api:2.1.3
|    +--- org.camunda.bpm:camunda-engine -> 7.23.0 (*)
|    +--- com.alibaba.nacos:logback-adapter -> 1.1.3
|    \--- org.slf4j:slf4j-api -> 2.0.16
+--- project :camunda-pulsar-connector-plugin
|    \--- org.springframework.boot:spring-boot-starter-web -> 3.3.6
|         +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         +--- org.springframework.boot:spring-boot-starter-json:3.3.6
|         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|         |    +--- org.springframework:spring-web:6.1.15
|         |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|         |    |    +--- org.springframework:spring-core:6.1.15 (*)
|         |    |    \--- io.micrometer:micrometer-observation:1.12.12 -> 1.13.8 (*)
|         |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3
|         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         |    \--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.3
|         |         +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|         |         +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|         |         \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6
|         |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|         |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|         |    \--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.33
|         |         \--- org.apache.tomcat.embed:tomcat-embed-core:10.1.33
|         +--- org.springframework:spring-web:6.1.15 (*)
|         \--- org.springframework:spring-webmvc:6.1.15
|              +--- org.springframework:spring-aop:6.1.15 (*)
|              +--- org.springframework:spring-beans:6.1.15 (*)
|              +--- org.springframework:spring-context:6.1.15 (*)
|              +--- org.springframework:spring-core:6.1.15 (*)
|              +--- org.springframework:spring-expression:6.1.15 (*)
|              \--- org.springframework:spring-web:6.1.15 (*)
+--- project :camunda-bpm-service
|    +--- project :camunda-bpm-api
|    |    +--- com.open-care:api-jsonschema-definition -> 3.4.7
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.1.1 -> 4.2.0
|    |    |    |    +--- org.springframework.cloud:spring-cloud-starter:4.2.0 -> 4.1.2
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    |    |    \--- org.springframework.security:spring-security-rsa:1.1.2
|    |    |    |    |         \--- org.bouncycastle:bcprov-jdk18on:1.77 -> 1.78.1
|    |    |    |    +--- org.springframework.cloud:spring-cloud-openfeign-core:4.2.0 -> 4.1.1
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.2.4 -> 3.3.6 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.4 -> 3.3.6
|    |    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    |    \--- org.aspectj:aspectjweaver:********
|    |    |    |    |    +--- io.github.openfeign.form:feign-form-spring:3.8.0
|    |    |    |    |    |    +--- io.github.openfeign.form:feign-form:3.8.0
|    |    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    |    +--- org.springframework:spring-web:5.1.5.RELEASE -> 6.1.15 (*)
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.26 -> 2.0.16
|    |    |    |    |    \--- commons-fileupload:commons-fileupload:1.5
|    |    |    |    |         \--- commons-io:commons-io:2.11.0 -> 2.16.1
|    |    |    |    +--- org.springframework:spring-web:6.2.0 -> 6.1.15 (*)
|    |    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.2.0 -> 4.1.2 (*)
|    |    |    |    +--- io.github.openfeign:feign-core:13.5 -> 13.2.1
|    |    |    |    \--- io.github.openfeign:feign-slf4j:13.5 -> 13.2.1
|    |    |    |         +--- io.github.openfeign:feign-core:13.2.1
|    |    |    |         \--- org.slf4j:slf4j-api:2.0.12 -> 2.0.16
|    |    |    +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-revised:1.3120.20220318T16
|    |    |    |    |    |    \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    \--- com.open-care:method-name-getter:1.3120.20220318T16
|    |    |    |    \--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16
|    |    |    |         \--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT
|    |    |    |         |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    |         |    \--- com.google.code.gson:gson:2.8.5 -> 2.10.1
|    |    |    |         +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |         +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |         \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    \--- com.open-care:api-jsonschema-definition-common:3.4.7
|    |    |         +--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    |         \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- cn.hutool:hutool-all -> 5.8.27
|    |    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    |    \--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
|    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2
|    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-core:2.1.2
|    |    |    +--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-eventbus:2.1.2
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.1 -> 1.0.4
|    |    |    |    +--- org.eclipse.collections:eclipse-collections:9.0.0
|    |    |    |    |    \--- org.eclipse.collections:eclipse-collections-api:9.0.0
|    |    |    |    +--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.8.10 -> 2.17.3 (*)
|    |    |    |    +--- com.lmax:disruptor:3.3.7
|    |    |    |    +--- io.vavr:vavr:0.9.2
|    |    |    |    |    \--- io.vavr:vavr-match:0.9.2
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    |    \--- org.slf4j:slf4j-api:1.7.25 -> 2.0.16
|    +--- org.camunda.bpm:camunda-engine-plugin-spin -> 7.23.0
|    |    \--- org.camunda.spin:camunda-spin-core:7.23.0
|    |         +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |         \--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    +--- org.camunda.spin:camunda-spin-dataformat-json-jackson -> 7.23.0
|    |    +--- org.camunda.spin:camunda-spin-core:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.camunda.commons:camunda-commons-utils:7.23.0 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-core:2.15.2 -> 2.17.3 (*)
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    \--- com.jayway.jsonpath:json-path:2.9.0
|    |         +--- net.minidev:json-smart:2.5.0 -> 2.5.1
|    |         |    \--- net.minidev:accessors-smart:2.5.1
|    |         |         \--- org.ow2.asm:asm:9.6
|    |         \--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0
|    |    |    +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |    |    +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.3
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6
|    |         +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.6 (*)
|    |         +--- org.springframework.boot:spring-boot-starter-validation:3.3.6
|    |         |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |         |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.33
|    |         |    \--- org.hibernate.validator:hibernate-validator:8.0.1.Final
|    |         |         +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |         +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |         |         \--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |         +--- org.springframework:spring-web:6.1.15 (*)
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- org.glassfish.hk2:osgi-resource-locator:1.0.3
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.9
|    |         |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1
|    |         |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    \--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.9
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.9
|    |         |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |         |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |         |    +--- org.jboss.logging:jboss-logging:3.6.0.Final -> 3.5.3.Final
|    |         |    +--- org.glassfish.expressly:expressly:5.0.0
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         +--- org.glassfish.jersey.ext:jersey-spring6:3.1.9
|    |         |    +--- org.glassfish.jersey.core:jersey-server:3.1.9 (*)
|    |         |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.9
|    |         |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6
|    |         |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    +--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6
|    |         |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6
|    |         |    |    |    |    |    \--- jakarta.inject:jakarta.inject-api:2.0.1
|    |         |    |    |    |    \--- org.glassfish.hk2.external:aopalliance-repackaged:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    |    \--- org.javassist:javassist:3.30.2-GA
|    |         |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.9 (*)
|    |         |    +--- org.glassfish.hk2:hk2:3.0.6
|    |         |    |    +--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-core:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-utils:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    +--- org.glassfish.hk2:hk2-runlevel:3.0.6
|    |         |    |    |    +--- org.glassfish.hk2:hk2-api:3.0.6 (*)
|    |         |    |    |    \--- org.glassfish.hk2:hk2-locator:3.0.6 (*)
|    |         |    |    \--- org.glassfish.hk2:class-model:3.0.6
|    |         |    |         \--- org.ow2.asm:asm-commons:9.6
|    |         |    +--- org.glassfish.hk2:spring-bridge:3.0.6
|    |         |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |         \--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.9
|    |              +--- org.glassfish.jersey.core:jersey-common:3.1.9 (*)
|    |              +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.9
|    |              |    \--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0
|    |              +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |              +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (*)
|    |              \--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta -> 7.23.0 (*)
|    +--- org.camunda.bpm:camunda-engine-rest:7.23.0
|    |    \--- org.camunda.bpm:camunda-engine-rest-core:7.23.0
|    |         +--- commons-fileupload:commons-fileupload:1.5 (*)
|    |         +--- commons-io:commons-io:2.17.0 -> 2.16.1
|    |         +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.15.2 -> 2.17.3
|    |         |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.3
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |         |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |         |    |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         |    \--- com.fasterxml.jackson:jackson-bom:2.17.3 (*)
|    |         +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |         \--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    +--- jakarta.ws.rs:jakarta.ws.rs-api -> 3.1.0
|    +--- org.springframework.boot:spring-boot-starter-data-jpa -> 3.3.6
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- com.zaxxer:HikariCP:5.1.0
|    |    |    |    \--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    \--- org.springframework:spring-jdbc:6.1.15 (*)
|    |    +--- org.hibernate.orm:hibernate-core:6.5.3.Final
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final
|    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final
|    |    |    +--- io.smallrye:jandex:3.1.2
|    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (*)
|    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5
|    |    |    |    \--- org.glassfish.jaxb:jaxb-core:4.0.5
|    |    |    |         +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
|    |    |    |         +--- jakarta.activation:jakarta.activation-api:2.1.3
|    |    |    |         +--- org.eclipse.angus:angus-activation:2.0.2 (*)
|    |    |    |         +--- org.glassfish.jaxb:txw2:4.0.5
|    |    |    |         \--- com.sun.istack:istack-commons-runtime:4.1.2
|    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1
|    |    |    \--- org.antlr:antlr4-runtime:4.13.0
|    |    +--- org.springframework.data:spring-data-jpa:3.3.6
|    |    |    +--- org.springframework.data:spring-data-commons:3.3.6
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |    +--- org.springframework:spring-orm:6.1.15 (*)
|    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    +--- org.antlr:antlr4-runtime:4.13.0
|    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.0.0 -> 2.1.1
|    |    |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    \--- org.springframework:spring-aspects:6.1.15
|    |         \--- org.aspectj:aspectjweaver:********
|    +--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
|    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2
|    |    +--- org.springframework.cloud:spring-cloud-starter:4.1.2 (*)
|    |    +--- org.springframework.cloud:spring-cloud-loadbalancer:4.1.2
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- org.springframework.cloud:spring-cloud-context:4.1.2 (*)
|    |    |    +--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12
|    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.4
|    |    |    \--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2
|    |    |         \--- io.projectreactor:reactor-core:3.5.20 -> 3.6.12 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-cache:3.2.4 -> 3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    \--- org.springframework:spring-context-support:6.1.15
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         \--- org.springframework:spring-core:6.1.15 (*)
|    |    \--- com.stoyanr:evictor:1.0.0
|    +--- org.springframework.boot:spring-boot-configuration-processor -> 3.3.6
|    +--- org.springframework.retry:spring-retry -> 2.0.10
|    +--- jakarta.persistence:jakarta.persistence-api -> 3.1.0
|    +--- jakarta.servlet:jakarta.servlet-api -> 6.0.0
|    +--- jakarta.annotation:jakarta.annotation-api -> 2.1.1
|    +--- jakarta.validation:jakarta.validation-api -> 3.0.2
|    +--- org.hibernate:hibernate-core -> 5.6.15.Final
|    |    +--- org.jboss.logging:jboss-logging:3.4.3.Final -> 3.5.3.Final
|    |    +--- javax.persistence:javax.persistence-api:2.2
|    |    +--- net.bytebuddy:byte-buddy:1.12.18 -> 1.14.19
|    |    +--- antlr:antlr:2.7.7
|    |    +--- org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec:1.1.1.Final
|    |    +--- org.jboss:jandex:2.4.2.Final
|    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0
|    |    +--- javax.activation:javax.activation-api:1.2.0
|    |    +--- org.hibernate.common:hibernate-commons-annotations:5.1.2.Final -> 6.0.6.Final
|    |    +--- javax.xml.bind:jaxb-api:2.3.1 -> 2.2.7
|    |    \--- org.glassfish.jaxb:jaxb-runtime:2.3.1 -> 4.0.5 (*)
|    +--- org.dom4j:dom4j -> 2.1.4
|    |    +--- jaxen:jaxen:1.1.6 -> 2.0.0
|    |    +--- javax.xml.stream:stax-api:1.0-2
|    |    +--- net.java.dev.msv:xsdlib:2013.6.1
|    |    |    \--- relaxngDatatype:relaxngDatatype:20020414
|    |    +--- javax.xml.bind:jaxb-api:2.2.12 -> 2.2.7
|    |    +--- pull-parser:pull-parser:2.1.10
|    |    \--- xpp3:xpp3:1.1.4c
|    +--- com.open-care:open-care-json-converter -> 1.286.20240806-SNAPSHOT-JSOG (*)
|    +--- com.open-care:api-jsonschema-definition -> 3.4.7 (*)
|    +--- com.open-care:open-care-core-util -> 2.7
|    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (*)
|    |    \--- org.apache.httpcomponents:httpcore:4.4.16
|    +--- com.open-care:open-care-common-util -> 3.5-SNAPSHOT
|    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    +--- com.open-care:open-care-common-util-core:3.5-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- com.open-care:api-jsonschema-definition-common:3.4.7 (*)
|    |    |    +--- com.open-care:open-care-core-component:2.7
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (*)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    |    \--- org.projectlombok:lombok:1.18.34 -> 1.18.36
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    \--- com.google.code.gson:gson:2.8.6-SNAPSHOT-JSOG -> 2.10.1
|    |    +--- com.open-care:open-care-core-component:2.7 (*)
|    |    +--- com.open-care:open-care-core-util:2.7 (*)
|    |    +--- org.springframework.cloud:spring-cloud-starter-openfeign:4.2.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.5
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |    |    |    \--- io.micrometer:micrometer-observation:1.12.13 -> 1.13.8 (*)
|    |    |    |    +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |    |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-web:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         +--- org.springframework:spring-expression:6.1.15 (*)
|    |    |         \--- org.springframework:spring-web:6.1.15 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    |    \--- org.springframework:spring-web:6.1.15 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5
|    |    |         +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |         +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |         +--- org.springframework:spring-core:6.1.15 (*)
|    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4
|    |    |              \--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- org.springframework.security:spring-security-config:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.5
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.5 (*)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.5 (*)
|    |    |    |    +--- org.springframework:spring-core:6.1.15 (*)
|    |    |    |    \--- com.nimbusds:oauth2-oidc-sdk:9.43.4
|    |    |    |         +--- com.github.stephenc.jcip:jcip-annotations:1.0-1
|    |    |    |         +--- com.nimbusds:content-type:2.2
|    |    |    |         +--- net.minidev:json-smart:[1.3.3,2.4.10] -> 2.5.1 (*)
|    |    |    |         +--- com.nimbusds:lang-tag:1.7
|    |    |    |         \--- com.nimbusds:nimbus-jose-jwt:9.37.3 -> 9.24.4 (*)
|    |    |    \--- org.springframework.security:spring-security-oauth2-jose:6.3.5 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.6
|    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE
|    |    |    |    +--- io.netty:netty-common:4.1.107.Final -> 4.1.115.Final
|    |    |    |    +--- io.netty:netty-handler:4.1.107.Final -> 4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final
|    |    |    |    |    |    \--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    +--- io.netty:netty-transport:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-resolver:4.1.115.Final (*)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |    |    \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    |    \--- io.netty:netty-codec:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-common:4.1.115.Final
|    |    |    |    |         +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |    |    |    |         \--- io.netty:netty-transport:4.1.115.Final (*)
|    |    |    |    +--- io.netty:netty-transport:4.1.107.Final -> 4.1.115.Final (*)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.4 -> 3.6.12 (*)
|    |    |    \--- org.springframework.data:spring-data-redis:3.3.6
|    |    |         +--- org.springframework.data:spring-data-keyvalue:3.3.6
|    |    |         |    +--- org.springframework.data:spring-data-commons:3.3.6 (*)
|    |    |         |    +--- org.springframework:spring-context:6.1.15 (*)
|    |    |         |    +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         |    \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    |         +--- org.springframework:spring-tx:6.1.15 (*)
|    |    |         +--- org.springframework:spring-oxm:6.1.15
|    |    |         |    +--- jakarta.xml.bind:jakarta.xml.bind-api:3.0.1 -> 4.0.2 (*)
|    |    |         |    +--- org.springframework:spring-beans:6.1.15 (*)
|    |    |         |    \--- org.springframework:spring-core:6.1.15 (*)
|    |    |         +--- org.springframework:spring-aop:6.1.15 (*)
|    |    |         +--- org.springframework:spring-context-support:6.1.15 (*)
|    |    |         \--- org.slf4j:slf4j-api:2.0.2 -> 2.0.16
|    |    +--- org.springframework.cloud:spring-cloud-starter-loadbalancer:4.1.2 (*)
|    |    +--- com.open-care:open-care-gson:2.8.6-SNAPSHOT
|    |    +--- com.googlecode.aviator:aviator:4.2.0
|    |    |    \--- commons-beanutils:commons-beanutils:1.9.3
|    |    |         +--- commons-logging:commons-logging:1.2 -> 1.3.4
|    |    |         \--- commons-collections:commons-collections:3.2.2
|    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    +--- io.projectreactor.addons:reactor-extra:3.4.6 -> 3.5.2 (*)
|    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    +--- org.apache.commons:commons-collections4:4.5.0-M1
|    |    +--- commons-beanutils:commons-beanutils:1.9.3 (*)
|    |    +--- org.apache.commons:commons-pool2:2.12.0
|    |    +--- commons-dbutils:commons-dbutils:1.7
|    |    +--- commons-io:commons-io:2.16.1
|    |    +--- commons-lang:commons-lang:2.6
|    |    +--- org.apache.httpcomponents:httpclient:4.5.13 (*)
|    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.33
|    |    |    \--- org.apache.tomcat:tomcat-juli:10.1.33
|    |    +--- org.apache.poi:poi:4.1.0
|    |    |    +--- commons-codec:commons-codec:1.12 -> 1.16.1
|    |    |    +--- org.apache.commons:commons-collections4:4.3 -> 4.5.0-M1
|    |    |    \--- org.apache.commons:commons-math3:3.6.1
|    |    +--- org.apache.poi:poi-ooxml:4.1.2
|    |    |    +--- org.apache.poi:poi:4.1.2 -> 4.1.0 (*)
|    |    |    +--- org.apache.poi:poi-ooxml-schemas:4.1.2
|    |    |    |    \--- org.apache.xmlbeans:xmlbeans:3.1.0
|    |    |    +--- org.apache.commons:commons-compress:1.19 -> 1.27.1
|    |    |    |    +--- commons-codec:commons-codec:1.17.1 -> 1.16.1
|    |    |    |    +--- commons-io:commons-io:2.16.1
|    |    |    |    \--- org.apache.commons:commons-lang3:3.16.0 -> 3.14.0
|    |    |    \--- com.github.virtuald:curvesapi:1.06
|    |    +--- org.apache.pdfbox:pdfbox:3.0.2
|    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    +--- org.apache.pdfbox:fontbox:3.0.2
|    |    |    |    +--- org.apache.pdfbox:pdfbox-io:3.0.2 (*)
|    |    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    |    \--- commons-logging:commons-logging:1.3.0 -> 1.3.4
|    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG
|    |    |    +--- joda-time:joda-time:2.6 -> 2.12.7
|    |    |    +--- com.open-care:gson-jodatime-serialisers:1.5.0-SNAPSHOT -> 1.6.0-SNAPSHOT (*)
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    +--- org.hibernate.orm:hibernate-core:6.4.4.Final -> 6.5.3.Final (*)
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.2.0 -> 3.3.2
|    |    |    |    +--- org.assertj:assertj-core:3.25.3 (c)
|    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    +--- org.junit.jupiter:junit-jupiter:5.10.3 -> 5.10.5 (c)
|    |    |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.3 -> 5.10.5 (c)
|    |    |    |    +--- org.junit.jupiter:junit-jupiter-engine:5.10.3 -> 5.10.5 (c)
|    |    |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.3 -> 5.10.5 (c)
|    |    |    |    +--- org.junit.platform:junit-platform-commons:1.10.3 -> 1.10.5 (c)
|    |    |    |    +--- org.junit.platform:junit-platform-engine:1.10.3 -> 1.10.5 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-core:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    +--- org.mockito:mockito-core:5.11.0 (c)
|    |    |    |    +--- org.mockito:mockito-junit-jupiter:5.11.0 (c)
|    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-test:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    +--- org.awaitility:awaitility:4.2.1 -> 4.2.2 (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.18 -> 1.14.19 (c)
|    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    +--- com.fasterxml:classmate:1.7.0 (c)
|    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    +--- org.hamcrest:hamcrest:2.2 (c)
|    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    +--- com.zaxxer:HikariCP:5.1.0 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.3.Final (c)
|    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    +--- org.skyscreamer:jsonassert:1.5.3 (c)
|    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-test:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-test:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    +--- org.xmlunit:xmlunit-core:2.9.1 (c)
|    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    \--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    +--- com.open-care:api-jsonschema-definition:3.4.7 (*)
|    |    +--- com.github.behaim:behaim:2.0.20240807
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- ch.qos.logback:logback-classic:0.9.21 -> 1.5.12 (*)
|    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    +--- com.google.guava:guava:31.1-jre -> 20.0
|    |    |    \--- com.open-care:open-care-generics-utils:1.0.20230118T16
|    |    |         +--- org.slf4j:slf4j-api:1.7.13 -> 2.0.16
|    |    |         \--- com.googlecode.gentyref:gentyref:1.2.0
|    |    +--- com.github.behaim:behaim_with-hibernate-proxy:2.0.20240807
|    |    |    +--- org.hibernate:hibernate-core:6.1.7.Final -> 5.6.15.Final (*)
|    |    |    +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.github.behaim:behaim_target-value-converters-for-date-type:2.0.20240807
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    \--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    \--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3
|    |    |    |    |    +--- com.graphql-java:java-dataloader:3.3.0
|    |    |    |    |    |    \--- org.slf4j:slf4j-api:1.7.30 -> 2.0.16
|    |    |    |    |    \--- org.reactivestreams:reactive-streams:1.0.3 -> 1.0.4
|    |    |    |    \--- com.graphql-java:graphql-java-extended-scalars:22.0
|    |    |    |         \--- com.graphql-java:graphql-java:22.0 -> 22.3 (*)
|    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (*)
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    |    \--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (*)
|    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    +--- com.graphql-java:graphql-java:22.3 (*)
|    |    +--- org.atteo:evo-inflector:1.2.2
|    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (*)
|    |    +--- com.open-care:graphql-jpa-mutation-autoconfigure:1.2.8.20240819T17
|    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6
|    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.6 (*)
|    |    |    |    \--- org.springframework.graphql:spring-graphql:1.3.3
|    |    |    |         +--- io.micrometer:context-propagation:1.1.2
|    |    |    |         +--- com.graphql-java:graphql-java:22.3 (*)
|    |    |    |         +--- io.projectreactor:reactor-core:3.6.11 -> 3.6.12 (*)
|    |    |    |         \--- org.springframework:spring-context:6.1.14 -> 6.1.15 (*)
|    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (*)
|    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    +--- com.open-care:open-care-json-gson-hibernateproxy:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10
|    |    |    |    +--- org.hibernate:hibernate-core:6.4.4.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    \--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10
|    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |         \--- org.jgrapht:jgrapht-core:1.3.0
|    |    |    |              \--- org.jheaps:jheaps:0.9
|    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:apache-commons-lang3-additional:1.3120.20220318T16 (*)
|    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    +--- com.github.behaim:behaim_visitor-to-copy-collection-to-jpa-entity-object:2.0.20240807 (*)
|    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    +--- com.open-care:interceptor-for-code-manipulation-on-graphql-jpa-query-schema:1.2.8.20240819T17
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.18 -> 1.14.19
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    |    +--- com.open-care:apache-commons-lang3-wrapper:1.3120.20220318T16 (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (*)
|    |    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |    \--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17
|    |    |    |    |         \--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    \--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8
|    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (*)
|    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16
|    |    |    |    +--- joda-time:joda-time:2.12.7
|    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1
|    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0
|    |    |    |    |    \--- jakarta.annotation:jakarta.annotation-api:3.0.0 -> 2.1.1
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-graphql-context:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    |    \--- org.atteo:evo-inflector:1.3 -> 1.2.2
|    |    |    |    +--- com.open-care:graphql-jpa-query-schema-code-manipulated:1.2.8.20240819T17
|    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8
|    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    +--- com.introproventures:graphql-jpa-query-schema:1.2.8
|    |    |    |    |    +--- com.graphql-java:graphql-java:22.1 -> 22.3 (c)
|    |    |    |    |    +--- com.graphql-java:graphql-java-extended-scalars:22.0 (c)
|    |    |    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0 (c)
|    |    |    |    |    +--- org.atteo:evo-inflector:1.3 -> 1.2.2 (c)
|    |    |    |    |    +--- jakarta.interceptor:jakarta.interceptor-api:2.2.0 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-annotations:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-scalars:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-autoconfigure:1.2.8 (c)
|    |    |    |    |    +--- com.introproventures:graphql-jpa-query-introspection:1.2.8 (c)
|    |    |    |    |    +--- joda-time:joda-time:2.12.7 (c)
|    |    |    |    |    +--- org.hibernate.orm:hibernate-core:6.5.2.Final -> 6.5.3.Final (c)
|    |    |    |    |    +--- org.hibernate.common:hibernate-commons-annotations:6.0.6.Final (c)
|    |    |    |    |    +--- jakarta.transaction:jakarta.transaction-api:2.0.1 (c)
|    |    |    |    |    +--- org.antlr:antlr4:4.13.0 (c)
|    |    |    |    |    +--- org.jboss.logging:jboss-logging:3.5.0.Final -> 3.5.3.Final (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- net.bytebuddy:byte-buddy-agent:1.14.15 -> 1.14.19 (c)
|    |    |    |    |    +--- io.smallrye:jandex:3.1.2 (c)
|    |    |    |    |    +--- com.fasterxml:classmate:1.5.1 -> 1.7.0 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.2 -> 4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.0 -> 4.0.2 (c)
|    |    |    |    |    +--- jakarta.inject:jakarta.inject-api:2.0.1 (c)
|    |    |    |    |    +--- com.zaxxer:HikariCP:3.2.0 -> 5.1.0 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-core:1.10.4 -> 1.13.8 (c)
|    |    |    |    |    +--- org.assertj:assertj-core:3.25.3 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-runtime:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- org.glassfish.jaxb:txw2:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-impl:4.0.5 (c)
|    |    |    |    |    +--- com.sun.xml.bind:jaxb-core:4.0.5 (c)
|    |    |    |    |    +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (c)
|    |    |    |    |    +--- com.sun.istack:istack-commons-runtime:4.1.2 (c)
|    |    |    |    |    +--- jakarta.activation:jakarta.activation-api:2.1.3 (c)
|    |    |    |    |    +--- org.eclipse.angus:angus-activation:2.0.2 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-base:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- com.fasterxml.jackson.module:jackson-module-parameter-names:2.17.2 -> 2.17.3 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-common:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-client:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.core:jersey-server:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.containers:jersey-container-servlet-core:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-bean-validation:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-entity-filtering:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.ext:jersey-spring6:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.media:jersey-media-json-jackson:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.glassfish.jersey.inject:jersey-hk2:3.1.7 -> 3.1.9 (c)
|    |    |    |    |    +--- org.junit.jupiter:junit-jupiter:5.10.3 -> 5.10.5 (c)
|    |    |    |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.3 -> 5.10.5 (c)
|    |    |    |    |    +--- org.junit.jupiter:junit-jupiter-engine:5.10.3 -> 5.10.5 (c)
|    |    |    |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.3 -> 5.10.5 (c)
|    |    |    |    |    +--- org.junit.platform:junit-platform-commons:1.10.3 -> 1.10.5 (c)
|    |    |    |    |    +--- org.junit.platform:junit-platform-engine:1.10.3 -> 1.10.5 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-api:2.23.1 (c)
|    |    |    |    |    +--- org.apache.logging.log4j:log4j-to-slf4j:2.23.1 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-commons:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-jakarta9:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- io.micrometer:micrometer-observation:1.13.2 -> 1.13.8 (c)
|    |    |    |    |    +--- org.mockito:mockito-core:5.11.0 (c)
|    |    |    |    |    +--- org.mockito:mockito-junit-jupiter:5.11.0 (c)
|    |    |    |    |    +--- io.netty:netty-buffer:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-haproxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-http2:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-memcache:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-mqtt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-redis:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-smtp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-socks:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-stomp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-codec-xml:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-proxy:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-handler-ssl-ocsp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-rxtx:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-sctp:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-udt:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-all:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-resolver-dns-native-macos:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-unix-common:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-epoll:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-classes-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.netty:netty-transport-native-kqueue:4.1.111.Final -> 4.1.115.Final (c)
|    |    |    |    |    +--- io.prometheus:simpleclient:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel:0.16.0 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_otel_agent:0.16.0 (c)
|    |    |    |    |    +--- org.reactivestreams:reactive-streams:1.0.4 (c)
|    |    |    |    |    +--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (c)
|    |    |    |    |    +--- io.projectreactor.addons:reactor-extra:3.5.1 -> 3.5.2 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-commons:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.data:spring-data-keyvalue:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework:spring-aop:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-aspects:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-beans:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-context-support:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-core:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-expression:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jcl:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-jdbc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-orm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-oxm:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-test:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-web:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework:spring-webmvc:6.1.11 -> 6.1.15 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-config:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-crypto:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-client:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-core:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-jose:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-oauth2-resource-server:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.springframework.security:spring-security-web:6.3.1 -> 6.3.5 (c)
|    |    |    |    |    +--- org.aspectj:aspectjweaver:******** (c)
|    |    |    |    |    +--- org.awaitility:awaitility:4.2.1 -> 4.2.2 (c)
|    |    |    |    |    +--- com.github.ben-manes.caffeine:caffeine:3.1.8 (c)
|    |    |    |    |    +--- commons-codec:commons-codec:1.16.1 (c)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0 (c)
|    |    |    |    |    +--- commons-pool:commons-pool:1.6 (c)
|    |    |    |    |    +--- org.apache.commons:commons-pool2:2.12.0 (c)
|    |    |    |    |    +--- com.google.code.gson:gson:2.10.1 (c)
|    |    |    |    |    +--- org.hamcrest:hamcrest:2.2 (c)
|    |    |    |    |    +--- org.hibernate.validator:hibernate-validator:8.0.1.Final (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpasyncclient:4.1.5 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore:4.4.16 (c)
|    |    |    |    |    +--- org.apache.httpcomponents:httpcore-nio:4.4.16 (c)
|    |    |    |    |    +--- jakarta.annotation:jakarta.annotation-api:2.1.1 (c)
|    |    |    |    |    +--- jakarta.servlet:jakarta.servlet-api:6.0.0 (c)
|    |    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2 (c)
|    |    |    |    |    +--- jakarta.ws.rs:jakarta.ws.rs-api:3.1.0 (c)
|    |    |    |    |    +--- jaxen:jaxen:2.0.0 (c)
|    |    |    |    |    +--- com.jayway.jsonpath:json-path:2.9.0 (c)
|    |    |    |    |    +--- net.minidev:json-smart:2.5.1 (c)
|    |    |    |    |    +--- org.skyscreamer:jsonassert:1.5.3 (c)
|    |    |    |    |    +--- io.lettuce:lettuce-core:6.3.2.RELEASE (c)
|    |    |    |    |    +--- ch.qos.logback:logback-classic:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- ch.qos.logback:logback-core:1.5.6 -> 1.5.12 (c)
|    |    |    |    |    +--- org.projectlombok:lombok:1.18.34 -> 1.18.36 (c)
|    |    |    |    |    +--- com.mysql:mysql-connector-j:8.3.0 (c)
|    |    |    |    |    +--- org.postgresql:postgresql:42.7.3 -> 42.7.4 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-test:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-autoconfigure:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-configuration-processor:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-actuator:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-aop:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-cache:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-data-redis:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-graphql:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jdbc:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-jersey:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-json:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-logging:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-client:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-security:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-test:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-tomcat:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-validation:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (c)
|    |    |    |    |    +--- org.slf4j:jul-to-slf4j:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.13 -> 2.0.16 (c)
|    |    |    |    |    +--- org.yaml:snakeyaml:2.2 (c)
|    |    |    |    |    +--- org.springframework.graphql:spring-graphql:1.3.2 -> 1.3.3 (c)
|    |    |    |    |    +--- org.springframework.retry:spring-retry:2.0.7 -> 2.0.10 (c)
|    |    |    |    |    +--- org.apache.tomcat:tomcat-jdbc:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-core:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-el:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.apache.tomcat.embed:tomcat-embed-websocket:10.1.26 -> 10.1.33 (c)
|    |    |    |    |    +--- org.xmlunit:xmlunit-core:2.9.1 (c)
|    |    |    |    |    +--- io.prometheus:simpleclient_tracer_common:0.16.0 (c)
|    |    |    |    |    \--- io.micrometer:context-propagation:1.1.1 -> 1.1.2 (c)
|    |    |    |    \--- io.projectreactor:reactor-core:3.6.8 -> 3.6.12 (*)
|    |    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17
|    |    |    |    +--- org.springframework.boot:spring-boot-dependencies:3.3.2 (*)
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.2 -> 3.3.6 (*)
|    |    |    |    +--- org.springframework:spring-tx:6.1.11 -> 6.1.15 (*)
|    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    +--- org.apache.commons:commons-lang3:3.14.0
|    |    |    |    +--- com.open-care:open-care-bean:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-gson:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:gson-jodatime-serialisers:1.6.0-SNAPSHOT (*)
|    |    |    |    +--- com.google.code.gson:gson:{strictly 2.8.6-SNAPSHOT-JSOG} -> 2.10.1
|    |    |    |    +--- com.open-care:open-care-json-converter:1.286.20240806-SNAPSHOT-JSOG (*)
|    |    |    |    +--- com.open-care:open-care-json-api:1.286.20240806-SNAPSHOT-JSOG
|    |    |    |    +--- com.introproventures:graphql-jpa-query-dependencies:1.2.8 (*)
|    |    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    |    |    +--- com.open-care:graphql-query-transformer-with-supporting-managedtype:1.2.8.20240819T17
|    |    |    |    |    +--- com.open-care:open-care-class-hierarchy-model-with-supporting-managedtype:1.0.20240508T10 (*)
|    |    |    |    |    +--- org.hibernate:hibernate-core:6.5.2.Final -> 5.6.15.Final (*)
|    |    |    |    |    +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |    +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |    +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |    \--- com.open-care:graphql-query-transformer:1.2.8.20240819T17
|    |    |    |    |         +--- com.open-care:open-care-class-hierarchy-model:1.0.20240508T10 (*)
|    |    |    |    |         +--- com.github.behaim:behaim:2.0.20240807 (*)
|    |    |    |    |         +--- org.apache.commons:commons-lang3:3.12.0 -> 3.14.0
|    |    |    |    |         +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    |    |    |         +--- org.slf4j:slf4j-api:2.0.11 -> 2.0.16
|    |    |    |    |         +--- com.graphql-java:graphql-java:22.1 -> 22.3 (*)
|    |    |    |    |         +--- com.open-care:open-care-generics-utils:1.0.20230118T16 (*)
|    |    |    |    |         +--- com.open-care:graphql-jpa-mutation-consts:1.2.8.20240819T17
|    |    |    |    |         \--- com.open-care:graphql-jpa-mutation-common-interfaces:1.2.8.20240819T17 (*)
|    |    |    |    \--- com.open-care:graphql-query-result-general-model-common:1.2.8.20240819T17
|    |    |    \--- com.open-care:graphql-jpa-query-autoconfigure-code-manipulated:1.2.8.20240819T17
|    |    +--- com.open-care:graphql-jpa-mutation-schema:1.2.8.20240819T17 (*)
|    |    +--- com.open-care:graphql-jpa-mutation-web:1.2.8.20240819T17 (*)
|    |    +--- com.jayway.jsonpath:json-path:2.9.0 (*)
|    |    +--- org.crazycake:camel-name-utils:1.0.0-RELEASE
|    |    +--- com.alibaba:druid:1.2.22
|    |    +--- com.open-care:kingbase-hibernate-dialect:6.2
|    |    +--- io.github.kostaskougios:cloning:1.10.3
|    |    |    \--- org.objenesis:objenesis:3.0.1 -> 3.3
|    |    +--- org.reflections:reflections:0.10.2
|    |    |    +--- org.javassist:javassist:3.28.0-GA -> 3.30.2-GA
|    |    |    +--- com.google.code.findbugs:jsr305:3.0.2
|    |    |    \--- org.slf4j:slf4j-api:1.7.32 -> 2.0.16
|    |    +--- com.github.jsqlparser:jsqlparser:1.2
|    |    +--- com.mysql:mysql-connector-j:8.3.0
|    |    +--- org.postgresql:postgresql:42.7.4
|    |    |    \--- org.checkerframework:checker-qual:3.42.0
|    |    +--- org.yaml:snakeyaml:1.26 -> 2.2
|    |    +--- com.ibm.icu:icu4j:71.1 -> 72.1
|    |    +--- com.google.code.findbugs:annotations:3.0.1
|    |    |    +--- net.jcip:jcip-annotations:1.0
|    |    |    \--- com.google.code.findbugs:jsr305:3.0.1 -> 3.0.2
|    |    +--- com.open-care:auth-core:1.0-SNAPSHOT
|    |    |    +--- org.mapstruct:mapstruct:1.6.3
|    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.3.6 (*)
|    |    |    +--- cn.hutool:hutool-all:5.8.27
|    |    |    +--- org.bouncycastle:bcprov-jdk15to18:1.78.1
|    |    |    +--- cn.dev33:sa-token-spring-boot3-starter:1.40.0
|    |    |    |    +--- org.springframework.boot:spring-boot-starter-web:3.0.1 -> 3.3.6 (*)
|    |    |    |    +--- cn.dev33:sa-token-jakarta-servlet:1.40.0
|    |    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    |    \--- jakarta.servlet:jakarta.servlet-api:6.0.0
|    |    |    |    \--- cn.dev33:sa-token-spring-boot-autoconfig:1.40.0
|    |    |    +--- cn.dev33:sa-token-spring-aop:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- org.springframework.boot:spring-boot-starter-aop:2.5.15 -> 3.3.6 (*)
|    |    |    +--- cn.dev33:sa-token-sso:1.40.0
|    |    |    |    \--- cn.dev33:sa-token-core:1.40.0
|    |    |    +--- cn.dev33:sa-token-jwt:1.40.0
|    |    |    |    +--- cn.dev33:sa-token-core:1.40.0
|    |    |    |    \--- cn.hutool:hutool-jwt:5.8.20
|    |    |    |         +--- cn.hutool:hutool-json:5.8.20
|    |    |    |         |    \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    |         \--- cn.hutool:hutool-crypto:5.8.20
|    |    |    |              \--- cn.hutool:hutool-core:5.8.20 -> 5.8.27
|    |    |    +--- com.fasterxml.jackson.core:jackson-core:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-annotations:2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    |    +--- jakarta.persistence:jakarta.persistence-api:3.1.0
|    |    |    +--- jakarta.validation:jakarta.validation-api:3.0.2
|    |    |    +--- org.springframework.boot:spring-boot-starter-data-jpa:3.3.6 (*)
|    |    |    \--- com.open-care:rpc-core:3.3.2-SNAPSHOT
|    |    |         \--- org.mapstruct:mapstruct:1.6.3
|    |    +--- cn.hutool:hutool-all:5.8.27
|    |    \--- joda-time:joda-time:2.12.7
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery -> 2023.0.1.0 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0
|    |    +--- com.alibaba.csp:sentinel-transport-simple-http:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-transport-common:1.8.8
|    |    |         +--- com.alibaba.csp:sentinel-datasource-extension:1.8.8
|    |    |         |    \--- com.alibaba.csp:sentinel-core:1.8.8
|    |    |         \--- com.alibaba:fastjson:1.2.83_noneautotype -> 1.2.83
|    |    +--- com.alibaba.csp:sentinel-annotation-aspectj:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- org.aspectj:aspectjweaver:1.9.2 -> ********
|    |    +--- com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2023.0.1.0
|    |    |    +--- org.springframework.cloud:spring-cloud-commons:4.1.2 (*)
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6
|    |    |         \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.alibaba.csp:sentinel-reactor-adapter:1.8.6 (*)
|    |    +--- com.alibaba.csp:sentinel-spring-webmvc-6x-adapter:1.8.6
|    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    \--- com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2
|    |    +--- com.alibaba.csp:sentinel-cluster-server-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6
|    |    |    |    \--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-parameter-flow-control:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    +--- com.alibaba.csp:sentinel-cluster-client-default:1.8.6
|    |    |    +--- com.alibaba.csp:sentinel-core:1.8.6 -> 1.8.8
|    |    |    +--- com.alibaba.csp:sentinel-cluster-common-default:1.8.6 (*)
|    |    |    \--- io.netty:netty-handler:4.1.48.Final -> 4.1.115.Final (*)
|    |    \--- com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2023.0.1.0
|    |         \--- com.alibaba.cloud:spring-cloud-alibaba-commons:2023.0.1.0
|    +--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7
|    |    +--- com.alibaba.csp:sentinel-datasource-extension:1.8.7 -> 1.8.8 (*)
|    |    \--- com.alibaba.nacos:nacos-client:1.4.2 -> 2.4.3 (*)
|    +--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2
|    |    +--- org.springframework.boot:spring-boot-starter-aop:3.2.9 -> 3.3.6 (*)
|    |    \--- org.apache.seata:seata-spring-boot-starter:2.1.0 -> 2.3.0
|    |         +--- org.apache.seata:seata-spring-autoconfigure-client:2.3.0
|    |         |    \--- org.apache.seata:seata-spring-autoconfigure-core:2.3.0
|    |         \--- org.apache.seata:seata-all:2.3.0
|    |              +--- org.springframework:spring-context:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-core:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-beans:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-aop:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-webmvc:5.3.39 -> 6.1.15 (*)
|    |              +--- org.springframework:spring-tx:5.3.39 -> 6.1.15 (*)
|    |              +--- io.netty:netty-all:4.1.101.Final -> 4.1.115.Final
|    |              |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-codec-haproxy:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-http2:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-memcache:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-mqtt:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-redis:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-smtp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-socks:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-stomp:4.1.115.Final
|    |              |    +--- io.netty:netty-codec-xml:4.1.115.Final
|    |              |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    +--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-handler-proxy:4.1.115.Final
|    |              |    +--- io.netty:netty-handler-ssl-ocsp:4.1.115.Final
|    |              |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-resolver:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-codec-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-handler:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-rxtx:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-sctp:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-udt:4.1.115.Final
|    |              |    +--- io.netty:netty-transport-classes-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-classes-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-resolver-dns:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-epoll:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-epoll:4.1.115.Final (*)
|    |              |    +--- io.netty:netty-transport-native-kqueue:4.1.115.Final
|    |              |    |    +--- io.netty:netty-common:4.1.115.Final
|    |              |    |    +--- io.netty:netty-buffer:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport:4.1.115.Final (*)
|    |              |    |    +--- io.netty:netty-transport-native-unix-common:4.1.115.Final (*)
|    |              |    |    \--- io.netty:netty-transport-classes-kqueue:4.1.115.Final (*)
|    |              |    \--- io.netty:netty-resolver-dns-native-macos:4.1.115.Final
|    |              |         \--- io.netty:netty-resolver-dns-classes-macos:4.1.115.Final (*)
|    |              +--- org.antlr:antlr4:4.8 -> 4.13.0
|    |              |    +--- org.antlr:antlr4-runtime:4.13.0
|    |              |    +--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.antlr:ST4:4.3.4
|    |              |    |    \--- org.antlr:antlr-runtime:3.5.3
|    |              |    +--- org.abego.treelayout:org.abego.treelayout.core:1.0.3
|    |              |    \--- com.ibm.icu:icu4j:72.1
|    |              +--- com.alibaba:fastjson:1.2.83
|    |              +--- com.alibaba:druid:1.2.20 -> 1.2.22
|    |              +--- com.typesafe:config:1.2.1
|    |              +--- org.slf4j:slf4j-api:1.7.36 -> 2.0.16
|    |              +--- commons-lang:commons-lang:2.6
|    |              +--- org.apache.commons:commons-pool2:2.11.1 -> 2.12.0
|    |              +--- commons-pool:commons-pool:1.6
|    |              +--- org.apache.dubbo.extensions:dubbo-filter-seata:1.0.2 -> 3.3.1
|    |              +--- aopalliance:aopalliance:1.0
|    |              +--- com.google.guava:guava:32.1.3-jre -> 20.0
|    |              +--- com.github.ben-manes.caffeine:caffeine:2.9.3 -> 3.1.8
|    |              |    +--- org.checkerframework:checker-qual:3.37.0 -> 3.42.0
|    |              |    \--- com.google.errorprone:error_prone_annotations:2.21.1
|    |              \--- net.bytebuddy:byte-buddy:1.14.15 -> 1.14.19
|    +--- com.nimbusds:nimbus-jose-jwt -> 9.24.4 (*)
|    +--- net.logstash.logback:logstash-logback-encoder -> 5.1
|    |    +--- ch.qos.logback:logback-core:1.2.3 -> 1.5.12
|    |    \--- com.fasterxml.jackson.core:jackson-databind:2.9.5 -> 2.17.3 (*)
|    +--- org.reflections:reflections -> 0.10.2 (*)
|    +--- uk.com.robust-it:cloning -> 1.9.2
|    |    \--- org.objenesis:objenesis:2.1 -> 3.3
|    +--- com.fasterxml.jackson.core:jackson-databind -> 2.17.3 (*)
|    +--- com.fasterxml.jackson.core:jackson-core -> 2.17.3 (*)
|    +--- com.google.code.gson:gson -> 2.10.1
|    +--- cn.hutool:hutool-all -> 5.8.27
|    +--- com.belerweb:pinyin4j -> 2.5.1
|    \--- org.mapstruct:mapstruct -> 1.6.3
+--- org.springframework.boot:spring-boot-starter-web -> 3.3.6 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-rest -> 7.23.0 (*)
+--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp -> 7.23.0
|    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter-webapp-core:7.23.0
|    |    +--- org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter:7.23.0 (*)
|    |    +--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0
|    |    |    +--- com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.core:jackson-databind:2.15.2 -> 2.17.3 (*)
|    |    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2 -> 2.17.3 (*)
|    |    |    +--- org.camunda.bpm:camunda-engine-rest-core-jakarta:7.23.0 (*)
|    |    |    \--- org.camunda.commons:camunda-commons-logging:7.23.0 (*)
|    |    +--- org.springframework.boot:spring-boot-starter-web:3.4.4 -> 3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-starter-jersey:3.4.4 -> 3.3.6 (*)
|    \--- org.camunda.bpm.webapp:camunda-webapp-webjar:7.23.0
|         \--- org.camunda.bpm.webapp:camunda-webapp-jakarta:7.23.0 (*)
+--- org.springframework.boot:spring-boot-starter-jdbc -> 3.3.6 (*)
+--- org.postgresql:postgresql -> 42.7.4 (*)
+--- org.springframework.boot:spring-boot-starter-actuator -> 3.3.6
|    +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
|    +--- org.springframework.boot:spring-boot-actuator-autoconfigure:3.3.6
|    |    +--- com.fasterxml.jackson.core:jackson-databind:2.17.3 (*)
|    |    +--- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.3 (*)
|    |    +--- org.springframework.boot:spring-boot-actuator:3.3.6
|    |    |    \--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
|    |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
|    +--- io.micrometer:micrometer-observation:1.13.8 (*)
|    \--- io.micrometer:micrometer-jakarta9:1.13.8
|         +--- io.micrometer:micrometer-core:1.13.8 (*)
|         +--- io.micrometer:micrometer-commons:1.13.8
|         \--- io.micrometer:micrometer-observation:1.13.8 (*)
+--- org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring -> 2.1.2 (*)
+--- org.springframework.cloud:spring-cloud-starter-openfeign -> 4.2.0 (*)
+--- org.springframework.retry:spring-retry -> 2.0.10
+--- org.springframework.cloud:spring-cloud-starter-loadbalancer -> 4.1.2 (*)
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-seata -> 2023.0.3.2 (*)
+--- org.apache.seata:seata-all -> 2.3.0 (*)
+--- org.apache.dubbo.extensions:dubbo-filter-seata -> 3.3.1
+--- com.taobao.arthas:arthas-spring-boot-starter -> 4.0.4
|    +--- com.taobao.arthas:arthas-agent-attach:4.0.4
|    |    +--- net.bytebuddy:byte-buddy-agent:1.14.11 -> 1.14.19
|    |    \--- org.zeroturnaround:zt-zip:1.16
|    |         \--- org.slf4j:slf4j-api:1.6.6 -> 2.0.16
|    \--- com.taobao.arthas:arthas-packaging:4.0.4
+--- com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel -> 2023.0.1.0 (*)
+--- com.alibaba.csp:sentinel-datasource-nacos -> 1.8.7 (*)
\--- org.springframework.boot:spring-boot-starter-test -> 3.3.6
     +--- org.springframework.boot:spring-boot-starter:3.3.6 (*)
     +--- org.springframework.boot:spring-boot-test:3.3.6
     |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
     |    \--- org.springframework:spring-test:6.1.15
     |         \--- org.springframework:spring-core:6.1.15 (*)
     +--- org.springframework.boot:spring-boot-test-autoconfigure:3.3.6
     |    +--- org.springframework.boot:spring-boot:3.3.6 (*)
     |    +--- org.springframework.boot:spring-boot-test:3.3.6 (*)
     |    \--- org.springframework.boot:spring-boot-autoconfigure:3.3.6 (*)
     +--- com.jayway.jsonpath:json-path:2.9.0 (*)
     +--- jakarta.xml.bind:jakarta.xml.bind-api:4.0.2 (*)
     +--- net.minidev:json-smart:2.5.1 (*)
     +--- org.assertj:assertj-core:3.25.3
     |    \--- net.bytebuddy:byte-buddy:1.14.11 -> 1.14.19
     +--- org.awaitility:awaitility:4.2.2
     |    \--- org.hamcrest:hamcrest:2.1 -> 2.2
     +--- org.hamcrest:hamcrest:2.2
     +--- org.junit.jupiter:junit-jupiter:5.10.5
     |    +--- org.junit:junit-bom:5.10.5
     |    |    +--- org.junit.jupiter:junit-jupiter:5.10.5 (c)
     |    |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5 (c)
     |    |    +--- org.junit.jupiter:junit-jupiter-engine:5.10.5 (c)
     |    |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5 (c)
     |    |    +--- org.junit.platform:junit-platform-commons:1.10.5 (c)
     |    |    \--- org.junit.platform:junit-platform-engine:1.10.5 (c)
     |    +--- org.junit.jupiter:junit-jupiter-api:5.10.5
     |    |    +--- org.junit:junit-bom:5.10.5 (*)
     |    |    +--- org.opentest4j:opentest4j:1.3.0
     |    |    \--- org.junit.platform:junit-platform-commons:1.10.5
     |    |         \--- org.junit:junit-bom:5.10.5 (*)
     |    +--- org.junit.jupiter:junit-jupiter-params:5.10.5
     |    |    +--- org.junit:junit-bom:5.10.5 (*)
     |    |    \--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
     |    \--- org.junit.jupiter:junit-jupiter-engine:5.10.5
     |         +--- org.junit:junit-bom:5.10.5 (*)
     |         +--- org.junit.platform:junit-platform-engine:1.10.5
     |         |    +--- org.junit:junit-bom:5.10.5 (*)
     |         |    +--- org.opentest4j:opentest4j:1.3.0
     |         |    \--- org.junit.platform:junit-platform-commons:1.10.5 (*)
     |         \--- org.junit.jupiter:junit-jupiter-api:5.10.5 (*)
     +--- org.mockito:mockito-core:5.11.0
     |    +--- net.bytebuddy:byte-buddy:1.14.12 -> 1.14.19
     |    +--- net.bytebuddy:byte-buddy-agent:1.14.12 -> 1.14.19
     |    \--- org.objenesis:objenesis:3.3
     +--- org.mockito:mockito-junit-jupiter:5.11.0
     |    +--- org.mockito:mockito-core:5.11.0 (*)
     |    \--- org.junit.jupiter:junit-jupiter-api:5.10.2 -> 5.10.5 (*)
     +--- org.skyscreamer:jsonassert:1.5.3
     |    \--- com.vaadin.external.google:android-json:0.0.20131108.vaadin1
     +--- org.springframework:spring-core:6.1.15 (*)
     +--- org.springframework:spring-test:6.1.15 (*)
     \--- org.xmlunit:xmlunit-core:2.9.1

testRuntimeOnly - Runtime only dependencies for source set 'test'. (n)
No dependencies

(c) - A dependency constraint, not a dependency. The dependency affected by the constraint occurs elsewhere in the tree.
(*) - Indicates repeated occurrences of a transitive dependency subtree. Gradle expands transitive dependency subtrees only once per project; repeat occurrences only display the root of the subtree, followed by this annotation.

(n) - A dependency or dependency configuration that cannot be resolved.

A web-based, searchable dependency report is available by adding the --scan option.
