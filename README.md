# camunda-plugin

This project uses [Gradle](https://gradle.org/).
To build and run the application, use the *Gradle* tool window by clicking the Gradle icon in the right-hand toolbar,
or run it directly from the terminal:

* Run `./gradlew run` to build and run the application.
* Run `./gradlew build` to only build the application.
* Run `./gradlew check` to run all checks, including tests.
* Run `./gradlew clean` to clean all build outputs.

Note the usage of the Gradle Wrapper (`./gradlew`).
This is the suggested way to use Gradle in production projects.

[Learn more about the Gradle Wrapper](https://docs.gradle.org/current/userguide/gradle_wrapper.html).

[Learn more about Gradle tasks](https://docs.gradle.org/current/userguide/command_line_interface.html#common_tasks).

## 项目结构

本项目采用Gradle多模块架构，包含以下核心模块:

- `camunda-server`: 主服务模块，基于Spring Boot实现Camunda BPM工作流引擎
- `camunda-server-nacos-support`: 提供Nacos服务发现与配置中心支持
- `camunda-pulsar-connector-plugin`: Camunda与Pulsar消息系统的连接器插件
- `camunda-bpm-api`: BPM相关API定义
- `camunda-bpm-service`: BPM核心业务逻辑实现
- `camunda-common`: 公共工具类和基础组件

共享构建逻辑提取到`buildSrc`中的约定插件。

## 技术栈

- **核心框架**: 基于spring-boot-parent ${project.property("openCareParentVersion")}
- **工作流引擎**: Camunda BPM 7
- **消息系统**: Apache Pulsar 3.2.3
- **服务发现**: Nacos
- **构建工具**: Gradle with 构建缓存和配置缓存
- **依赖管理**: 版本目录(gradle/libs.versions.toml)

This project uses a version catalog (see `gradle/libs.versions.toml`) to declare and version dependencies
and both a build cache and a configuration cache (see `gradle.properties`).

## 任务变量查询示例

TaskQueryRequestDTO现在支持变量查询功能，可以通过以下方式使用：

```java
import com.open_care.bpm.dto.TaskQueryRequestDTO;
import com.open_care.bpm.dto.TaskQueryRequestDTO.Filter;
import com.open_care.bpm.utils.VariableFilterBuilder;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class TaskQueryExample {

    @Autowired
    private TaskService taskService;

    public void queryTaskWithVariables() {
        // 创建查询请求DTO
        TaskQueryRequestDTO queryDTO = TaskQueryRequestDTO.builder()
                .processDefinitionKey("myProcess")
                // 过滤条件列表，可以包含单个变量过滤器和组合过滤器
                .filters(Arrays.asList(
                    // 单个变量过滤条件
                    VariableFilterBuilder.taskEquals("priority", "high"),
                    VariableFilterBuilder.processGreaterThan("amount", 1000),
                    
                    // OR组合：status='completed' OR status='in-progress'
                    VariableFilterBuilder.or(
                        VariableFilterBuilder.processEquals("status", "completed"),
                        VariableFilterBuilder.processEquals("status", "in-progress")
                    ),
                    
                    // AND组合：region='north' AND department='sales'
                    VariableFilterBuilder.and(
                        VariableFilterBuilder.processEquals("region", "north"),
                        VariableFilterBuilder.processEquals("department", "sales")
                    )
                ))
                .build();
        
        // 获取TaskQuery对象
        TaskQuery taskQuery = taskService.createTaskQuery();
        
        // 应用查询条件
        // 最终生成的查询语句类似于：
        // (priority='high' AND amount>1000 AND
        // (status='completed' OR status='in-progress') AND
        // (region='north' AND department='sales'))
        TaskQuery query = com.open_care.bpm.utils.TaskQueryDTOUtils.getUserTasksQuery(queryDTO, taskQuery);
        
        // 执行查询
        long count = query.count();
        System.out.println("找到符合条件的任务数量: " + count);
    }
}

## 支持的变量操作符

- EQUALS：等于
- NOT_EQUALS：不等于
- GREATER_THAN：大于
- GREATER_THAN_OR_EQUALS：大于等于
- LESS_THAN：小于
- LESS_THAN_OR_EQUALS：小于等于
- LIKE：模糊匹配
- NOT_LIKE：非模糊匹配