package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/5/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TaskTransferLogDTO extends TaskInfoDTO {

    @OcColumn(title = "转办信息")
    private TaskAssignLogDTO assignLog;
    
    @OcColumn(title = "转办日志列表")
    private List<TaskAssignLogDTO> assignLogs;
}
