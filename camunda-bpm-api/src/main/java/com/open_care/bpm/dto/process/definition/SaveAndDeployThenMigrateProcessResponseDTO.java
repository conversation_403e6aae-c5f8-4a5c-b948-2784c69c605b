package com.open_care.bpm.dto.process.definition;

import com.open_care.annotation.OcColumn;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 保存、部署并迁移流程响应DTO
 * 
 * <AUTHOR>
 * @date :2025/5/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SaveAndDeployThenMigrateProcessResponseDTO implements IBaseDTO {

    /**
     * 状态，0表示成功，非0表示失败
     */
    @OcColumn(title = "状态")
    private String status;
    
    /**
     * 错误信息，成功时为空
     */
    @OcColumn(title = "错误信息")
    private String error;
    
    /**
     * 成功时的消息
     */
    @OcColumn(title = "消息")
    private String msg;
    
    /**
     * 流程信息
     */
    @OcColumn(title = "流程信息")
    private ProcessInfoDTO processInfo;
}
