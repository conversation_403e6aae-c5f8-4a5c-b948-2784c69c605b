package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/5/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TaskHandleLogDTO implements IBaseDTO {

    @OcColumn(title = "处理人")
    private String handler;

    @OcColumn(title = "处理人名字")
    private String handlerName;

    @OcColumn(title = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleTime;

    @OcColumn(title = "处理备注")
    private String handleRemark;

    @OcColumn(title = "处理结果")
    private String handleResult;

    @OcColumn(title = "完成任务时附带的相关变量")
    private Map<String,Object> variables;
}
