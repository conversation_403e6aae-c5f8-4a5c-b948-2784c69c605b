package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/5/8
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class TaskAssignLogDTO implements Serializable, IBaseDTO {

    @OcColumn(title = "id")
    private String id;

    @OcColumn(title = "任务拥有者")
    private String owner;

    @OcColumn(title = "拥有者名称")
    private String ownerName;

    @OcColumn(title = "操作者/任务指派人")
    private String operator;

    @OcColumn(title = "操作者名称")
    private String operatorName;

    @OcColumn(title = "任务被受让人")
    private String assignee;

    @OcColumn(title = "任务候选人")
    private List<String> candidateUsers;

    @OcColumn(title = "任务候选组")
    private List<String> candidateGroups;

    @OcColumn(title = "转办时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date assignTime;

    @OcColumn(title = "转办原因")
    private String assignReason;


    @OcColumn(title = "指派备注")
    private String assignRemark;

    @OcColumn(title = "原始候选人")
    private List<String> originalCandidateUsers;

    @OcColumn(title = "原始候选用户组")
    private List<String> originalCandidateGroups;

    @OcColumn(title = "原始指派人")
    private String originalAssignee;

}
