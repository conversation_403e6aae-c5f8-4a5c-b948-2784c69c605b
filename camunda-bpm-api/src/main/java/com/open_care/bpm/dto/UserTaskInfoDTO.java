package com.open_care.bpm.dto;

/**
 * <AUTHOR>
 * @date :2025/4/2
 */

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 用户任务信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserTaskInfoDTO implements Serializable, IBaseDTO {

    /**
     * 任务ID
     */
    @OcColumn(title = "任务ID")
    private String id;

    /**
     * 任务名称
     */
    @OcColumn(title = "任务名称")
    private String name;
}