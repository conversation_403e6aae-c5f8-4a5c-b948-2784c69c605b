package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.api.common.export.Page;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/5/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ProcessInstQueryRequestDTO  implements IBaseDTO {

    @OcColumn(title = "分页参数")
    private Page pagination;

    @OcColumn(title = "流程key")
    private List<String> processDefKeys;

    @OcColumn(title = "流程Id")
    private List<String> processDefIds;

    @OcColumn(title = "流程实例id")
    private List<String> processInstIds;

    @OcColumn(title = "提交人")
    private List<String> committers;

    @OcColumn(title = "流程是否结束")
    private Boolean ended;

}
