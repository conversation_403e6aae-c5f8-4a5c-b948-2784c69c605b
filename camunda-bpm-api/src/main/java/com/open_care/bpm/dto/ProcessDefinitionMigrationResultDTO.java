package com.open_care.bpm.dto;

import com.open_care.annotation.OcClass;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程定义迁移结果DTO
 * 用于返回流程定义迁移的结果信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@OcClass(title = "流程定义迁移结果")
public class ProcessDefinitionMigrationResultDTO implements IBaseDTO {

    /**
     * 流程定义键
     */
    @OcColumn(title = "流程定义键")
    private String processDefinitionKey;

    /**
     * 源流程定义ID列表
     */
    @OcColumn(title = "源流程定义ID列表")
    @Builder.Default
    private List<String> sourceProcessDefinitionIds = CollUtil.newArrayList();

    /**
     * 目标流程定义ID
     */
    @OcColumn(title = "目标流程定义ID")
    private String targetProcessDefinitionId;

    /**
     * 迁移成功的流程实例数量
     */
    @OcColumn(title = "迁移成功的流程实例数量")
    private Integer successCount;

    /**
     * 迁移失败的流程实例数量
     */
    @OcColumn(title = "迁移失败的流程实例数量")
    private Integer failureCount;

    /**
     * 迁移失败的流程实例ID列表
     */
    @OcColumn(title = "迁移失败的流程实例ID列表")
    @Builder.Default
    private List<String> failedProcessInstanceIds = CollUtil.newArrayList();

    /**
     * 迁移失败的原因列表
     */
    @OcColumn(title = "迁移失败的原因列表")
    @Builder.Default
    private List<String> failureReasons = CollUtil.newArrayList();
}
