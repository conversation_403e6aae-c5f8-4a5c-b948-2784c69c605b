package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date :2025/5/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TaskTransferDTO implements IBaseDTO {

    @OcColumn(title = "任务id")
    private List<String> taskIds;

    @OcColumn(title = "任务候选人")
    private List<String> candidateUsers;

    @OcColumn(title = "任务候选组")
    private List<String> candidateGroups;

    @OcColumn(title = "受让人")
    private String assignee;

    @OcColumn(title = "原因")
    private String reason;

    @OcColumn(title = "操作人")
    private String operator;

    @OcColumn(title = "指派时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date assignTime;

}
