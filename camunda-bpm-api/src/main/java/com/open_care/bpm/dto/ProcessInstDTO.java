package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/5/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ProcessInstDTO implements IBaseDTO {

    @OcColumn(title = "ID")
    private String id;

    @OcColumn(title = "关联类名称")
    private String className;

    @OcColumn(title = "实例id")
    private String entityInstId;

    @OcColumn(title = "流程编号")
    private String processNo;

    @OcColumn(title = "流程提交人")
    private String committer;

    @OcColumn(title = "流程最后一次提交人")
    private String lastCommitter;

    @OcColumn(title = "流程提交人名字")
    private String committerName;

    @OcColumn(title = "流程最后一次提交人名字")
    private String lastCommitterName;

    @OcColumn(title = "流程提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commitTime;

    @OcColumn(title = "流程最后一次提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastCommitTime;

    @OcColumn(title = "流程标题")
    private String processTitle;

    @OcColumn(title = "当前任务信息")
    private List<TaskInfoDTO> taskInfos;

    @OcColumn(title = "是否挂起")
    private Boolean suspended;

    @OcColumn(title = "是否结束")
    private Boolean ended;

    @OcColumn(title = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @OcColumn(title = "备注")
    private String remark;

    @OcColumn(title = "附件")
    private List<String> attachments;

    @OcColumn(title = "流程实例编号")
    private String businessKey;

    @OcColumn(title = "流程开始时的变量")
    private Map<String, Object> startingVariables;

    @OcColumn(title = "流程变量")
    private Map<String, Object> variables;

//    @OcColumn(title = "流程来源")
//    private WorkOrderSourceEnuM source;
//
//    @OcColumn(title = "流程类型")
//    private ProcessBusinessTypeEnum processBusinessType;

    @OcColumn(title = "流程信息")
    private ProcessInfoDTO processInfo;

}
