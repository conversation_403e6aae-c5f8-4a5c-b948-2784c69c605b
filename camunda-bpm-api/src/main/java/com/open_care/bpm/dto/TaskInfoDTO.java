/**
 * Copyright 2019-2020 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.api.common.enums.TaskStateEnum;
import com.open_care.bpm.enums.TaskCategoryEnum;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.open_care.api.common.dto.TaskPropertiesDTO;
import lombok.extern.log4j.Log4j2;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Log4j2
public class TaskInfoDTO implements IBaseDTO, Comparable<TaskInfoDTO> {
    @OcColumn(title = "任务id")
    protected String taskId;
    @OcColumn(title = "修订版本")
    protected int revision;

    @OcColumn(title = "所有者")
    protected String owner;
    // 候选用户
    @OcColumn(title = "候选用户")
    protected List<String> candidateUsers;
    @OcColumn(title = "候选组")
    protected List<String> candidateGroups;
    // 任务受让人
    @OcColumn(title = "受让人")
    protected String assignee;
    @OcColumn(title = "任务状态")
    protected TaskStateEnum taskState;
    @OcColumn(title = "父任务id")
    protected String parentTaskId;
    @OcColumn(title = "名称")
    protected String name;
    @OcColumn(title = "任务名称")
    protected String taskName;
    @OcColumn(title = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date taskCreateTime;
    @OcColumn(title = "描述")
    protected String description;
    @OcColumn(title = "优先级")
    @Builder.Default
    protected int priority = 50; // PRIORITY_NORMAL = 50
    @OcColumn(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date createTime; // The time when the task has been created
    @OcColumn(title = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected Date dueDate;
    @OcColumn(title = "跟进日期")
    protected Date followUpDate;
    @OcColumn(title = "挂起状态")
    @Builder.Default
    protected int suspensionState = 1; // ACTIVE = 1
    @OcColumn(title = "租户id")
    protected String tenantId;
    @OcColumn(title = "窗口id")
    protected String windowId;
    @OcColumn(title = "类型")
    protected String type;
    @OcColumn(title = "标题")
    protected String title;
    @OcColumn(title = "流程实例id")
    protected String processInstanceId;
    @OcColumn(title = "流程定义id")
    protected String processDefinitionId;
    @OcColumn(title = "流程定义键")
    protected String processDefinitionKey;
    @OcColumn(title = "流程名称")
    protected String processName;
    @OcColumn(title = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date endTime;
    @OcColumn(title = "当前任务名称")
    protected String currentTaskName;
    @OcColumn(title = "评论")
    protected String comment;
    @OcColumn(title = "实体实例id")
    protected String entityInstId;
    @OcColumn(title = "组件名称")
    protected String componentName;
    @OcColumn(title = "任务定义键")
    protected String taskDefinitionKey;
    @OcColumn(title = "业务键")
    protected String businessKey;
    @OcColumn(title = "删除原因")
    protected String deleteReason;
    @OcColumn(title = "行id")
    protected String _rowid;

    @OcColumn(title = "变量")
    @Builder.Default
    Map<String, Object> variables = new HashMap<>();

    @OcColumn(title = "流程实例信息")
    private ProcessInstDTO processInst;

    // 任务的扩展属性
    @OcColumn(title = "扩展属性")
    @Builder.Default
    protected Map<String, Object> extensionProperties = new HashMap<>();

    @OcColumn(title = "额外属性")
    private TaskPropertiesDTO properties;

    @OcColumn(title = "任务指派记录")
    private List<TaskAssignLogDTO> assignLogs;

    @OcColumn(title = "处理信息（如果任务已经完成）")
    private TaskHandleLogDTO handleLog;

    /**
     * 是否是被他人指派的任务
     */
    @OcColumn(title = "是否被他人指派")
    private Boolean isAssignedByOthers;
    
    /**
     * 任务类别：个人任务或共享池任务
     * 如果任务未被领取(assignee为null)则是共享任务，已经被领取则是个人任务
     */
    @OcColumn(title = "任务类别")
    private TaskCategoryEnum taskCategory;

    public int compareTo(TaskInfoDTO other) {
        return other.createTime.compareTo(this.createTime);
    }

}
