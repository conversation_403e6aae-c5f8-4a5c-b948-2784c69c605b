package com.open_care.bpm.dto;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.api.common.edit.FieldData;
import com.open_care.api.common.export.Page;
import com.open_care.api.enums.SortType;
import com.open_care.api.enums.TaskAcceptingUserType;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户待办任务查询DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class TaskQueryRequestDTO implements Serializable, IBaseDTO {

    private static final long serialVersionUID = 7L;

    /**
     * 流程key
     */
    @OcColumn(title = "流程定义键")
    private String processDefinitionKey;
    /**
     * 流程id
     */
    @OcColumn(title = "流程定义ID")
    private String processDefinitionId;

    /**
     * 任务ID
     */
    @OcColumn(title = "任务ID")
    private String taskId;

    /**
     * 任务id集合
     */
    @OcColumn(title = "任务ID集合")
    private List<String> taskIds;

    /**
     * 根据业务key查询，
     */
    @OcColumn(title = "业务键集合")
    private Collection<String> businessKeys;

    /**
     * 流程实例id
     */
    @OcColumn(title = "流程实例ID")
    private String processInstanceId;

    /**
     * 任务优先级
     */
    @OcColumn(title = "任务优先级")
    private Integer taskPriority;

    @OcColumn(title = "用户ID")
    private String userId;

    @OcColumn(title = "用户组")
    private List<String> userGroups;
    /**
     * 用户是以何种身份接受的任务
     */
    @OcColumn(title = "任务接受类型")
    private TaskAcceptingUserType taskAcceptingUserType;

    @OcColumn(title = "分页信息")
    private Page page;

    /**
     * 任务创建时间的排序类型（目前只支持根据创建时间做排序）
     */
    @OcColumn(title = "创建时间排序类型")
    private SortType createTimeSortType;

    /**
     * 任务优先级排序
     */
    @OcColumn(title = "优先级排序类型")
    private SortType prioritySortType;

    /**
     * 任务名称
     */
    @OcColumn(title = "任务名称")
    private String name;
    /**
     * 任务key
     */
    @OcColumn(title = "任务定义键")
    private String taskDefinitionKey;
    /**
     * 优先级 ，-1表示由高到低排序
     */
    @OcColumn(title = "优先级")
    private Integer priority;
    /**
     * 参与人ID 这里一般是实际办理人，是能够主观判断的人
     */
    @OcColumn(title = "受让人")
    private String assignee;
    /**
     * 候选人ID 表示我可能是办理人之一
     */
    @OcColumn(title = "候选人")
    private String candidateUser;
    /**
     * 候选组ID 表示我可能所属的组
     */
    @OcColumn(title = "候选组")
    private String candidateGroup;
    /**
     * 流程定义ID
     */
    @OcColumn(title = "流程定义名称")
    private String processDefinitionName;
    /**
     * 作业ID 这里指的是 job ID
     */
    @OcColumn(title = "执行ID")
    private String executionId;
    /**
     * 任务创建时间
     */
    @OcColumn(title = "创建时间查询 开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeStart;

    @OcColumn(title = "创建时间查询 结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTimeEnd;
    /**
     * 业务Key
     */
    @OcColumn(title = "业务键")
    private String businessKey;

    /**
     * 排序内容
     */
    @OcColumn(title = "排序字段")
    private String orderBy;

    /**
     * 是否正序
     */
    @OcColumn(title = "是否正序")
    private Boolean asc;

    /**
     * 起始页
     */
    @OcColumn(title = "页码")
    private Integer pageNo;

    /**
     * 每页数量
     */
    @OcColumn(title = "每页数量")
    private Integer pageSize;

    /**
     * 参数
     */
    @OcColumn(title = "变量")
    private Map<String, Object> variables;

    /**
     * 过滤条件列表，可以包含单变量过滤器和变量过滤器组
     * 列表中的条件之间是AND关系
     */
    @OcColumn(title = "变量过滤器")
    private List<VariableFilter> variableFilters;

    public static TaskQueryRequestDTO fieldDateConvertTskQueryRequestDTO(Collection<FieldData> fieldData, Page page) {
        TaskQueryRequestDTO taskQueryRequestDTO = new TaskQueryRequestDTO();
        taskQueryRequestDTO.setPage(page);

        if (CollUtil.isEmpty(fieldData)) {
            return taskQueryRequestDTO;
        }

        for (FieldData fieldDatum : fieldData) {
            handleFieldData(fieldDatum, taskQueryRequestDTO);
        }
        return taskQueryRequestDTO;
    }

    private static void handleFieldData(FieldData fieldDatum, TaskQueryRequestDTO taskQueryRequestDTO) {
        switch (fieldDatum.getFieldName()) {
            case Fields.processDefinitionKey:
                taskQueryRequestDTO.setProcessDefinitionKey(fieldDatum.getFieldValue().toString());
                break;
            case Fields.processDefinitionId:
                taskQueryRequestDTO.setProcessDefinitionId(fieldDatum.getFieldValue().toString());
                break;
            case Fields.taskId:
                taskQueryRequestDTO.setTaskId(fieldDatum.getFieldValue().toString());
                break;
            case Fields.businessKeys:
                taskQueryRequestDTO.setBusinessKeys(CollUtil.newArrayList(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.taskPriority:
                taskQueryRequestDTO.setTaskPriority(Integer.parseInt(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.userId:
                taskQueryRequestDTO.setUserId(fieldDatum.getFieldValue().toString());
                break;
            case Fields.taskAcceptingUserType:
                taskQueryRequestDTO.setTaskAcceptingUserType(TaskAcceptingUserType.valueOf(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.createTimeSortType:
                taskQueryRequestDTO.setCreateTimeSortType(SortType.valueOf(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.prioritySortType:
                taskQueryRequestDTO.setPrioritySortType(SortType.valueOf(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.name:
                taskQueryRequestDTO.setName(fieldDatum.getFieldValue().toString());
                break;
            case Fields.taskDefinitionKey:
                taskQueryRequestDTO.setTaskDefinitionKey(fieldDatum.getFieldValue().toString());
                break;
            case Fields.priority:
                taskQueryRequestDTO.setPriority(Integer.parseInt(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.assignee:
                taskQueryRequestDTO.setAssignee(fieldDatum.getFieldValue().toString());
                break;
            case Fields.candidateUser:
                taskQueryRequestDTO.setCandidateUser(fieldDatum.getFieldValue().toString());
                break;
            case Fields.candidateGroup:
                taskQueryRequestDTO.setCandidateGroup(fieldDatum.getFieldValue().toString());
                break;
            case Fields.processDefinitionName:
                taskQueryRequestDTO.setProcessDefinitionName(fieldDatum.getFieldValue().toString());
                break;
            case Fields.executionId:
                taskQueryRequestDTO.setExecutionId(fieldDatum.getFieldValue().toString());
                break;
            case Fields.businessKey:
                taskQueryRequestDTO.setBusinessKey(fieldDatum.getFieldValue().toString());
                break;
            case Fields.orderBy:
                taskQueryRequestDTO.setOrderBy(fieldDatum.getFieldValue().toString());
                break;
            case Fields.asc:
                taskQueryRequestDTO.setAsc(Boolean.parseBoolean(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.pageNo:
                taskQueryRequestDTO.setPageNo(Integer.parseInt(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.pageSize:
                taskQueryRequestDTO.setPageSize(Integer.parseInt(fieldDatum.getFieldValue().toString()));
                break;
            case Fields.variables:
                taskQueryRequestDTO.setVariables((Map<String, Object>) fieldDatum.getFieldValue());
                break;
            case Fields.variableFilters:
                taskQueryRequestDTO.setVariableFilters((List<VariableFilter>) fieldDatum.getFieldValue());
                break;
        }
    }
}
