package com.open_care.bpm.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date :2025/4/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ProcessInfoDTO implements IBaseDTO {
    @OcColumn(title = "ID")
    private String id;

    /**
     * 流程名称
     */
    @OcColumn(title = "流程名称")
    private String name;

    
    /**
     * 流程定义ID
     */
    @OcColumn(title = "流程定义ID")
    private String processDefinitionId;
    
    /**
     * 流程定义Key
     */
    @OcColumn(title = "流程定义Key")
    private String processDefinitionKey;
    
    /**
     * 流程定义名称
     */
    @OcColumn(title = "流程定义名称")
    private String processDefinitionName;

    /**
     * 流程中的所有用户任务
     */
    @Builder.Default
    @OcColumn(title = "用户任务列表")
    private List<UserTaskInfoDTO> userTasks = new ArrayList<>();

    /**
     * 扩展属性
     */
    @OcColumn(title = "扩展属性")
    private Map<String,Object> extensionProperties;

    /**
     * 业务属性
     */
    @OcColumn(title = "业务属性")
    private Map<String,Object> variables;

    /**
     * 流程迁移结果
     */
    @OcColumn(title = "流程迁移结果")
    private ProcessDefinitionMigrationResultDTO migrationResult;

}


