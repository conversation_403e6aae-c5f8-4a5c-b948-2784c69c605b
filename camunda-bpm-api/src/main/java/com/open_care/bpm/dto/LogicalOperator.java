package com.open_care.bpm.dto;

import com.open_care.core.IShowLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 逻辑操作符枚举，用于组合多个过滤条件
 */
@AllArgsConstructor
@Getter
public enum LogicalOperator implements IShowLabelEnum {
    /**
     * 与操作
     */
    AND("AND", "与"),

    /**
     * 或操作
     */
    OR("OR", "或");

    private final String dbValue;
    private final String label;
}