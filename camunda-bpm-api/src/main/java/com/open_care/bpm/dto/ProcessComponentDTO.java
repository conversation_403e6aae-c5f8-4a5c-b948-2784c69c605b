package com.open_care.bpm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.open_care.annotation.OcColumn;
import com.open_care.bpm.enums.ProcessComponentTypeEnum;
import com.open_care.dto.BaseEntityDTO;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * 流程组件DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ProcessComponentDTO implements IBaseDTO {

    /**
     * 主键ID
     */
    @OcColumn(title = "id")
    private String id;


    /**
     * 创建人
     */
    @OcColumn(title = "创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @OcColumn(title = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @OcColumn(title = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @OcColumn(title = "更新时间")
    private Date updateTime;

    /**
     * 组件名称
     */
    @OcColumn(title = "组件名称")
    private String name;

    /**
     * 组件描述
     */
    @OcColumn(title = "组件描述")
    private String description;

    @OcColumn(title = "组件类型")
    private ProcessComponentTypeEnum componentType;

    /**
     * 组件内容 (XML或JSON格式)
     */
    @OcColumn(title = "组件内容")
    private String content;
    
    /**
     * 组件唯一标识
     */
    @OcColumn(title = "组件唯一标识")
    private String componentKey;
    
    /**
     * 组件BPMN类型
     */
    @OcColumn(title = "组件BPMN类型")
    private String type;
    
    /**
     * 组件分组
     */
    @OcColumn(title = "组件分组")
    private String group;

    @OcColumn(title = "值")
    private Map<String,Object> values;
    /**
     * 组件属性值
     */
    @OcColumn(title = "组件属性值")
    private Map<String, Object> extensionProperties;
} 