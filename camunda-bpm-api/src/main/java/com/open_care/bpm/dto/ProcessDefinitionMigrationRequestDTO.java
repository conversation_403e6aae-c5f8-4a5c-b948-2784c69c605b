package com.open_care.bpm.dto;

import com.open_care.annotation.OcClass;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Set;

/**
 * 流程定义迁移请求DTO
 * 用于将旧流程定义的实例数据迁移到新的流程定义
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@OcClass(title = "流程定义迁移请求")
public class ProcessDefinitionMigrationRequestDTO implements IBaseDTO {


    /**
     * 流程实例id
     */
    @OcColumn(title = "流程实例id")
    private Set<String> processInstIds;
    /**
     * 流程定义键
     */
    @OcColumn(title = "流程定义键")
    private String processDefinitionKey;

    /**
     * 源流程定义版本
     * 如果为null，则表示不根据版本迁移
     */
    @OcColumn(title = "源流程定义版本")
    private Integer sourceVersion;

    /**
     * 是否迁移该版本之前的所有版本
     * 如果为true，则迁移sourceVersion及之前的所有版本
     * 如果为false，则只迁移sourceVersion指定的版本
     */
    @OcColumn(title = "是否迁移该版本之前的所有版本")
    @Builder.Default
    private Boolean migrateAllPreviousVersions = false;

    /**
     * 目标流程定义版本
     * 如果为null，则表示迁移到最新版本
     */
    @OcColumn(title = "目标流程定义版本")
    private Integer targetVersion;
}
