package com.open_care.bpm.enums;

import com.open_care.core.IShowLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 因为 IShowLabelEnum 基于JDK17进行编辑，当前版本JDK8 无法使用，
 * 所以bpm 和APP对当前的enum重复定义了两边，当bpm升级成JDK17之后，需要将两个javaClass进行合并
 * @See {@link org.camunda.bpm.engine.task.Task}
 */
@AllArgsConstructor
@Getter
public enum PriorityEnum implements IShowLabelEnum {
    LOW("0","低"),
    NORMAL("50","普通"),
    URGENT("100","紧急"),
    ;

    private final String dbValue;

    private final String label;

}
