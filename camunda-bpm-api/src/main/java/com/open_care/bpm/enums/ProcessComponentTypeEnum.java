package com.open_care.bpm.enums;

import com.open_care.core.IShowLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程组件类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ProcessComponentTypeEnum implements IShowLabelEnum {

    PROCESS("PROCESS", "流程定义"),
    NODE("NODE", "节点")
    ;

    private final String dbValue;
    private final String label;

    /**
     * 根据数据库值获取枚举实例
     *
     * @param dbValue 数据库值
     * @return 枚举实例，如果不存在则返回null
     */
    public static ProcessComponentTypeEnum getByDbValue(String dbValue) {
        if (dbValue == null) {
            return null;
        }

        for (ProcessComponentTypeEnum type : values()) {
            if (type.getDbValue().equals(dbValue)) {
                return type;
            }
        }
        return null;
    }
} 