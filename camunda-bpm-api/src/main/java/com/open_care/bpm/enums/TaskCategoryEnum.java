package com.open_care.bpm.enums;

import com.open_care.core.IShowLabelEnum;
import lombok.Getter;

/**
 * 任务类别枚举
 */
@Getter
public enum TaskCategoryEnum implements IShowLabelEnum {
    PERSONAL_TASKS("1", "个人任务"),
    SHARED_POOL_TASKS("10", "共享池任务");

    private final String dbValue;
    private final String label;

    TaskCategoryEnum(String dbValue, String label) {
        this.dbValue = dbValue;
        this.label = label;
    }
} 