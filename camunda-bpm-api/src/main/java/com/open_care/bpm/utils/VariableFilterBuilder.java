package com.open_care.bpm.utils;

import com.open_care.bpm.dto.LogicalOperator;
import com.open_care.bpm.dto.SingleVariableFilter;
import com.open_care.bpm.dto.VariableFilterGroup;
import com.open_care.bpm.dto.VariableOperator;
import com.open_care.bpm.dto.VariableScopeType;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 变量过滤器构建工具类
 * 用于方便地构建任务和流程变量的查询条件
 */
public class VariableFilterBuilder {

    /**
     * 创建任务变量等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.EQUALS)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量不等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskNotEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.NOT_EQUALS)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量大于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskGreaterThan(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.GREATER_THAN)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量大于等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskGreaterThanOrEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.GREATER_THAN_OR_EQUALS)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量小于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskLessThan(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LESS_THAN)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量小于等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskLessThanOrEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LESS_THAN_OR_EQUALS)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量模糊匹配查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskLike(String name, String value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LIKE)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建任务变量非模糊匹配查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter taskNotLike(String name, String value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.NOT_LIKE)
                .variableScope(VariableScopeType.TASK)
                .build();
    }

    /**
     * 创建流程变量等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.EQUALS)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量不等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processNotEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.NOT_EQUALS)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量大于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processGreaterThan(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.GREATER_THAN)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量大于等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processGreaterThanOrEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.GREATER_THAN_OR_EQUALS)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量小于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processLessThan(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LESS_THAN)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量小于等于查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processLessThanOrEquals(String name, Object value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LESS_THAN_OR_EQUALS)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量模糊匹配查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processLike(String name, String value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.LIKE)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建流程变量非模糊匹配查询条件
     *
     * @param name  变量名
     * @param value 变量值
     * @return 变量过滤器
     */
    public static SingleVariableFilter processNotLike(String name, String value) {
        return SingleVariableFilter.builder()
                .name(name)
                .value(value)
                .operator(VariableOperator.NOT_LIKE)
                .variableScope(VariableScopeType.PROCESS)
                .build();
    }

    /**
     * 创建AND组合过滤器
     *
     * @param filters 变量过滤器列表
     * @return 组合变量过滤器
     */
    public static VariableFilterGroup and(SingleVariableFilter... filters) {
        return and(Arrays.asList(filters));
    }

    /**
     * 创建OR组合过滤器
     *
     * @param filters 变量过滤器列表
     * @return 组合变量过滤器
     */
    public static VariableFilterGroup or(SingleVariableFilter... filters) {
        return or(Arrays.asList(filters));
    }

    /**
     * 创建AND组合过滤器
     *
     * @param filters 变量过滤器列表
     * @return 组合变量过滤器
     */
    public static VariableFilterGroup and(List<SingleVariableFilter> filters) {
        return VariableFilterGroup.builder()
                .filters(filters)
                .operator(LogicalOperator.AND)
                .build();
    }

    /**
     * 创建OR组合过滤器
     *
     * @param filters 变量过滤器列表
     * @return 组合变量过滤器
     */
    public static VariableFilterGroup or(List<SingleVariableFilter> filters) {
        return VariableFilterGroup.builder()
                .filters(filters)
                .operator(LogicalOperator.OR)
                .build();
    }
} 