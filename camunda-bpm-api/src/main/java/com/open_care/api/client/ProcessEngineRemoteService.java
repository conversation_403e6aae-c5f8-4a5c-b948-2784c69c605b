package com.open_care.api.client;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.bpmn.CombineDTO;
import com.open_care.api.common.dto.bpmn.NewTaskDTO;
import com.open_care.api.common.dto.bpmn.ProcessInstanceModificationDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.bpm.dto.ProcessComponentDTO;
import com.open_care.bpm.dto.ProcessInfoDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessRequestDTO;
import com.open_care.bpm.dto.process.definition.SaveAndDeployThenMigrateProcessResponseDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(name = "camunda-service", contextId = "process-engine-service")
public interface ProcessEngineRemoteService {
    @GetMapping("/OcprocessDefList/{page}/{size}")
    String getProcessDefList(@PathVariable(value = "page") int page, @PathVariable(value = "size") int size);

    @PostMapping("/queryOcprocessDefList")
    OcResponse queryProcessDefList(@RequestBody QueryRequestDTO queryRequestDTO);

    @PostMapping("/saveOcprocessDefInfo/{id}")
    OcResponse<ProcessInfoDTO> saveOcprocessDefInfo(@PathVariable(value = "id") String id, @RequestBody String body);

    @PostMapping("/renameById/{id}")
    OcResponse renameById(@PathVariable(value = "id") String id, @RequestBody String body);

    @GetMapping("/getOcprocessDefInfoById/{id}/{version}")
    OcResponse getOcprocessDefInfoById(@PathVariable(value = "id") String id, @PathVariable(value = "version") Integer version);

    @GetMapping("/getOcprocessDefInfoByIdWithOutVersion/{id}")
    OcResponse getOcprocessDefInfoByIdWithOutVersion(@PathVariable(value = "id") String id);

    @GetMapping("/deployProcessdef/{id}/{version}")
    OcResponse deployProcessdefByOCProcessdefId(@PathVariable(value = "id") String id, @PathVariable(value = "version") String version);

    @GetMapping("/getAppProcessInfo/{appDefId}")
    OcResponse getAppProcessInfo(@PathVariable(value = "appDefId") String appDefId);

    @PostMapping("/importAppProcessInfo/{appDefId}")
    OcResponse importAppProcessInfo(@PathVariable(value = "appDefId") String appDefId, @RequestBody String body);

    @PostMapping("/createProductProcess/{productId}/{processKey}")
    OcResponse createProductProcess(@PathVariable(value = "productId") String productId,
                                    @PathVariable(value = "processKey") String processKey,
                                    @RequestBody Map<String, Object> body);

    @PostMapping("/createPackageProcess/{productId}/{processKey}")
    OcResponse createPackageProcess(@PathVariable(value = "productId") String productId,
                                    @PathVariable(value = "processKey") String processKey,
                                    @RequestBody CombineDTO body);

    @PostMapping("/startBeforeActivity/{processInstId}/{activityId}")
    OcResponse startBeforeActivity(@PathVariable(value = "processInstId") String processInstId,
                                   @PathVariable(value = "activityId") String activityId,
                                   @RequestBody ProcessInstanceModificationDTO body);

    @PostMapping("/startAfterActivity/{processInstId}/{activityId}")
    OcResponse startAfterActivity(@PathVariable(value = "processInstId") String processInstId,
                                  @PathVariable(value = "activityId") String activityId,
                                  @RequestBody ProcessInstanceModificationDTO body);

    @PostMapping("/cancelAllForActivity/{processInstId}/{activityId}")
    OcResponse cancelAllForActivity(@PathVariable(value = "processInstId") String processInstId,
                                    @PathVariable(value = "activityId") String activityId,
                                    @RequestBody ProcessInstanceModificationDTO body);

    @PostMapping("/addMultiInstance")
        //加签
    OcResponse addMultiInstance(@RequestBody NewTaskDTO body);

    /**
     * 获取所有流程组件
     *
     * @return 流程组件列表
     */
    @GetMapping("/processComponents")
    OcResponse<List<ProcessComponentDTO>> getAllProcessComponents();

    /**
     * 保留流程实例（保存成新流程实例，后端会自动更改 processName 和 processDefinitionKey）
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/saveAndDeployThenMigrateProcess")
    OcResponse<SaveAndDeployThenMigrateProcessResponseDTO> saveAndDeployThenMigrateProcess(@RequestBody SaveAndDeployThenMigrateProcessRequestDTO requestDTO);

}
