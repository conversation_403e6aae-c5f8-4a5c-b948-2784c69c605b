package com.open_care.api.client;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.ProcessHistoryDTO;
import com.open_care.api.common.query.QueryDataResponse;
import com.open_care.api.dto.process.CompleteProcessTaskDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationRequestDTO;
import com.open_care.bpm.dto.ProcessDefinitionMigrationResultDTO;
import com.open_care.bpm.dto.ProcessInstDTO;
import com.open_care.bpm.dto.ProcessInstQueryRequestDTO;
import com.open_care.bpm.dto.ProcessModificationDTO;
import com.open_care.bpm.dto.TaskHistoryQueryRequestDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.dto.TaskQueryRequestDTO;
import com.open_care.bpm.dto.TaskTransferDTO;
import com.open_care.bpm.dto.TaskTransferLogDTO;
import com.open_care.bpm.dto.TaskTransferLogQueryRequestDTO;
import com.open_care.bpm.enums.PriorityEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;
import java.util.Set;


@FeignClient(name = "camunda-service", contextId = "process-remote-service")
public interface ProcessRemoteService {
    @PostMapping("/start-process-instance-by-key/{processDefinitionKey}")
    OcResponse<?> startProcessInstanceByKey(@PathVariable("processDefinitionKey") String processDefinitionKey, @RequestBody String body);

    @PostMapping("/start-process-instance-by-id/{processDefinitionId}")
    OcResponse<?> startProcessInstanceById(@PathVariable("processDefinitionId") String processDefinitionId, @RequestBody String body);

    @PostMapping("/start-process-instance-by-key-and-run-first-task/{processDefinitionKey}")
    OcResponse<?> startProcessInstanceByKeyAndRunFirstTask(@PathVariable("processDefinitionKey") String processDefinitionKey, @RequestBody String body);

    @PostMapping("/start-process-instance-by-id-and-run-first-task/{processDefinitionId}")
    OcResponse<?> startProcessInstanceByIdAndRunFirstTask(@PathVariable("processDefinitionId") String processDefinitionId, @RequestBody String body);

    @RequestMapping("/claim-task/{taskId}/{userId}")
    OcResponse<?> claimTask(@PathVariable("taskId") String taskId, @PathVariable("userId") String userId);

    @RequestMapping("/claim-task/{userId}")
    OcResponse<?> claimTask(@RequestBody Set<String> taskIds, @PathVariable("userId") String userId);

    @PostMapping("/unclaim-task/{taskId}")
    OcResponse<Object> unclaimTask(@PathVariable("taskId") String taskId);

    @PostMapping("/unclaim-task")
    OcResponse<Object> unclaimTask(@RequestBody Set<String> taskIds);

    @GetMapping("/queryIdentityEntityByTaskId/{taskId}")
    OcResponse<Object> queryIdentityEntityByTaskId(@PathVariable("taskId") String taskId);

    @GetMapping("/modifyTaskPriorityByTaskId/{taskId}/{priorityEnum}")
    OcResponse<String> modifyTaskPriorityByTaskId(@PathVariable("taskId") String taskId, @PathVariable("priorityEnum") PriorityEnum priorityEnum);

    @RequestMapping("/delegate-task/{taskId}/{userId}")
    OcResponse<?> delegateTask(@PathVariable("taskId") String taskId, @PathVariable("userId") String userId);

    @RequestMapping("/delegate-task/{userId}")
    OcResponse<?> delegateTask(@RequestBody Set<String> taskIds, @PathVariable("userId") String userId);


    @RequestMapping("/assignee-task/{taskId}/{userId}")
    OcResponse<?> assigneeTask(@PathVariable("taskId") String taskId, @PathVariable("userId") String userId);

    @PostMapping("/assignee-task/{userId}")
    OcResponse<?> assigneeTask(@RequestBody Set<String> taskIds, @PathVariable("userId") String userId);

    @RequestMapping("/resolve-task/{taskId}")
    OcResponse<?> resolveTask(@PathVariable("taskId") String taskId, @RequestBody String body);

    @RequestMapping("/complete-task/{taskId}")
    OcResponse<?> completeTask(@PathVariable("taskId") String taskId, @RequestBody Map<String,Object> variables);

    @RequestMapping("/batch-complete-tasks")
    OcResponse<?> batchCompleteTasks(@RequestBody List<CompleteProcessTaskDTO> completeProcessTaskDTOList);

    @RequestMapping("/set-task-variables/{taskId}")
    ResponseEntity<String> setTaskVariables(@PathVariable("taskId") String taskId, @RequestBody String body);

    @GetMapping("/get-current-tasks-by-procdefkey/{processDefinitionKey}")
    ResponseEntity<String> getCurrentTasksByProcDefKey(@PathVariable("processDefinitionKey") String processDefinitionKey);

    @GetMapping("/get-current-tasks-by-procdef/{processDefinitionId}")
    ResponseEntity<String> getCurrentTasksByProcDef(@PathVariable("processDefinitionId") String processDefinitionId);

    @GetMapping("/get-current-tasks-by-procinst/{processInstanceId}")
    ResponseEntity<String> getCurrentTasksByProcInst(@PathVariable("processInstanceId") String processInstanceId);

    @GetMapping("/get-current-tasks-by-user/{userId}")
    ResponseEntity<String> getCurrentTasksByUser(@PathVariable("userId") String userId);

    @GetMapping("/get-current-tasks-count-by-user/{userId}")
    ResponseEntity<Long> getCurrentTasksCountByUser(@PathVariable("userId") String userId);

    @PostMapping("/findUserTasksWithPagination")
    OcResponse<QueryDataResponse<List<TaskInfoDTO>>> findUserTasksWithPagination(@RequestBody TaskQueryRequestDTO taskQueryRequestDTO);

    @PostMapping("/findUserTaskHistoryWithPagination")
    OcResponse<QueryDataResponse<List<TaskInfoDTO>>> findUserTaskHistoryWithPagination(@RequestBody TaskHistoryQueryRequestDTO taskQueryRequestDTO);

    @PostMapping("/get-current-tasks-by-users")
    ResponseEntity<String> getCurrentTasksByUsers(@RequestBody String userIds);

    @RequestMapping("/get-current-tasks-by-user-and-procdefid/{userId}/{processDefinitionId}")
    ResponseEntity<String> getCurrentTasksByUserAndProcDefId(@PathVariable("userId") String userId,
                                                             @PathVariable("processDefinitionId") String processDefinitionId,
                                                             @RequestBody Map body
    );

    @RequestMapping("/get-current-tasks-by-user-and-procdefkey/{userId}/{processDefinitionKey}")
    ResponseEntity<String> getCurrentTasksByUserAndProcDefKey(@PathVariable("userId") String userId,
                                                              @PathVariable("processDefinitionKey") String processDefinitionKey,
                                                              @RequestBody Map body
    );

    @GetMapping("/get-task-by-taskId/{taskId}")
    OcResponse<TaskInfoDTO> getTaskByTaskId(@PathVariable("taskId") String taskId);

    @PostMapping("/process-modification")
    OcResponse<?> processModification(@RequestBody ProcessModificationDTO processModificationDTO);

    @GetMapping("/get-history-tasks-by-user-and-procdef/{userId}/{processDefinitionId}")
    ResponseEntity<String> getHistoryTasksByUserAndProcDef(@PathVariable("userId") String userId, @PathVariable("processDefinitionId") String processDefinitionId);

    @RequestMapping("/get-taskdef-by-procdef/{processDefinitionId}")
    ResponseEntity<String> getTasksByProcessKey(@PathVariable("processDefinitionId") String processDefinitionId);

    @RequestMapping("/get-task_delegate_users/{taskId}")
    ResponseEntity<String> getTaskDelegateUsersByTaskId(@PathVariable("taskId") String taskId);

    @RequestMapping("/get-task_assignee_users/{taskId}")
    ResponseEntity<String> getTaskAssigneeUsersByTaskId(@PathVariable("taskId") String taskId);

    @RequestMapping("/get-process_instances")
    ResponseEntity<String> getProcessInstances();

    @RequestMapping("/clear-process_instances")
    ResponseEntity<String> clearProcessInstances();

    @RequestMapping("/processDefinitions")
    ResponseEntity<String> processDefinitions();

    @RequestMapping("/userTaskDefinitions")
    ResponseEntity<String> userTaskDefs();

    @RequestMapping("/getProcessDefinition/{processDefinitionId}/xml")
    ResponseEntity<String> getProcessDefinitionXml(@PathVariable("processDefinitionId") String processDefinitionId);

    /**
     * 获取流程实例的历史信息，包括执行历史和流程图
     *
     * @param processInstanceId 流程实例ID
     * @return 包含流程历史信息的响应对象
     */
    @GetMapping("/get-history-tasks-by-procInst/{processInstanceId}")
    OcResponse<ProcessHistoryDTO> getHistoryTasksByProcInst(@PathVariable("processInstanceId") String processInstanceId);

    @PostMapping("/delete-processInstance/{processInstanceId}")
    OcResponse<?> deleteProcessInstance(@PathVariable("processInstanceId") String processInstanceId, @RequestBody String body);

    @GetMapping("/restart-processInstances/{processInstanceId}")
    OcResponse<?> restartProcessInstances(String processInstanceId);

    @PostMapping("/delete-task/{taskId}")
    OcResponse<?> deleteTask(@PathVariable("taskId") String taskId, @RequestBody String body);

    @PostMapping("/candidate-task/{taskId}")
    OcResponse<?> candidateTask(@PathVariable("taskId") String taskId, @RequestBody Map body);

    @GetMapping("/getTaskAuditHistory/{processInstanceId}")
    OcResponse<?> getTaskAuditHistory(@PathVariable("processInstanceId") String processInstanceId);

    @GetMapping("/get-current-tasks")
    ResponseEntity<String> getCurrentTasks();

    @GetMapping("/get-current-tasks-by-taskDefKey/{taskDefKey}")
    ResponseEntity<String> getCurrentTasksByTaskDefKey(@PathVariable("taskDefKey") String taskDefKey);

    @GetMapping("/get-history-tasks-by-taskDefKey/{taskDefKey}")
    ResponseEntity<String> getHistoryTasksByTaskDefKey(@PathVariable("taskDefKey") String taskDefKey);

    @GetMapping("/get-history-tasks-by-processInstId/{processInstanceId}")
    ResponseEntity<String> getHistoryTasksByProcessInstId(@PathVariable("processInstanceId") String processInstanceId);

    @PostMapping("/get-current-tasks-by-taskDefKeys")
    ResponseEntity<String> getCurrentTasksByTaskDefKeys(@RequestBody List<String> taskDefKeys);

    @PostMapping("/get-history-tasks-by-taskDefKeys")
    ResponseEntity<String> getHistoryTasksByTaskDefKeys(@RequestBody List<String> taskDefKeys);

    @PostMapping("/get-current-tasks-by-procinstIds")
    ResponseEntity<String> getCurrentTasksByProcInstIds(@RequestBody List<String> processInstanceIds);

    @GetMapping("/get-history-tasks-by-userId/{userId}")
    ResponseEntity<String> getHistoryTasksByUserId(@PathVariable("userId") String userId);

    @PostMapping("/findTaskTransferLog")
    OcResponse<QueryDataResponse<List<TaskTransferLogDTO>>> findTaskTransferLog(@RequestBody TaskTransferLogQueryRequestDTO taskTransferLogQueryRequestDTO);

    @PostMapping("/taskTransfer")
    OcResponse<List<TaskTransferLogDTO>> taskTransfer(@RequestBody TaskTransferDTO taskTransferDTO);

    /**
     * 分页查询流程实例
     *
     * @param requestDTO 查询条件
     * @return 查询结果（带分页信息）
     */
    @PostMapping("/findProcessInst")
    OcResponse<QueryDataResponse<List<ProcessInstDTO>>> findProcessInstWithPagination(@RequestBody ProcessInstQueryRequestDTO requestDTO);

    /**
     * 迁移流程定义实例
     * 支持将某一个流程key的某个版本或者这个版本之前的所有版本迁移到最新版本或者某个具体的版本
     *
     * @param requestDTO 迁移请求参数
     * @return 迁移结果
     */
    @PostMapping("/migrateProcessDefinitionInstances")
    OcResponse<ProcessDefinitionMigrationResultDTO> migrateProcessDefinitionInstances(@RequestBody ProcessDefinitionMigrationRequestDTO requestDTO);
}
