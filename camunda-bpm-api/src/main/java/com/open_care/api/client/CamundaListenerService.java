package com.open_care.api.client;

import com.open_care.api.common.dto.ExecutionEventDTO;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.dto.TaskEventDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Camunda流程监听器服务接口
 * 提供统一的流程执行事件处理方法
 * 
 * <AUTHOR>
 * @date :2025/4/11
 */
public interface CamundaListenerService {

    /**
     * 任务事件监听回调
     *
     * @param taskEventDTO 任务事件DTO
     */
    @PostMapping("/taskListener")
    OcResponse<Void> taskListener(@RequestBody TaskEventDTO taskEventDTO);

    /**
     * 流程执行事件处理
     * 根据ExecutionEventDTO中的eventType属性判断事件类型并进行相应处理
     * 
     * @param executionEventDTO 执行事件DTO
     */
    @PostMapping("/executionListener")
    OcResponse<Void> executionListener(@RequestBody ExecutionEventDTO executionEventDTO);

}
