package com.open_care.api.common.dto;

import com.open_care.bpm.dto.TaskInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * 流程实例历史信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ProcessHistoryDTO {
    /**
     * 流程定义XML
     */
    private String xml;
    
    /**
     * 任务列表
     */
    @Builder.Default
    private List<TaskInfoDTO> tasks = new ArrayList<>();
    
    /**
     * 已完成的任务定义键列表
     */
    @Builder.Default
    private List<String> completeTasks = new ArrayList<>();
    
    /**
     * 当前任务定义键列表
     */
    @Builder.Default
    private List<String> currentTasks = new ArrayList<>();
    
    /**
     * 活动节点信息列表
     */
    @Builder.Default
    private List<ActivityNodeDTO> activities = new ArrayList<>();
    
    /**
     * 流程连接线ID列表
     */
    @Builder.Default
    private List<String> sequenceFlows = new ArrayList<>();


} 