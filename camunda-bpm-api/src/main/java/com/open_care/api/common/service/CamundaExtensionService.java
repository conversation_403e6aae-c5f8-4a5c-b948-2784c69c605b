package com.open_care.api.common.service;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 基础服务接口
 * 用于跨服务调用的通用接口
 */
public interface CamundaExtensionService {
    /**
     * 根据条件获取用户列表
     * 
     * @param condition 条件
     * @return 用户列表JSON
     */
    @RequestMapping(value = "/getUsersByCondition", method = RequestMethod.POST)
    ResponseEntity<String> getUsersByCondition(@RequestBody String condition);

    /**
     * 通知用户任务变更
     * 
     * @param userId 用户ID
     */
    @RequestMapping(value = "/notifyUserTaskChange/{userId}", method = RequestMethod.GET)
    void notifyUserTaskChange(@PathVariable("userId") String userId);
} 