package com.open_care.api.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * @date :2025/4/15
 */
@AllArgsConstructor
@Getter
public enum TaskEventType {
    CREATE("create", "创建"),
    ASSIGNMENT("assignment", "分配"),
    COMPLETE("complete", "完成"),
    DELETE("delete", "删除"),
    UPDATE("update", "更新"),
    TIMEOUT_LIFECYCLE("timeout.lifecycle", "生命周期超时"),
    ASSIGNMENT_TIMEOUT("timeout.assignment", "申领超时"),
    COMPLETE_TIMEOUT("timeout.completion", "完成超时");

    private final String eventName;
    private final String desc;

    /**
     * 根据code获取枚举值
     *
     * @param taskStatus 状态码
     * @return 对应的枚举值，如果没有找到则返回null
     */
    public static TaskEventType valueOfStatus(String taskStatus) {
        return Arrays.stream(values())
                .filter(status -> StrUtil.equals(status.getEventName(), taskStatus))
                .findFirst()
                .orElse(null);
    }
} 