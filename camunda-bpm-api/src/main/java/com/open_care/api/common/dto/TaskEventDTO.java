package com.open_care.api.common.dto;

import com.open_care.annotation.OcColumn;
import com.open_care.api.common.enums.TaskEventType;
import com.open_care.bpm.dto.TaskInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 任务事件监听数据传输对象
 * 用于跨服务调用的任务事件信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class TaskEventDTO extends TaskInfoDTO {
    @OcColumn(title = "事件类型")
    private TaskEventType eventType;

    @OcColumn(title = "任务状态")
    private String taskStatus;

    @OcColumn(title = "任务属性")
    private TaskPropertiesDTO properties;
} 