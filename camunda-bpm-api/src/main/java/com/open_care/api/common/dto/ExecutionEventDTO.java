package com.open_care.api.common.dto;

import com.open_care.api.common.enums.ExecutionEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 流程执行事件监听数据传输对象
 * 基于DelegateExecution设计，包含完整的流程执行相关属性
 * 
 * <AUTHOR>
 * @date :2025/4/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionEventDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 事件类型
     */
    private ExecutionEventType eventType;


    /**
     * 执行ID
     */
    private String executionId;
    
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    
    /**
     * 流程定义ID
     */
    private String processDefinitionId;
    
    /**
     * 业务键
     */
    private String businessKey;
    
    /**
     * 父执行ID
     */
    private String parentId;
    
    /**
     * 超级执行ID
     */
    private String superExecutionId;
    
    /**
     * 流程变量
     */
    private Map<String, Object> variables;
    
    /**
     * 本地变量
     */
    private Map<String, Object> variablesLocal;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 事件名称
     */
    private String eventName;
    
    /**
     * 当前活动ID
     */
    private String currentActivityId;
    
    /**
     * 当前活动名称
     */
    private String currentActivityName;
    
    /**
     * 当前事务ID
     */
    private String currentTransitionId;
    
    /**
     * 流程实例活动中
     */
    private boolean processInstanceActive;
    
    /**
     * 并发执行
     */
    private boolean concurrent;
    
    /**
     * 源活动ID（仅用于连线事件）
     */
    private String sourceActivityId;
    
    /**
     * 目标活动ID（仅用于连线事件）
     */
    private String targetActivityId;
    
    /**
     * 流程实例启动时间
     */
    private Date processInstanceStartTime;
    
    /**
     * 流程异常信息
     */
    private String processExceptionMessage;
    
    /**
     * 流程实例已结束
     */
    private boolean processInstanceEnded;
    
    /**
     * 活动实例ID
     */
    private String activityInstanceId;
    
    /**
     * 父活动实例ID
     */
    private String parentActivityInstanceId;
    
    /**
     * 流程状态
     */
    private String processState;

    private ExecutionPropertiesDTO properties;


} 