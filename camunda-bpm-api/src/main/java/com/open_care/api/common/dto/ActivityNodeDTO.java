package com.open_care.api.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 流程活动节点DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ActivityNodeDTO {
    /**
     * 活动节点ID
     */
    private String activityId;
    
    /**
     * 活动节点名称
     */
    private String activityName;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 执行人
     */
    private String assignee;

    private String executionId;

    /**
     * 备注
     */
    private String comment;
    
    /**
     * 上一个节点ID（为保持向后兼容）
     */
    private String previousActivityId;
    
    /**
     * 上一个节点名称（为保持向后兼容）
     */
    private String previousActivityName;
    
    /**
     * 输入流ID（为保持向后兼容）
     */
    private String incomingFlowId;
    
    /**
     * 所有来源节点信息
     */
    @Builder.Default
    private List<NodeSourceDTO> incomingSources = new ArrayList<>();

} 