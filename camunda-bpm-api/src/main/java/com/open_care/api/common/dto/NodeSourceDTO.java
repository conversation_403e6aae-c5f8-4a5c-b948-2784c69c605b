package com.open_care.api.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 节点来源信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class NodeSourceDTO {
    /**
     * 连接线ID
     */
    private String flowId;
    
    /**
     * 来源节点ID
     */
    private String sourceId;
    
    /**
     * 来源节点名称
     */
    private String sourceName;

} 