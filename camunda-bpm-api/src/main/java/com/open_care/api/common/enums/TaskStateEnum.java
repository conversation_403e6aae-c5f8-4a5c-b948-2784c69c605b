package com.open_care.api.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date :2025/4/27
 */
@AllArgsConstructor
@Getter
public enum TaskStateEnum {
    // 通用 取自 TaskListener
    STATE_CREATED("Created"),
    STATE_INIT("Init"),
    STATE_DELETED("Deleted"),
    STATE_COMPLETED("Completed"),
    STATE_UPDATED("Updated"),

    // 外部任务 取自 ExternalTaskState
    STATE_FAILED("Failed"),
    STATE_SUCCESSFUL("Successful"),

    ;

    private final String taskState;


    public static TaskStateEnum valueOfState(String taskState) {
        return Arrays.stream(TaskStateEnum.values())
                .filter(state -> StrUtil.equalsAnyIgnoreCase(state.taskState, taskState))
                .findFirst()
                .orElse(null);
    }
}
