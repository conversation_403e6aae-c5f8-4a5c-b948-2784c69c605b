package com.open_care.api.common.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;

/**
 * 流程执行事件类型枚举
 * 定义流程执行过程中的各种事件类型
 * 
 * <AUTHOR>
 * @date :2025/4/11
 */
public enum ExecutionEventType {
    
    /**
     * 流程开始事件
     */
    EXECUTION_START("start", "开始"),
    
    /**
     * 流程结束事件
     */
    EXECUTION_END("end", "结果"),

    EXECUTION_TAKE("take","take"),
    ;

    /**
     * 事件代码
     */
    private final String code;
    
    /**
     * 事件名称
     */
    private final String name;
    
    ExecutionEventType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举值
     * 
     * @param eventName 事件代码
     * @return 枚举值
     */
    public static ExecutionEventType valueOfType(String eventName) {
        return Arrays.stream(values())
                .filter(event -> StrUtil.equals(event.getName(), eventName))
                .findFirst()
                .orElse(null);
    }
} 