import io.spring.gradle.dependencymanagement.dsl.DependencyManagementExtension

plugins {
    id("org.springframework.boot") apply false
    id("io.spring.dependency-management") apply false
}

allprojects {
    group = "com.open-care"
    version = "1.0.0-SNAPSHOT"
    

    
    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        mavenLocal()
        maven {
            url = uri("https://repo.open-care.com/repository/maven-public/")
            credentials {
                username = project.findProperty("nexusUsername") as String? ?: ""
                password = project.findProperty("nexusPassword") as String? ?: ""
            }
        }
        mavenCentral()
    }
}

subprojects {
    apply(plugin = "java")
    apply(plugin = "io.spring.dependency-management")
    
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor(0, java.util.concurrent.TimeUnit.SECONDS)

    }
    
    configure<DependencyManagementExtension> {
        imports {
            mavenBom("com.open-care:spring-boot-parent:${project.property("openCareParentVersion")}")
            mavenBom("com.open-care:dependency-parent:${project.property("openCareParentVersion")}")
        }
        
        resolutionStrategy {
            cacheChangingModulesFor(0, java.util.concurrent.TimeUnit.SECONDS)
        }
    }


    tasks.withType<JavaCompile> {
        options.encoding = "UTF-8"
    }

    tasks.withType<Test> {
        useJUnitPlatform()
    }
}
