package com.open_care.bpm.core;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.open_care.bpm.dto.TaskAssignLogDTO;
import com.open_care.bpm.dto.TaskInfoDTO;
import com.open_care.bpm.enums.PriorityEnum;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.variable.value.DateValue;
import org.camunda.bpm.engine.variable.value.StringValue;
import org.camunda.spin.SpinList;
import org.camunda.spin.json.SpinJsonNode;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 流程变量相关工具类
 *
 * <AUTHOR>
 * @date :2024/07/26
 */
public class ProcessVariableUtil {

    /**
     * 流程变量前缀
     */
    public static final String PROCESS_VARIABLE_PREFIX = "process.";

    /**
     * 业务变量前缀
     */
    public static final String PROCESS_BUSINESS_VARIABLE_PREFIX = "business.";

    public static final String TASK_VARIABLE_PREFIX = "task.";


    /**
     * 流程相关变量
     */

    public static final String ENTITY_NAME = "entityName";


    public static final String BUSINESS_KEY = "businessKey";

    public static final String PROCESS_NAME = "processName";


    public static final String PROCESS_DEF_TYPE = "processDefType";

    public static final String PROCESS_KEY = "processKey";

    public static final String COMMITTER = "committer";

    public static final String COMMITTER_NAME = "committerName";

    public static final String COMMIT_TIME = "commitTime";

    public static final String ENTITY_INST_ID = "entityInstId";

    public static final String START_TIME = "startTime";

    public static final String REMARK = "remark";

    public static final String ATTACHMENTS = "attachments";

    public static final String PRIORITY_ENUM = "priorityEnum";

    public static final String SOURCE = "source";

    public static final String BUSINESS_INFO = "businessInfo";

    /**
     * 任务相关变量
     */

    public static final String IS_ASSIGNMENT = "isAssignment";

    // 任务指派记录
    public static final String ASSIGN_LOGS = "assignLogs";

    // 任务完成相关变量
    public static final String COMPLETE_HANDLER = "complete.handler";
    public static final String COMPLETE_HANDLER_NAME = "complete.handlerName";
    public static final String COMPLETE_HANDLE_TIME = "complete.handleTime";
    public static final String COMPLETE_HANDLE_REMARK = "complete.handleRemark";
    public static final String COMPLETE_HANDLE_RESULT = "complete.handleResult";
    public static final String COMPLETE_VARIABLES = "complete.variables";

    /**
     * 任务提交人变量名
     */
    public static final String VARIABLE_COMMITTER = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, COMMITTER);

    public static final String VARIABLE_COMMITTER_NAME = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, COMMITTER_NAME);

    public static final String VARIABLE_COMMIT_TIME = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, COMMIT_TIME);

    /**
     * 备注变量名
     */
    public static final String VARIABLE_REMARK = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, REMARK);

    /**
     * 实体类名称变量名
     */
    public static final String VARIABLE_ENTITY_NAME = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, ENTITY_NAME);

    /**
     * 业务ID变量名
     */
    public static final String VARIABLE_BUSINESS_KEY = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, BUSINESS_KEY);

    /**
     * 流程名称变量名
     */
    public static final String VARIABLE_PROCESS_NAME = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, PROCESS_NAME);

    /**
     * 流程定义类型变量名
     */
    public static final String VARIABLE_PROCESS_DEF_TYPE = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, PROCESS_DEF_TYPE);

    /**
     * 流程键变量名
     */
    public static final String VARIABLE_PROCESS_KEY = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, PROCESS_KEY);

    /**
     * 实体实例ID变量名
     */
    public static final String VARIABLE_ENTITY_INST_ID = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, ENTITY_INST_ID);

    /**
     * 开始时间变量名
     */
    public static final String VARIABLE_START_TIME = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, START_TIME);

    /**
     * 附件变量名
     */
    public static final String VARIABLE_ATTACHMENTS = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, ATTACHMENTS);

    /**
     * 流程优先级变量名
     */
    public static final String VARIABLE_PRIORITY_ENUM = StrUtil.format("{}{}", PROCESS_VARIABLE_PREFIX, PRIORITY_ENUM);

    /**
     * 业务变量-来源
     */
    public static final String VARIABLE_BUSINESS_SOURCE = StrUtil.format("{}{}", PROCESS_BUSINESS_VARIABLE_PREFIX, SOURCE);

    /**
     * 业务变量-业务信息
     */
    public static final String VARIABLE_BUSINESS_INFO = StrUtil.format("{}{}", PROCESS_BUSINESS_VARIABLE_PREFIX, BUSINESS_INFO);


    /**
     * 是否是分派任务
     */
    public static final String VARIABLE_TASK_IS_ASSIGNMENT = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, IS_ASSIGNMENT);

    /**
     * 任务指派记录
     */
    public static final String VARIABLE_TASK_ASSIGN_LOGS = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, ASSIGN_LOGS);

    /**
     * 任务完成相关变量
     */
    public static final String VARIABLE_TASK_COMPLETE_HANDLER = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_HANDLER);
    public static final String VARIABLE_TASK_COMPLETE_HANDLER_NAME = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_HANDLER_NAME);
    public static final String VARIABLE_TASK_COMPLETE_HANDLE_TIME = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_HANDLE_TIME);
    public static final String VARIABLE_TASK_COMPLETE_HANDLE_REMARK = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_HANDLE_REMARK);
    public static final String VARIABLE_TASK_COMPLETE_HANDLE_RESULT = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_HANDLE_RESULT);
    public static final String VARIABLE_TASK_COMPLETE_VARIABLES = StrUtil.format("{}{}", TASK_VARIABLE_PREFIX, COMPLETE_VARIABLES);

    /**
     * 从任务中获取提交者
     *
     * @param task 任务信息
     * @return 提交者ID
     */
    public static String getCommitterFromTask(TaskInfoDTO task) {
        return getCommitterFromVariables(task.getVariables());
    }

    public static String getCommitterNameFromTask(TaskInfoDTO task) {
        return getCommitterNameFromVariables(task.getVariables());
    }


    public static Date getCommitTimeFromTask(TaskInfoDTO task) {
        return getCommitTimeFromVariables(task.getVariables());
    }

    public static String getCommitterFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_COMMITTER).toString());
    }

    public static String getCommitterNameFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_COMMITTER_NAME).toString());
    }


    public static Date getCommitTimeFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> (Date) variables.get(VARIABLE_COMMIT_TIME));
    }


    /**
     * 从任务中获取提交者
     *
     * @param task 任务信息
     * @return 提交者ID
     */
    public static String getCommitterFromTask(DelegateTask task) {
        return nullIfException(() -> {
            Object committerObj = task.getVariable(VARIABLE_COMMITTER);
            return committerObj != null ? committerObj.toString() : null;
        });
    }

    /**
     * 从任务变量中获取流程名称
     *
     * @param variables 任务变量
     * @return 流程名称
     */
    public static String getProcessNameFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_PROCESS_NAME).toString());
    }

    /**
     * 从任务中获取流程名称
     *
     * @param task 任务信息
     * @return 流程名称
     */
    public static String getProcessNameFromTask(TaskInfoDTO task) {
        return getProcessNameFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取流程名称
     *
     * @param task 任务信息
     * @return 流程名称
     */
    public static String getProcessNameFromTask(DelegateTask task) {
        return getProcessNameFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取实体类名称
     *
     * @param variables 任务变量
     * @return 实体类名称
     */
    public static String getEntityNameFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_ENTITY_NAME).toString());
    }

    /**
     * 从任务中获取实体类名称
     *
     * @param task 任务信息
     * @return 实体类名称
     */
    public static String getEntityNameFromTask(TaskInfoDTO task) {
        return getEntityNameFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取实体类名称
     *
     * @param task 任务信息
     * @return 实体类名称
     */
    public static String getEntityNameFromTask(DelegateTask task) {
        return getEntityNameFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取业务ID
     *
     * @param variables 任务变量
     * @return 业务ID
     */
    public static String getBusinessKeyFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_BUSINESS_KEY).toString());
    }

    /**
     * 从任务中获取业务ID
     *
     * @param task 任务信息
     * @return 业务ID
     */
    public static String getBusinessKeyFromTask(TaskInfoDTO task) {
        return getBusinessKeyFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取业务ID
     *
     * @param task 任务信息
     * @return 业务ID
     */
    public static String getBusinessKeyFromTask(DelegateTask task) {
        return getBusinessKeyFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取实体实例ID
     *
     * @param variables 任务变量
     * @return 实体实例ID
     */
    public static String getEntityInstIdFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_ENTITY_INST_ID).toString());
    }

    /**
     * 从任务中获取实体实例ID
     *
     * @param task 任务信息
     * @return 实体实例ID
     */
    public static String getEntityInstIdFromTask(TaskInfoDTO task) {
        return getEntityInstIdFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取实体实例ID
     *
     * @param task 任务信息
     * @return 实体实例ID
     */
    public static String getEntityInstIdFromTask(DelegateTask task) {
        return getEntityInstIdFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取备注
     *
     * @param variables 任务变量
     * @return 备注
     */
    public static String getRemarkFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_REMARK).toString());
    }

    /**
     * 从任务中获取备注
     *
     * @param task 任务信息
     * @return 备注
     */
    public static String getRemarkFromTask(TaskInfoDTO task) {
        return getRemarkFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取备注
     *
     * @param task 任务信息
     * @return 备注
     */
    public static String getRemarkFromTask(DelegateTask task) {
        return getRemarkFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取业务来源
     *
     * @param variables 任务变量
     * @return 业务来源
     */
    public static String getBusinessSourceFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_BUSINESS_SOURCE).toString());
    }

    /**
     * 从任务中获取业务来源
     *
     * @param task 任务信息
     * @return 业务来源
     */
    public static String getBusinessSourceFromTask(TaskInfoDTO task) {
        return getBusinessSourceFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取业务来源
     *
     * @param task 任务信息
     * @return 业务来源
     */
    public static String getBusinessSourceFromTask(DelegateTask task) {
        return getBusinessSourceFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取业务信息
     * 支持三种情况：
     * 1. businessInfo作为一个完整的对象存储在变量中
     * 2. 业务信息被拆分成多个变量，以VARIABLE_BUSINESS_INFO.开头
     * 3. 嵌套属性路径，如VARIABLE_BUSINESS_INFO.a.b.c.d形式
     *
     * @param variables 任务变量
     * @return 业务信息Map，包含合并后的所有业务信息
     */
    public static Map<String, Object> getBusinessInfoFromVariables(Map<String, Object> variables) {
        JSONObject result = new JSONObject();

        // 获取整体的businessInfo对象
        Object businessInfo = variables.get(VARIABLE_BUSINESS_INFO);
        if (businessInfo != null) {
            // 如果businessInfo是Map类型，直接使用
            if (businessInfo instanceof Map) {
                result.putAll((Map<String, Object>) businessInfo);
            } else {
                // 尝试将其他类型转为JSON对象
                try {
                    JSONObject jsonObject = JSONUtil.parseObj(businessInfo, false);
                    result.putAll(jsonObject);
                } catch (Exception e) {
                    // 如果转换失败，则将原始对象放入结果中
                    result.put(BUSINESS_INFO, businessInfo);
                }
            }
        }

        // 收集所有以VARIABLE_BUSINESS_INFO.开头的变量并处理嵌套属性
        String prefix = VARIABLE_BUSINESS_INFO + ".";
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(prefix)) {
                // 提取完整属性路径，去掉前缀
                String propertyPath = key.substring(prefix.length());
                Object value = entry.getValue();

                // 使用Hutool的JSONObject.putByPath方法直接设置嵌套属性
                result.putByPath(propertyPath, value);
            }
        }

        return result;
    }

    /**
     * 从任务中获取业务信息
     *
     * @param task 任务信息
     * @return 业务信息Map，包含合并后的所有业务信息
     */
    public static Map<String, Object> getBusinessInfoFromTask(TaskInfoDTO task) {
        return getBusinessInfoFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取业务信息
     *
     * @param task 任务信息
     * @return 业务信息Map，包含合并后的所有业务信息
     */
    public static Map<String, Object> getBusinessInfoFromTask(DelegateTask task) {
        return getBusinessInfoFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取附件
     *
     * @param variables 任务变量
     * @return 附件
     */
    public static List<String> getAttachmentsFromVariables(Map<String, Object> variables) {

        Object attachments = variables.get(VARIABLE_ATTACHMENTS);
        SpinList<SpinJsonNode> elements = SpinJsonNode.JSON(attachments).elements();

        return nullIfException(() -> elements.stream().map(Objects::toString).toList());
    }

    /**
     * 从任务中获取附件
     *
     * @param task 任务信息
     * @return 附件
     */
    public static List<String> getAttachmentsFromTask(TaskInfoDTO task) {
        return getAttachmentsFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取附件
     *
     * @param task 任务信息
     * @return 附件
     */
    public static List<String> getAttachmentsFromTask(DelegateTask task) {
        return getAttachmentsFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取流程优先级
     *
     * @param variables 任务变量
     * @return 流程优先级
     */
    public static PriorityEnum getPriorityEnumFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> PriorityEnum.valueOf(variables.get(VARIABLE_PRIORITY_ENUM).toString()));
    }

    /**
     * 从任务中获取流程优先级
     *
     * @param task 任务信息
     * @return 流程优先级
     */
    public static PriorityEnum getPriorityEnumFromTask(TaskInfoDTO task) {
        return getPriorityEnumFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取流程优先级
     *
     * @param task 任务信息
     * @return 流程优先级
     */
    public static PriorityEnum getPriorityEnumFromTask(DelegateTask task) {
        return getPriorityEnumFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取流程优先级
     *
     * @param task 任务信息
     * @return 流程优先级
     */
    public static Boolean getIsAssignmentFromTask(TaskInfoDTO task) {
        return getIsAssignmentFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取是否是分派任务
     *
     * @param task 任务信息
     * @return 是否是分派任务
     */
    public static Boolean getIsAssignmentFromTask(DelegateTask task) {
        return getIsAssignmentFromVariables(task.getVariables());
    }

    public static List<TaskAssignLogDTO> getAssignLogsFromVariables(Map<String, Object> variables) {
        Object assignLogs = variables.get(VARIABLE_TASK_ASSIGN_LOGS);
        if (assignLogs == null) {
            return null;
        }
        return nullIfException(() -> SpinJsonNode.JSON(assignLogs).elements().stream().map(ele -> SpinJsonNode.JSON(ele).mapTo(TaskAssignLogDTO.class)).toList());
    }


    private static Boolean getIsAssignmentFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> BooleanUtil.toBoolean(variables.get(VARIABLE_TASK_IS_ASSIGNMENT).toString()));
    }

    /**
     * 从任务变量中获取完成任务的处理人
     *
     * @param variables 任务变量
     * @return 处理人ID
     */
    public static String getCompleteHandlerFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_TASK_COMPLETE_HANDLER).toString());
    }

    /**
     * 从任务中获取完成任务的处理人
     *
     * @param task 任务信息
     * @return 处理人ID
     */
    public static String getCompleteHandlerFromTask(TaskInfoDTO task) {
        return getCompleteHandlerFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理人
     *
     * @param task 任务信息
     * @return 处理人ID
     */
    public static String getCompleteHandlerFromTask(DelegateTask task) {
        return nullIfException(() -> task.getVariable(VARIABLE_TASK_COMPLETE_HANDLER).toString());
    }

    /**
     * 从任务变量中获取完成任务的处理人名称
     *
     * @param variables 任务变量
     * @return 处理人名称
     */
    public static String getCompleteHandlerNameFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_TASK_COMPLETE_HANDLER_NAME).toString());
    }

    /**
     * 从任务中获取完成任务的处理人名称
     *
     * @param task 任务信息
     * @return 处理人名称
     */
    public static String getCompleteHandlerNameFromTask(TaskInfoDTO task) {
        return getCompleteHandlerNameFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理人名称
     *
     * @param task 任务信息
     * @return 处理人名称
     */
    public static String getCompleteHandlerNameFromTask(DelegateTask task) {
        return nullIfException(() -> task.getVariable(VARIABLE_TASK_COMPLETE_HANDLER_NAME).toString());
    }

    /**
     * 从任务变量中获取完成任务的处理时间
     *
     * @param variables 任务变量
     * @return 处理时间
     */
    public static Date getCompleteHandleTimeFromVariables(Map<String, Object> variables) {
        Object handleTime = variables.get(VARIABLE_TASK_COMPLETE_HANDLE_TIME);
        if (handleTime instanceof String) {
            return DateUtil.parse(handleTime.toString(), DatePattern.NORM_DATETIME_PATTERN);
        }
        if (handleTime instanceof StringValue) {
            return DateUtil.parse(((StringValue)handleTime).getValue(), DatePattern.NORM_DATETIME_PATTERN);
        }
        if (handleTime instanceof DateValue) {
            return ((DateValue) handleTime).getValue();
        }
        if (handleTime instanceof Date) {
            return (Date) handleTime;
        }
        return null;
    }

    /**
     * 从任务中获取完成任务的处理时间
     *
     * @param task 任务信息
     * @return 处理时间
     */
    public static Date getCompleteHandleTimeFromTask(TaskInfoDTO task) {
        return getCompleteHandleTimeFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理时间
     *
     * @param task 任务信息
     * @return 处理时间
     */
    public static Date getCompleteHandleTimeFromTask(DelegateTask task) {
        return getCompleteHandleTimeFromVariables(task.getVariables());
    }

    /**
     * 从任务变量中获取完成任务的处理备注
     *
     * @param variables 任务变量
     * @return 处理备注
     */
    public static String getCompleteHandleRemarkFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_TASK_COMPLETE_HANDLE_REMARK).toString());
    }

    /**
     * 从任务中获取完成任务的处理备注
     *
     * @param task 任务信息
     * @return 处理备注
     */
    public static String getCompleteHandleRemarkFromTask(TaskInfoDTO task) {
        return getCompleteHandleRemarkFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理备注
     *
     * @param task 任务信息
     * @return 处理备注
     */
    public static String getCompleteHandleRemarkFromTask(DelegateTask task) {
        return nullIfException(() -> task.getVariable(VARIABLE_TASK_COMPLETE_HANDLE_REMARK).toString());
    }

    /**
     * 从任务变量中获取完成任务的处理结果
     *
     * @param variables 任务变量
     * @return 处理结果
     */
    public static String getCompleteHandleResultFromVariables(Map<String, Object> variables) {
        return nullIfException(() -> variables.get(VARIABLE_TASK_COMPLETE_HANDLE_RESULT).toString());
    }

    /**
     * 从任务中获取完成任务的处理结果
     *
     * @param task 任务信息
     * @return 处理结果
     */
    public static String getCompleteHandleResultFromTask(TaskInfoDTO task) {
        return getCompleteHandleResultFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理结果
     *
     * @param task 任务信息
     * @return 处理结果
     */
    public static String getCompleteHandleResultFromTask(DelegateTask task) {
        return nullIfException(() -> task.getVariable(VARIABLE_TASK_COMPLETE_HANDLE_RESULT).toString());
    }

    /**
     * 从任务变量中获取完成任务的处理变量
     *
     * @param variables 任务变量
     * @return 处理变量Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getCompleteVariablesFromVariables(Map<String, Object> variables) {
        Object completeVariables = variables.get(VARIABLE_TASK_COMPLETE_VARIABLES);
        if (completeVariables == null) {
            return null;
        }

        // 如果已经是Map类型，直接返回
        if (completeVariables instanceof Map) {
            return (Map<String, Object>) completeVariables;
        }

        // 尝试使用SpinJson转换
        try {
            return SpinJsonNode.JSON(completeVariables).mapTo(Map.class);
        } catch (Exception e) {
            // 转换失败时，尝试使用JSONUtil转换
            try {
                return JSONUtil.parseObj(completeVariables, false);
            } catch (Exception ex) {
                // 所有转换都失败，返回null
                return null;
            }
        }
    }

    /**
     * 从任务中获取完成任务的处理变量
     *
     * @param task 任务信息
     * @return 处理变量Map
     */
    public static Map<String, Object> getCompleteVariablesFromTask(TaskInfoDTO task) {
        return getCompleteVariablesFromVariables(task.getVariables());
    }

    /**
     * 从任务中获取完成任务的处理变量
     *
     * @param task 任务信息
     * @return 处理变量Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getCompleteVariablesFromTask(DelegateTask task) {
        Object completeVariables = task.getVariable(VARIABLE_TASK_COMPLETE_VARIABLES);
        if (completeVariables == null) {
            return null;
        }

        // 如果已经是Map类型，直接返回
        if (completeVariables instanceof Map) {
            return (Map<String, Object>) completeVariables;
        }

        // 尝试使用SpinJson转换
        try {
            return SpinJsonNode.JSON(completeVariables).mapTo(Map.class);
        } catch (Exception e) {
            // 转换失败时，尝试使用JSONUtil转换
            try {
                return JSONUtil.parseObj(completeVariables, false);
            } catch (Exception ex) {
                // 所有转换都失败，返回null
                return null;
            }
        }
    }

    private static <T> T nullIfException(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            return null;
        }
    }
}