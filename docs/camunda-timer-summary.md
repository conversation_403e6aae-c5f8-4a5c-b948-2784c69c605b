# Camunda 7 与 Spring Boot 3 定时任务快速指南

## 核心概念

1. **Job Executor**: Camunda的后台线程池，负责执行异步任务和定时任务
2. **定时器类型**:
   - 定时器启动事件: 按时间触发流程启动
   - 边界定时器事件: 附加在活动上，可处理超时
   - 中间定时器事件: 在流程执行中暂停直到指定时间

## 实现方式

### 1. BPMN流程中定义定时器

最推荐的方式，通过流程图定义定时任务:

```xml
<!-- 定时器启动事件 - 每10分钟执行一次 -->
<bpmn:startEvent id="StartTimer">
  <bpmn:timerEventDefinition>
    <bpmn:timeCycle>R/PT10M</bpmn:timeCycle>
  </bpmn:timerEventDefinition>
</bpmn:startEvent>

<!-- 边界定时器事件 - 5分钟后超时 -->
<bpmn:boundaryEvent id="TimeoutEvent" attachedToRef="UserTask">
  <bpmn:timerEventDefinition>
    <bpmn:timeDuration>PT5M</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:boundaryEvent>
```

### 2. 创建服务任务处理定时器触发后的逻辑

```java
@Component("timerTaskDelegate")
public class TimerTaskDelegate implements JavaDelegate {
    @Override
    public void execute(DelegateExecution execution) {
        // 定时任务触发时的业务逻辑
        log.info("定时任务执行: {}", execution.getProcessInstanceId());
        // 执行具体操作...
    }
}
```

### 3. 配置Job Executor

在`application.yml`中配置:

```yaml
camunda:
  bpm:
    job-execution:
      enabled: true  # 启用Job Executor
      deployment-aware: true  # 集群环境中避免重复执行
      core-pool-size: 3  # 核心线程数
      max-pool-size: 10  # 最大线程数
```

### 4. 使用API管理定时任务

```java
// 查询定时任务
List<Job> timerJobs = managementService.createJobQuery()
    .timers()
    .active()
    .list();

// 立即执行定时任务
managementService.executeJob(jobId);

// 修改定时任务执行时间
managementService.setJobDuedate(jobId, newDueDate);

// 修改重试次数
managementService.setJobRetries(jobId, 3);
```

## 常用定时器表达式

### 1. 固定日期

```xml
<bpmn:timeDate>2023-12-31T23:59:59Z</bpmn:timeDate>
```

### 2. 持续时间 (ISO 8601格式)

```xml
<bpmn:timeDuration>PT5M</bpmn:timeDuration> <!-- 5分钟 -->
<bpmn:timeDuration>PT1H</bpmn:timeDuration> <!-- 1小时 -->
<bpmn:timeDuration>P1D</bpmn:timeDuration>  <!-- 1天 -->
```

### 3. 循环周期

```xml
<!-- ISO 8601重复格式 - 每10分钟执行一次 -->
<bpmn:timeCycle>R/PT10M</bpmn:timeCycle>

<!-- 重复5次，每10分钟一次 -->
<bpmn:timeCycle>R5/PT10M</bpmn:timeCycle>

<!-- Cron表达式 - 每天凌晨3点 -->
<bpmn:timeCycle>0 0 3 * * ?</bpmn:timeCycle>
```

## 最佳实践

1. 尽量在BPMN流程图中定义定时任务，而不是通过代码创建
2. 为定时任务实现适当的异常处理和重试机制
3. 确保事务边界合理，避免长事务
4. 定期监控失败的定时任务，特别是生产环境
5. 在集群环境中确保配置正确，避免任务重复执行
6. 通过业务键(BusinessKey)关联流程与业务对象

## 实用技巧

1. 使用变量动态设置定时器持续时间:
   ```xml
   <bpmn:timeDuration>${timerDuration}</bpmn:timeDuration>
   ```

2. 使用边界非中断事件实现提醒功能:
   ```xml
   <bpmn:boundaryEvent id="Reminder" cancelActivity="false">
     <bpmn:timerEventDefinition>...</bpmn:timerEventDefinition>
   </bpmn:boundaryEvent>
   ```

3. 结合异步任务与定时器实现更复杂的调度:
   ```xml
   <bpmn:serviceTask id="Task" camunda:asyncBefore="true" />
   ```

## 故障排查

如果定时任务未执行，检查:
1. Job Executor是否启用
2. 数据库中是否有对应的Job记录(ACT_RU_JOB表)
3. 应用日志中是否有Job执行相关的错误
4. 定时器表达式是否正确

## 更多资源

- [Camunda官方文档 - 定时器事件](https://docs.camunda.org/manual/7.17/reference/bpmn20/events/timer-events/)
- [Camunda官方文档 - Job Executor](https://docs.camunda.org/manual/7.17/user-guide/process-engine/the-job-executor/) 