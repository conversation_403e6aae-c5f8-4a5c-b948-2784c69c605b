# Camunda 7 定时任务使用示例

本文档提供一些实际应用场景中使用 Camunda 7 定时任务的示例代码和方法。

## 1. 常见定时任务场景

### 1.1 每日数据同步

使用定时器启动事件创建每天固定时间执行的数据同步流程：

```xml
<!-- BPMN XML 片段 -->
<bpmn:startEvent id="TimerStartEvent_DailySync">
  <bpmn:timerEventDefinition>
    <!-- 每天凌晨2点执行 - Cron表达式 -->
    <bpmn:timeCycle>0 0 2 * * ?</bpmn:timeCycle>
  </bpmn:timerEventDefinition>
</bpmn:startEvent>
```

对应的服务任务实现：

```java
@Component("dataSyncDelegate")
public class DataSyncDelegate implements JavaDelegate {
    @Override
    public void execute(DelegateExecution execution) throws Exception {
        // 数据同步逻辑
        log.info("执行每日数据同步 - {}", LocalDateTime.now());
        
        // 执行同步逻辑...
        
        // 记录结果
        execution.setVariable("syncCompleted", true);
        execution.setVariable("syncTime", LocalDateTime.now().toString());
    }
}
```

### 1.2 工单超时提醒

使用边界定时器事件处理工单处理超时：

```xml
<!-- BPMN XML 片段 -->
<bpmn:userTask id="ProcessTicket" name="处理工单">
  <!-- 工单相关配置 -->
</bpmn:userTask>

<bpmn:boundaryEvent id="TimerBoundaryEvent_TicketTimeout" attachedToRef="ProcessTicket">
  <bpmn:timerEventDefinition>
    <!-- 4小时后超时 -->
    <bpmn:timeDuration>PT4H</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
  <!-- 超时后执行通知 -->
</bpmn:boundaryEvent>
```

### 1.3 延迟发送通知

使用中间定时器事件实现延迟通知：

```xml
<!-- BPMN XML 片段 -->
<bpmn:sequenceFlow sourceRef="ApproveRequest" targetRef="WaitBeforeNotify" />

<bpmn:intermediateCatchEvent id="WaitBeforeNotify">
  <bpmn:timerEventDefinition>
    <!-- 等待30分钟 -->
    <bpmn:timeDuration>PT30M</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:intermediateCatchEvent>

<bpmn:sequenceFlow sourceRef="WaitBeforeNotify" targetRef="SendNotification" />
```

## 2. API 调用示例

### 2.1 部署带有定时器的流程

```java
// 使用ProgrammaticTimerService部署定时启动流程（每小时执行一次）
String deploymentId = programmaticTimerService.deployTimerStartProcess(
    "hourly-report-process",
    "R/PT1H", 
    "reportGenerationDelegate"
);
```

### 2.2 启动带有边界定时器的流程

```java
// 准备流程变量
Map<String, Object> variables = new HashMap<>();
variables.put("ticketId", "TK-12345");
variables.put("priority", "high");
variables.put("assignee", "john.doe");

// 启动流程
String processInstanceId = timerJobService.startTimerProcess(
    "support-ticket-process", 
    "TICKET-12345",
    variables
);

// 查询创建的定时任务
List<Job> timerJobs = timerJobService.getTimerJobsByProcessInstanceId(processInstanceId);
```

### 2.3 动态修改定时任务时间

```java
// 查找特定流程实例的定时任务
List<Job> jobs = managementService.createJobQuery()
    .processInstanceId(processInstanceId)
    .timers()
    .list();

if (!jobs.isEmpty()) {
    Job timerJob = jobs.get(0);
    
    // 延长超时时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    calendar.add(Calendar.HOUR, 2); // 增加2小时
    
    // 更新定时任务时间
    managementService.setJobDuedate(timerJob.getId(), calendar.getTime());
    
    log.info("已将工单 {} 的超时时间延长2小时", ticketId);
}
```

### 2.4 手动触发定时任务执行

```java
// 立即执行定时任务
managementService.executeJob(jobId);
```

## 3. 常见业务场景示例

### 3.1 订单超时自动取消

```java
// 创建订单时启动流程
Map<String, Object> variables = new HashMap<>();
variables.put("orderId", "ORD-67890");
variables.put("customer", "<EMAIL>");
variables.put("amount", 299.99);

// 启动带有30分钟支付超时的流程
ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
    "order-payment-process",
    "ORD-67890",
    variables
);

// 订单状态更新
orderService.updateOrderStatus(orderId, "WAITING_PAYMENT");
```

对应的 BPMN 流程中包含边界定时器事件：

```xml
<bpmn:userTask id="WaitForPayment" name="等待支付">
  <bpmn:extensionElements>
    <camunda:formData>
      <camunda:formField id="orderId" label="订单编号" type="string" />
      <camunda:formField id="amount" label="金额" type="string" />
    </camunda:formData>
  </bpmn:extensionElements>
</bpmn:userTask>

<bpmn:boundaryEvent id="PaymentTimeout" attachedToRef="WaitForPayment">
  <bpmn:timerEventDefinition>
    <bpmn:timeDuration>PT30M</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:boundaryEvent>

<bpmn:sequenceFlow sourceRef="PaymentTimeout" targetRef="CancelOrder" />

<bpmn:serviceTask id="CancelOrder" name="取消订单" 
    camunda:delegateExpression="${orderCancellationDelegate}">
</bpmn:serviceTask>
```

### 3.2 定期生成报表

使用定时器启动事件自动创建报表：

```java
@Component("reportGenerationDelegate")
public class ReportGenerationDelegate implements JavaDelegate {
    
    @Autowired
    private ReportService reportService;
    
    @Override
    public void execute(DelegateExecution execution) throws Exception {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        
        // 生成昨日报表
        String reportId = reportService.generateDailyReport(yesterday);
        
        // 设置流程变量
        execution.setVariable("reportId", reportId);
        execution.setVariable("reportDate", yesterday.toString());
        execution.setVariable("generationTime", LocalDateTime.now().toString());
        
        log.info("已生成日报表 - 日期: {}, 报表ID: {}", yesterday, reportId);
    }
}
```

### 3.3 多级超时提醒

使用多个边界事件实现不同级别的提醒：

```xml
<bpmn:userTask id="ReviewDocument" name="审核文档">
  <!-- 任务配置 -->
</bpmn:userTask>

<!-- 第一级提醒 - 1小时 -->
<bpmn:boundaryEvent id="FirstReminder" attachedToRef="ReviewDocument">
  <bpmn:timerEventDefinition>
    <bpmn:timeDuration>PT1H</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:boundaryEvent>
<bpmn:sequenceFlow sourceRef="FirstReminder" targetRef="SendFirstReminder" />
<bpmn:serviceTask id="SendFirstReminder" 
    camunda:delegateExpression="${reminderDelegate}">
  <bpmn:extensionElements>
    <camunda:inputParameter name="reminderLevel">1</camunda:inputParameter>
  </bpmn:extensionElements>
</bpmn:serviceTask>

<!-- 第二级提醒 - 4小时 -->
<bpmn:boundaryEvent id="SecondReminder" attachedToRef="ReviewDocument">
  <bpmn:timerEventDefinition>
    <bpmn:timeDuration>PT4H</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:boundaryEvent>
<bpmn:sequenceFlow sourceRef="SecondReminder" targetRef="SendSecondReminder" />
<bpmn:serviceTask id="SendSecondReminder" 
    camunda:delegateExpression="${reminderDelegate}">
  <bpmn:extensionElements>
    <camunda:inputParameter name="reminderLevel">2</camunda:inputParameter>
  </bpmn:extensionElements>
</bpmn:serviceTask>
```

## 4. 实用技巧

### 4.1 动态设置定时器持续时间

使用表达式从流程变量设置定时器持续时间：

```xml
<bpmn:boundaryEvent id="DynamicTimer" attachedToRef="UserTask">
  <bpmn:timerEventDefinition>
    <bpmn:timeDuration>${timerDuration}</bpmn:timeDuration>
  </bpmn:timerEventDefinition>
</bpmn:boundaryEvent>
```

启动流程时设置变量：

```java
Map<String, Object> variables = new HashMap<>();
// 设置为30分钟
variables.put("timerDuration", "PT30M");
// 对于高优先级任务，设置为15分钟
if (isPriorityHigh) {
    variables.put("timerDuration", "PT15M");
}

runtimeService.startProcessInstanceByKey("dynamic-timer-process", variables);
```

### 4.2 使用事务边界

确保定时器事件与业务逻辑在同一事务中处理：

```java
@Transactional
public void handleOrderTimeout(String orderId) {
    // 1. 更新订单状态
    orderRepository.updateStatus(orderId, OrderStatus.CANCELLED);
    
    // 2. 退还预留库存
    inventoryService.releaseReservedItems(orderId);
    
    // 3. 记录取消原因
    auditService.logOrderCancellation(orderId, "Payment timeout");
    
    // 4. 发送通知
    notificationService.sendOrderCancellationNotice(orderId);
}
```

### 4.3 批量管理定时任务

查询和管理特定类型的所有定时任务：

```java
// 查找所有等待执行的支付超时定时任务
List<Job> paymentTimeoutJobs = managementService.createJobQuery()
    .processDefinitionKey("order-payment-process")
    .activityId("PaymentTimeout")
    .timers()
    .active()
    .list();

// 例如，在特殊情况下延长所有定时器时间
Calendar newDueDate = Calendar.getInstance();
newDueDate.setTime(new Date());
newDueDate.add(Calendar.HOUR, 1);

for (Job job : paymentTimeoutJobs) {
    managementService.setJobDuedate(job.getId(), newDueDate.getTime());
}
```

## 5. 常见问题与解决方案

### 5.1 定时任务未执行

检查以下几点：
- Job Executor 是否启用
- 数据库中是否存在对应的 Job 记录
- 检查 ACT_RU_JOB 表中的定时任务状态
- 检查 Camunda 日志是否有 Job 执行相关的错误

### 5.2 处理失败的定时任务

设置适当的重试策略并监控失败的作业：

```java
// 查询失败的定时任务
List<Job> failedJobs = managementService.createJobQuery()
    .withException()
    .list();

for (Job failedJob : failedJobs) {
    // 获取异常信息
    String exceptionMessage = failedJob.getExceptionMessage();
    String jobId = failedJob.getId();
    
    // 记录错误
    log.error("定时任务执行失败 - JobID: {}, 错误: {}", jobId, exceptionMessage);
    
    // 设置重试次数
    managementService.setJobRetries(jobId, 3);
}
```

### 5.3 在集群环境中使用定时任务

确保配置正确避免重复执行：

```yaml
camunda:
  bpm:
    job-execution:
      deployment-aware: true  # 部署感知模式
```