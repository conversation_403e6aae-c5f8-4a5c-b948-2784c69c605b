# Camunda 7 与 Spring Boot 3 集成 - 定时任务实现指南

本文档介绍如何在 Camunda 7 与 Spring Boot 3 集成环境中使用 Job Executor 功能创建和管理定时任务。

## 1. 概述

Camunda 7 提供了强大的 Job Executor 功能，用于执行各类异步任务和定时任务。在 Spring Boot 3 环境中，Camunda 的 Job Executor 会自动配置并启动，无需额外配置即可使用。

定时任务主要应用场景：
- 定期执行业务逻辑
- 任务超时处理
- 异步执行长时间运行的操作
- 延迟执行特定任务

## 2. 定时任务类型

Camunda 支持以下几种类型的定时器：

### 2.1 定时器启动事件

- 用于按指定时间规则自动启动流程实例
- 在 BPMN 图中使用定时器启动事件元素
- 示例：每天凌晨 3 点自动执行批处理任务

### 2.2 边界定时器事件

- 附加到活动（如用户任务）上的定时器
- 当定时器触发时，会创建一个并行的执行流
- 可用于实现任务超时处理、提醒等功能
- 示例：用户任务 3 天未完成则自动发送提醒

### 2.3 中间定时器事件

- 在流程执行过程中等待特定时间
- 可用于流程延迟执行、定时检查等场景
- 示例：发送通知后等待 2 小时再继续流程

## 3. 定时器表达式

Camunda 支持以下几种定时器表达式：

### 3.1 固定日期（Date）

```xml
<bpmn:timerEventDefinition>
  <bpmn:timeDate>2023-12-31T23:59:59Z</bpmn:timeDate>
</bpmn:timerEventDefinition>
```

### 3.2 持续时间（Duration）

使用 ISO 8601 格式定义持续时间：

```xml
<bpmn:timerEventDefinition>
  <bpmn:timeDuration>PT30M</bpmn:timeDuration>
</bpmn:timerEventDefinition>
```

- `P`: 时段标识符
- `T`: 时间部分标识符
- `30M`: 30 分钟

其他常见示例：
- `PT5M`: 5 分钟
- `PT1H`: 1 小时
- `P1D`: 1 天
- `P1M`: 1 个月

### 3.3 循环周期（Cycle）

使用 ISO 8601 重复格式或 cron 表达式：

```xml
<!-- 每 10 分钟执行一次 -->
<bpmn:timerEventDefinition>
  <bpmn:timeCycle>R/PT10M</bpmn:timeCycle>
</bpmn:timerEventDefinition>

<!-- 从现在开始，每 10 分钟执行一次，共执行 5 次 -->
<bpmn:timerEventDefinition>
  <bpmn:timeCycle>R5/PT10M</bpmn:timeCycle>
</bpmn:timerEventDefinition>

<!-- 使用 cron 表达式：每天凌晨 3 点执行 -->
<bpmn:timerEventDefinition>
  <bpmn:timeCycle>0 0 3 * * ?</bpmn:timeCycle>
</bpmn:timerEventDefinition>
```

## 4. 实现方法

### 4.1 通过 BPMN 流程定义定时任务

最常用也最推荐的方式是在 BPMN 流程定义中使用定时器元素。Camunda Engine 会根据流程定义自动创建并管理这些定时任务。

步骤：
1. 使用 Camunda Modeler 设计流程，添加定时器事件
2. 部署流程定义到 Camunda 引擎
3. 根据需要启动流程实例或等待定时器启动事件触发

### 4.2 创建 JavaDelegate 实现定时任务逻辑

创建一个实现 `JavaDelegate` 接口的类，在定时器触发时执行业务逻辑：

```java
@Component
public class TimerTaskDelegate implements JavaDelegate {
    
    private static final Logger log = LoggerFactory.getLogger(TimerTaskDelegate.class);

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        // 执行定时任务业务逻辑
        log.info("定时任务执行中 - 流程实例ID: {}", execution.getProcessInstanceId());
        
        // 设置执行结果变量
        execution.setVariable("timerExecuted", true);
        execution.setVariable("executionTime", LocalDateTime.now().toString());
    }
}
```

### 4.3 通过 API 创建和管理定时任务

除了在 BPMN 定义中静态声明定时任务外，还可以通过 Camunda API 动态管理定时任务：

```java
@Service
public class TimerJobService {
    
    @Autowired
    private ManagementService managementService;
    
    @Autowired
    private RuntimeService runtimeService;
    
    /**
     * 获取特定流程实例的定时任务
     */
    public List<Job> getTimerJobsByProcessInstanceId(String processInstanceId) {
        return managementService.createJobQuery()
                .processInstanceId(processInstanceId)
                .timers()
                .list();
    }
    
    /**
     * 立即执行指定的定时任务
     */
    public void executeTimerJob(String jobId) {
        managementService.executeJob(jobId);
    }
    
    /**
     * 修改定时任务执行时间
     */
    public void updateTimerDueDate(String jobId, Date newDueDate) {
        managementService.setJobDuedate(jobId, newDueDate);
    }
}
```

## 5. Job Executor 配置

Spring Boot 3 与 Camunda 7 集成时，Job Executor 会自动配置。可通过 `application.yml` 进行自定义配置：

```yaml
camunda:
  bpm:
    job-execution:
      enabled: true  # 启用 Job Executor
      deployment-aware: true  # 部署感知模式
      core-pool-size: 3  # 核心线程池大小
      max-pool-size: 10  # 最大线程池大小
      lock-time-in-millis: 300000  # 锁定时间（毫秒）
      wait-time-in-millis: 5000  # 等待时间（毫秒）
      max-jobs-per-acquisition: 3  # 每次获取最大作业数
```

## 6. 异步与定时任务

### 6.1 异步执行与 Job Executor

Camunda 中的异步继续（Asynchronous Continuation）也依赖于 Job Executor：

```xml
<bpmn:serviceTask id="Task1" camunda:asyncBefore="true" camunda:asyncAfter="true">
  <!-- 任务定义 -->
</bpmn:serviceTask>
```

- `asyncBefore="true"`: 在任务执行前创建一个异步点
- `asyncAfter="true"`: 在任务执行后创建一个异步点

### 6.2 异步与定时器结合使用

可以将异步执行与定时器结合使用，实现更复杂的业务场景：

1. 将任务标记为异步
2. 添加重试配置处理临时故障
3. 添加定时器边界事件处理持久故障或超时

## 7. 定时任务监控与管理

### 7.1 通过 Camunda Cockpit 监控

Camunda Cockpit 提供 Job 页面，可查看所有定时任务：
- 查看未来将执行的定时任务
- 查看失败的定时任务
- 手动触发执行
- 修改优先级和重试次数

### 7.2 通过 API 监控

使用 ManagementService 查询和管理定时任务：

```java
// 查询所有定时任务
List<Job> timerJobs = managementService.createJobQuery()
    .timers()
    .list();

// 查询失败的定时任务
List<Job> failedJobs = managementService.createJobQuery()
    .withException()
    .list();

// 获取特定的定时任务
Job job = managementService.createJobQuery()
    .jobId(jobId)
    .singleResult();
```

## 8. 最佳实践

1. **使用 BPMN 流程定义定时任务**：尽可能在流程模型中定义定时任务，而不是通过代码创建
2. **合理设置重试策略**：为定时任务配置适当的重试次数和重试间隔
3. **监控定时任务执行**：定期检查失败的定时任务并进行处理
4. **考虑集群环境**：在集群环境中，确保 Job Executor 配置正确，避免任务重复执行
5. **使用业务键关联**：使用业务键（BusinessKey）关联流程实例与业务对象
6. **异常处理**：在 JavaDelegate 中妥善处理异常，避免定时任务频繁失败

## 9. 常见问题

1. **定时任务未执行**
   - 检查 Job Executor 是否启用
   - 检查定时器表达式是否正确
   - 查看数据库中的 ACT_RU_JOB 表

2. **定时任务执行失败**
   - 检查 JavaDelegate 实现是否有异常
   - 查看日志中的错误信息
   - 检查 ACT_RU_JOB 表中的 EXCEPTION_MSG_ 字段

3. **集群环境中的问题**
   - 确保集群节点时间同步
   - 配置 deployment-aware 属性避免重复执行
   - 考虑使用独占锁防止并发问题

## 10. 参考资料

- [Camunda 官方文档 - Timers](https://docs.camunda.org/manual/7.17/reference/bpmn20/events/timer-events/)
- [Camunda 官方文档 - Job Executor](https://docs.camunda.org/manual/7.17/user-guide/process-engine/the-job-executor/)
- [Spring Boot 与 Camunda 集成](https://docs.camunda.org/manual/7.17/user-guide/spring-boot-integration/)
- [ISO 8601 持续时间格式](https://en.wikipedia.org/wiki/ISO_8601#Durations)
- [Cron 表达式生成器](https://www.freeformatter.com/cron-expression-generator-quartz.html) 