# Camunda Server Nacos 集成支持

本模块提供了将Camunda服务注册到Nacos服务发现和配置中心的功能。

## 功能特性

- 自动将Camunda服务注册到Nacos服务发现中心
- 支持从Nacos配置中心读取配置
- 基于Spring Cloud Alibaba Nacos，版本2023.0.3.2

## 使用方法

1. 在你的Camunda服务模块中引入本模块依赖：

```kotlin
dependencies {
    implementation(project(":camunda-server-nacos-support"))
    // 其它依赖
}
```

2. 在你的应用配置文件（application.yml 或 application.properties）中添加Nacos配置：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848  # Nacos服务器地址
        namespace: your-namespace    # 可选，Nacos命名空间ID
      config:
        server-addr: localhost:8848  # Nacos配置中心地址
        file-extension: yaml         # 配置文件扩展名
```

3. 启动你的应用，它将自动注册到Nacos服务发现中心

## 配置项说明

| 配置项 | 说明 | 默认值 |
|-------|------|-------|
| spring.cloud.nacos.enabled | 是否启用Nacos集成 | true |
| spring.cloud.nacos.discovery.server-addr | Nacos服务器地址 | localhost:8848 |
| spring.cloud.nacos.discovery.namespace | Nacos命名空间ID | - |
| spring.cloud.nacos.config.server-addr | Nacos配置中心地址 | localhost:8848 |

## 注意事项

- 确保Nacos服务器已经启动并可访问
- 在开发环境中，可以使用Docker快速启动Nacos服务：
  ```bash
  docker run --name nacos -e MODE=standalone -p 8848:8848 -d nacos/nacos-server:latest
  ``` 