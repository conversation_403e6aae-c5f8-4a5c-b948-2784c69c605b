package com.open_care.camunda.nacos.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Configuration;

/**
 * Nacos服务发现和配置中心自动配置类
 */
@Configuration
@EnableDiscoveryClient
@ConditionalOnProperty(name = "spring.cloud.nacos.enabled", matchIfMissing = true)
public class NacosAutoConfiguration {
    // 此配置类将自动启用Nacos服务发现
} 