{"properties": [{"name": "spring.cloud.nacos.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用Nacos服务发现和配置中心（默认启用）", "defaultValue": true}, {"name": "spring.cloud.nacos.discovery.server-addr", "type": "java.lang.String", "description": "Nacos服务器地址，格式为 host:port", "defaultValue": "localhost:8848"}, {"name": "spring.cloud.nacos.discovery.namespace", "type": "java.lang.String", "description": "Nacos命名空间ID，用于隔离不同环境"}, {"name": "spring.cloud.nacos.config.server-addr", "type": "java.lang.String", "description": "Nacos配置中心服务器地址", "defaultValue": "localhost:8848"}]}