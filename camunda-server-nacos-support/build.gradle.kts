plugins {
    id("java")
    id("org.springframework.boot") apply false
}

dependencies {
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery")
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config")
    implementation("org.springframework.boot:spring-boot-starter")
    implementation("com.alibaba.nacos:logback-adapter")
    implementation("org.slf4j:slf4j-api")

    testImplementation("org.junit.jupiter:junit-jupiter")
}

