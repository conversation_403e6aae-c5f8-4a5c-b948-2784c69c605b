#networks:
#  ci_cd_default:  # 自定义网络名称
#    external: true  # 声明为外部网络
#    name: ci_cd_default  # 指定已存在的网络名称
services:
  camunda:
    image: mcr.microsoft.com/devcontainers/java:17-bookworm
#    container_name: dev-container-camunda
#    volumes:
#      - ~/.gradle:/home/<USER>/.gradle
#      - ..:/workspace/camunda-plugin:cached
    command: sleep infinity

    network_mode: host
    environment:
      - TZ=Asia/Shanghai
#    ports:
#      - "8382:8382"
#    labels:
#      - "com.docker.compose.project=dev-container"
    restart: unless-stopped
#    networks:
#      - ci_cd_default
