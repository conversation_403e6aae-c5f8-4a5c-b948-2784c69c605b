{
  "name": "Camunda Plugin Development",
  "dockerComposeFile": "docker-compose.yml",
  "service": "camunda",
//  "remoteUser": "devcontainer",
//  "workspaceFolder": "/home/<USER>",
  "features": {
    "ghcr.io/devcontainers/features/java:latest": {
      "installGradle": true,
      "gradleVersion": "8.7",
      "installMaven": true,
      "version": "17"
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "moby": true
    },
    "ghcr.io/devcontainers/features/git:1": {
      "version": "latest",
      "ppa": false
    },
//    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "vscjava.vscode-java-pack",
        "vscjava.vscode-gradle",
        "redhat.vscode-xml",
        "ms-azuretools.vscode-docker"
      ],
      "settings": {
        "java.configuration.updateBuildConfiguration": "automatic",
        "java.compile.nullAnalysis.mode": "automatic",
        "java.import.gradle.version": "8.7",
        "java.import.gradle.user.home": "/home/<USER>/.gradle",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.organizeImports": true
        }
      }
    },
    "jetbrains": {
      "intellij": {
        "edition": "ULTIMATE",
        "plugins": [
          "com.intellij.gradle",
          "org.jetbrains.plugins.gradle",
          "com.intellij.java",
          "com.github.copilot",
          "com.intellij.ai.codeCompletion",
          "com.intellij.ai.chat",
          "com.intellij.ai.assistant"
        ]
      }
    }
  },
  "forwardPorts": [],
  "postCreateCommand": "mkdir -p ~/.m2 && cp .devcontainer/maven-settings.xml ~/.m2/settings.xml"
}