# Camunda Modeler 扩展属性使用指南

本文档介绍如何在Camunda Modeler中设置扩展属性，以控制任务和执行事件的触发和处理方式。

## 基本概念

在Camunda流程定义中，可以通过扩展属性(Extension Properties)控制以下行为：

1. 是否触发特定事件
2. 是否同步处理特定事件
3. 超时事件的定时配置
4. 指定事件处理器

扩展属性可以在Camunda Modeler中设置，也可以在运行时通过变量进行设置。**变量的优先级高于扩展属性**。

### 属性简写机制

系统支持布尔类型开关属性的简写形式：

- `task.listen=true` 等同于 `task.listen.enable=true` 
- `execution.listen.start=true` 等同于 `execution.listen.start.enable=true`

简写机制**仅适用于布尔类型的enable开关属性**，不适用于其他类型如sync、type等。

#### 简写规则

1. 如果属性名以路径结尾且值为布尔类型，系统会自动将其视为该路径的`enable`属性
2. 解析属性时，完整形式的优先级高于简写形式
3. 搜索顺序：先查找完整路径（如`task.listen.complete.enable`），找不到则查找简写形式（如`task.listen.complete`）

## 任务事件控制属性

### 全局控制

- `task.listen.enable` - 控制是否触发所有任务事件的通用开关（默认：true）
- `task.listen.sync` - 控制所有任务事件是否同步调用（默认：false）

**简写形式**: 可以使用 `task.listen=true` 替代 `task.listen.enable=true`

### 特定事件控制

- `task.listen.create.enable` - 控制是否触发创建事件
- `task.listen.create.sync` - 控制创建事件是否同步调用
- `task.listen.assignment.enable` - 控制是否触发分配事件
- `task.listen.assignment.sync` - 控制分配事件是否同步调用
- `task.listen.complete.enable` - 控制是否触发完成事件
- `task.listen.complete.sync` - 控制完成事件是否同步调用
- `task.listen.delete.enable` - 控制是否触发删除事件
- `task.listen.delete.sync` - 控制删除事件是否同步调用
- `task.listen.update.enable` - 控制是否触发更新事件
- `task.listen.update.sync` - 控制更新事件是否同步调用
- `task.listen.timeout.enable` - 控制是否触发超时事件
- `task.listen.timeout.sync` - 控制超时事件是否同步调用
- `task.listen.handler` - 指定处理该任务所有事件的处理器标识符或名称 (可选)

**简写示例**: `task.listen.complete=true` 等同于 `task.listen.complete.enable=true`

### 超时事件的特殊控制

超时事件有三种类型，每种类型都可以单独控制是否启用、同步方式以及超时配置：

#### 基本控制

- `task.listen.timeout.enable` - 控制普通超时事件（默认：false）
- `task.listen.timeout.sync` - 控制普通超时事件是否同步调用（默认：false）
- `task.listen.timeout.assignment.enable` - 控制申领超时事件（默认：false）
- `task.listen.timeout.assignment.sync` - 控制申领超时事件是否同步调用（默认：false）
- `task.listen.timeout.complete.enable` - 控制完成超时事件（默认：false）
- `task.listen.timeout.complete.sync` - 控制完成超时事件是否同步调用（默认：false）

**简写示例**: 
- `task.listen.timeout=true` 等同于 `task.listen.timeout.enable=true`
- `task.listen.timeout.assignment=true` 等同于 `task.listen.timeout.assignment.enable=true`

#### 超时类型和表达式配置

每种超时类型都可以设置定时器类型和表达式：

- `task.listen.timeout.type` - 普通超时定时器类型（可选值：date、duration、cycle，默认：duration）
- `task.listen.timeout.express` - 普通超时定时器表达式（默认：P1D，表示1天）
- `task.listen.timeout.assignment.type` - 申领超时定时器类型（默认：duration）
- `task.listen.timeout.assignment.express` - 申领超时定时器表达式（默认：PT8H，表示8小时）
- `task.listen.timeout.complete.type` - 完成超时定时器类型（默认：duration）
- `task.listen.timeout.complete.express` - 完成超时定时器表达式（默认：PT16H，表示16小时）

支持的定时器类型说明：
- `date` - 具体日期时间，使用ISO 8601日期格式（如：2025-04-14T12:00:00Z）
- `duration` - 持续时间，使用ISO 8601持续时间格式（如：PT5M表示5分钟，P1D表示1天）
- `cycle` - 循环周期，使用ISO 8601重复间隔格式（如：R3/PT10H表示每10小时执行一次，共执行3次）

#### 多组超时配置

除了基本的超时配置外，还可以通过数字后缀创建多组超时定时任务：

```
task.listen.timeout.0=true
task.listen.timeout.0.type=duration
task.listen.timeout.0.express=PT30M

task.listen.timeout.1=true
task.listen.timeout.1.type=duration
task.listen.timeout.1.express=PT1H
```

配置优先级从高到低为：
1. 特定类型超时配置（如task.listen.timeout.assignment.express）
2. 数字后缀超时配置（如task.listen.timeout.0.express）
3. 统一超时配置（如task.listen.timeout.express）

## 执行事件控制属性

### 全局控制

- `execution.listen.enable` - 控制是否触发所有执行事件的通用开关（默认：false）
- `execution.listen.sync` - 控制所有执行事件是否同步调用（默认：false）

**简写使用**: 可以使用`execution.listen=true`代替`execution.listen.enable=true`。
在解析属性时，如果同时存在简写和完整形式（例如同时设置了`execution.listen=true`和`execution.listen.enable=false`），
将优先使用完整形式的配置（即`execution.listen.enable=false`生效）。

### 特定事件控制

- `execution.listen.start.enable` - 控制是否触发开始事件
- `execution.listen.start.sync` - 控制开始事件是否同步调用
- `execution.listen.end.enable` - 控制是否触发结束事件
- `execution.listen.end.sync` - 控制结束事件是否同步调用
- `execution.listen.take.enable` - 控制是否触发经过事件
- `execution.listen.take.sync` - 控制经过事件是否同步调用
- `execution.listen.handler` - 指定处理该执行所有事件的处理器标识符或名称 (可选)

**简写和优先级示例**:

```
// 以下两个配置同时存在时，第二个配置优先级更高
execution.listen.start = true          // 简写形式
execution.listen.start.enable = false   // 完整形式，优先级更高

// 对于不同类型属性，简写机制不适用
execution.listen.start.sync = true     // 无简写形式
```

## 在Camunda Modeler中设置扩展属性

1. 在BPMN图表中选择一个用户任务(User Task)或其他流程元素
2. 在属性面板中，找到"Extensions"部分（通常在底部）
3. 点击"+"按钮添加扩展属性
4. 输入属性名称（如"task.listen.complete.enable"或简写"task.listen.complete"）和值（"true"或"false"）

![Camunda Modeler扩展属性设置示例](https://camunda.com/wp-content/uploads/2021/06/extension-properties.png)

## 在运行时设置变量

可以在流程实例启动时或任务创建/执行时通过API设置变量，例如：

```java
// 设置全局监听配置 - 完整形式
runtimeService.setVariable(processInstanceId, "task.listen.enable", false);
// 或使用简写形式
runtimeService.setVariable(processInstanceId, "task.listen", false);

// 设置特定事件同步处理
taskService.setVariable(taskId, "task.listen.complete.sync", true);

// 设置任务超时配置 - 完整形式
taskService.setVariable(taskId, "task.listen.timeout.enable", true);
// 或使用简写形式
taskService.setVariable(taskId, "task.listen.timeout", true);
taskService.setVariable(taskId, "task.listen.timeout.express", "PT30M"); // 30分钟后超时
```

## 优先级规则

对于同一个控制项，系统按照以下优先级获取配置：

1. 变量中的配置（优先级最高）
2. 特定事件的特定设置 - 完整形式（如"task.listen.complete.enable"）
3. 特定事件的特定设置 - 简写形式（如"task.listen.complete"）
4. 通用的同类设置 - 完整形式（如"task.listen.enable"）
5. 通用的同类设置 - 简写形式（如"task.listen"）
6. 默认值（如同步调用通常默认为false）

## 事件处理机制

Camunda Plugin项目使用基于注解的事件监听器替代传统的插件方式，处理流程涉及：

1. **事件捕获**：系统通过 `ReactorTaskEventListener` 和 `ReactorExecutionEventListener` 捕获Camunda引擎的原生事件
2. **事件过滤**：根据扩展属性配置，决定是否处理特定事件
3. **同步/异步处理**：根据配置决定同步处理或异步处理事件
4. **超时处理**：创建、申领和完成事件会触发相应的超时定时任务创建

### 事件处理流程

```
事件发生 → 事件监听器捕获 → 检查扩展属性 → 创建事件上下文 → 
根据同步/异步配置处理 → 发送到业务处理器 → 执行业务逻辑
```

对于超时事件处理流程：

```
创建/申领事件 → 检查超时配置 → 创建定时任务 → 定时器触发 → 
生成超时事件 → 发送到业务处理器 → 执行业务逻辑
```

## 实际使用示例

### 场景1：同步处理完成事件，忽略分配事件

```
task.listen.complete.sync = true
task.listen.assignment.enable = false
// 或使用简写
task.listen.assignment = false
```

### 场景2：仅处理任务的创建和完成事件，忽略其他所有事件

```
task.listen.enable = false
// 或使用简写
task.listen = false
task.listen.create.enable = true
task.listen.complete.enable = true
// 或使用简写
task.listen.create = true
task.listen.complete = true
```

### 场景3：同步处理所有执行事件

```
execution.listen.enable = true
execution.listen.sync = true
// 或使用简写
execution.listen = true
```

### 场景4：只处理申领超时，忽略其他超时类型

```
task.listen.timeout.enable = false
// 或使用简写
task.listen.timeout = false
task.listen.timeout.assignment.enable = true
// 或使用简写
task.listen.timeout.assignment = true
task.listen.timeout.complete.enable = false
// 或使用简写
task.listen.timeout.complete = false
```

### 场景5：设置不同的超时时间

```
task.listen.timeout.enable = true
// 或使用简写
task.listen.timeout = true
task.listen.timeout.express = "PT4H"  // 普通超时4小时
task.listen.timeout.assignment.enable = true
// 或使用简写
task.listen.timeout.assignment = true
task.listen.timeout.assignment.express = "PT2H"  // 申领超时2小时
task.listen.timeout.complete.enable = true
// 或使用简写
task.listen.timeout.complete = true
task.listen.timeout.complete.express = "PT8H"  // 完成超时8小时
```

### 场景6：使用循环超时检查

```
task.listen.timeout.enable = true
// 或使用简写
task.listen.timeout = true
task.listen.timeout.type = "cycle"
task.listen.timeout.express = "R3/PT1H"  // 每小时检查一次，共3次
```

### 场景7：使用多组超时配置

```
// 第一组超时：30分钟后发送提醒
task.listen.timeout.0 = true  // 简写形式
task.listen.timeout.0.type = duration
task.listen.timeout.0.express = PT30M

// 第二组超时：2小时后自动升级
task.listen.timeout.1 = true  // 简写形式
task.listen.timeout.1.type = duration
task.listen.timeout.1.express = PT2H

// 第三组超时：8小时后自动分配给管理员
task.listen.timeout.2 = true  // 简写形式
task.listen.timeout.2.type = duration
task.listen.timeout.2.express = PT8H
```

### 场景8: 指定自定义处理器

```
// 为特定任务指定处理器
task.listen.handler = approvalTaskHandler

// 为特定执行流程指定处理器
execution.listen.handler = specialProcessHandler
```

## 高级功能：前缀过滤属性

为了避免属性名称中使用点分隔(.dot)导致的嵌套结构冲突，项目中的 `ExtensionPropertyUtils` 提供了前缀过滤功能。通过 `getAllPropertiesAsJsonWrapper(variableScope, prefix)` 方法，可以只获取指定前缀的属性，并封装到 `PropertyJsonWrapper` 对象中，从而简化属性访问。

### 使用前缀过滤获取JSON包装器

在代码中，可以先获取指定前缀（如 "task.listen"）的 `PropertyJsonWrapper` 对象，然后使用相对路径访问内部属性：

```java
// 1. 获取指定前缀的属性包装器
String taskListenPrefix = ExtensionPropertyUtils.compose(EventListenConst.TASK, EventListenConst.LISTEN);
PropertyJsonWrapper propsWrapper = ExtensionPropertyUtils.getAllPropertiesAsJsonWrapper(delegateTask, taskListenPrefix);

// 2. 使用相对路径访问属性
// 例如，判断 'complete' 事件是否启用 (访问 task.listen.complete.enable)
boolean isCompleteEnabled = propsWrapper.getBooleanByPrefixPathSuffix(
    taskListenPrefix,       // 完整前缀
    EventListenConst.ENABLE,  // 后缀 (enable)
    true,                  // 默认值
    TaskListenEventType.COMPLETE.getEventName() // 中间路径 (complete)
);

// 例如，获取任务事件处理器名称 (访问 task.listen.handler)
String taskHandler = propsWrapper.getPropWithDefaultValue(
    taskListenPrefix,      // 完整前缀
    EventListenConst.HANDLER, // 属性名 (handler)
    null                   // 默认值
);

// 例如，获取第 0 组超时配置的表达式 (访问 task.listen.timeout.0.express)
String timeoutExpress0 = propsWrapper.getPropWithDefaultValue(
    taskListenPrefix,           // 完整前缀
    TaskTimerProp.EXPRESS.getName(), // 属性名 (express)
    EventListenConst.TIMEOUT,     // 中间路径 (timeout)
    "0"                       // 中间路径 (0)
);
```

这种方式具有以下优点：
1. 避免了属性名中点分隔导致的嵌套层级冲突问题。
2. 提高了属性访问的效率，因为只处理了特定前缀的属性。
3. 简化了属性路径的构建，可以使用相对路径或组合路径访问。

## 集成到现有项目中

### 1. 添加依赖

确保在项目中添加了必要的依赖：

```gradle
// 添加Camunda插件依赖
implementation(project(":camunda-common"))
implementation("org.camunda.bpm.springboot:camunda-bpm-spring-boot-starter")
implementation("org.camunda.bpm.extension.reactor:camunda-bpm-reactor-spring")
```

### 2. 配置监听器

添加配置类启用事件监听器：

```java
@Configuration
@ComponentScan("com.open_care.camunda.event.listener")
public class CamundaEventConfig {
    // 配置代码（如有必要）
}
```

### 3. 创建自定义事件处理器

```java
@Component
public class CustomTaskEventProcessor implements TaskEventProcessor {
    
    @Override
    public void process(TaskEventContext context) {
        TaskEvent event = context.getEvent();
        String eventName = event.getEventName();
        
        // 根据事件类型执行不同业务逻辑
        if ("create".equals(eventName)) {
            // 任务创建逻辑
        } else if ("complete".equals(eventName)) {
            // 任务完成逻辑
        }
        
        // 获取任务属性
        String customProperty = event.getPropsWrapper().getProp(
            "task.listen", "customProp");
    }
}
```

## 最佳实践

1. **使用变量覆盖默认配置**：针对特定任务或流程实例进行配置，使用变量而非修改流程定义中的扩展属性。

2. **超时配置分层**：对于超时事件，按照特定类型 > 数字后缀 > 统一配置的优先级设置，便于管理和维护。

3. **保持配置一致性**：在同一个项目中保持配置命名和结构的一致性，避免不同的流程使用不同的配置模式。

4. **命名约定**：所有配置都采用小写字母和点分隔的命名方式，如`task.listen.create.enable`。

5. **日志记录**：在开发环境中启用debug日志，查看属性解析和事件处理的详细过程：
   ```properties
   logging.level.com.open_care.camunda.utils.ExtensionPropertyUtils=DEBUG
   logging.level.com.open_care.camunda.event.listener=DEBUG
   ```

6. **超时任务分类**：根据业务需求区分使用普通超时、申领超时和完成超时，每种类型设置合理的超时时间。

7. **开发自定义处理器**：针对特定业务需求开发专门的事件处理器，并使用`task.listen.handler`进行指定。

8. **定时器类型选择**：
   - 使用`duration`类型处理相对时间的超时（如2小时后）
   - 使用`date`类型处理绝对时间的超时（如具体日期时间）
   - 使用`cycle`类型实现重复提醒
   
9. **定期清理过期定时任务**：配置Camunda的任务清理job或自定义逻辑，避免无效定时任务积累。

10. **避免过多同步事件**：尽量减少同步事件处理，避免阻塞工作流执行。关键节点可以使用同步处理，大多数情况推荐异步处理。

11. **选择合适的属性表示方式**：虽然简写形式（如`task.listen=true`）更简洁，但在需要明确表达意图的场景下，使用完整形式（如`task.listen.enable=true`）更清晰。在同一项目中保持一致的风格。
