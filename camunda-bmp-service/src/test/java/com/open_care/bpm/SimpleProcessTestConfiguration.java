package com.open_care.bpm;

import com.open_care.api.client.ConfigurationRemoteService;
import com.open_care.api.client.GatewayRemoteService;
import com.open_care.api.client.MessageRemoteService;
import com.open_care.bpm.config.CamundaProperties;
import com.open_care.bpm.script.groovy.OcGroovyScriptExtendPlugin;
import com.open_care.bmp.script.groovy.context.OpenCareContext;
import com.open_care.bpm.script.groovy.context.service.*;
import com.open_care.bpm.util.SpringContextUtil;
import com.open_care.util.json.JacksonJsonConvert;
import com.open_care.util.json.JsonConverter;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.mockito.Mockito;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.r2dbc.R2dbcAutoConfiguration;
import org.springframework.boot.autoconfigure.r2dbc.R2dbcTransactionManagerAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 简化的测试配置类
 * 用于测试Camunda流程和Groovy脚本功能
 */
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {
        RedisAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class,
        R2dbcAutoConfiguration.class,
        R2dbcTransactionManagerAutoConfiguration.class
})
@ComponentScan(basePackages = {
        "com.open_care.bpm.script.groovy.context",
        "org.camunda",
})
@Log4j2
public class SimpleProcessTestConfiguration {

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public ConfigurationRemoteService configurationRemoteService() {
        return Mockito.mock(ConfigurationRemoteService.class);
    }

    @Bean
    public GatewayRemoteService gatewayRemoteService() {
        return Mockito.mock(GatewayRemoteService.class);
    }

    @Bean
    public MessageRemoteService messageRemoteService() {
        return Mockito.mock(MessageRemoteService.class);
    }

    @Bean
    @ConfigurationProperties(prefix = "camunda.bpm")
    public CamundaProperties camundaProperties(){
        return new CamundaProperties();
    }

    /**
     * 注册 OcGroovyScriptExtendPlugin 插件
     * 该插件会自动被 Camunda 引擎加载并应用
     */
    @Bean
    public OcGroovyScriptExtendPlugin ocGroovyScriptExtendPlugin(OpenCareContext openCareContext) {
        return new OcGroovyScriptExtendPlugin(openCareContext);
    }
} 