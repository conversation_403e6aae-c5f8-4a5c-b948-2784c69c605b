package com.open_care.camunda.pulsar.connector.plugin.config;

import lombok.extern.log4j.Log4j2;
import org.camunda.connector.plugin.impl.ConnectorProcessEnginePlugin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;



/**
 * <AUTHOR>
 * @date :2025/4/8
 */
@Configuration
@Log4j2
public class CamundaPulsarConnectorPluginConfig {


    @Bean
    public ConnectorProcessEnginePlugin connectorProcessEnginePlugin(){
        return new ConnectorProcessEnginePlugin();
    }

}
