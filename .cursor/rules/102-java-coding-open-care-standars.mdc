---
description: Open Care Java编码规范
globs: "**/*.java"
alwaysApply: true
---
# Open Care Java编码规范

## DTO 定义规范
- 所有DTO类必须继承 `com.open_care.dto.IBaseDTO` 接口
- 必须在DTO类上添加 注解 `com.open_care.annotation.OcClass`
- 注解的`title`属性必须设置，用于提供该类的中文描述信息 `OcClass`
- 所有DTO属性应添加 `com.open_care.annotation.OcColumn` 注解
- `OcColumn`注解的`title`属性必须设置，用于提供属性的中文名称或描述

### 代码示例
``` java
import com.open_care.dto.IBaseDTO;
import com.open_care.annotation.OcClass;
import com.open_care.annotation.OcColumn;
import lombok.Data;

@Data
@OcClass(title = "患者信息")
public class PatientDTO implements IBaseDTO {

    @OcColumn(title = "患者ID")
    private Long id;

    @OcColumn(title = "患者姓名")
    private String name;

    @OcColumn(title = "年龄")
    private Integer age;

    @OcColumn(title = "联系电话")
    private String phone;

    @OcColumn(title = "就诊状态")
    private VisitStatusEnum status;
}
```
## Entity 定义规范
- 项目使用**Hibernate**作为ORM框架，不使用MyBatis
- 实体类应当正确使用JPA注解进行映射
- 实体类应遵循JPA实体设计规范
- 使用`@Entity`标记实体类
- 使用`@Table`指定表名
- 使用`@Id`和`@GeneratedValue`定义主键
- 使用`@Column`定义列属性
- 正确设置实体间的关联关系

### 代码示例
``` java
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "t_patient")
public class Patient {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", length = 50, nullable = false)
    private String name;

    @Column(name = "age")
    private Integer age;

    @Column(name = "phone", length = 20)
    private String phone;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private VisitStatusEnum status;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_id")
    private Department department;
}
```
## Enum 定义规范
- 所有业务枚举必须继承 接口 `com.open_care.core.IShowLabelEnum`
- 必须定义以下两个关键属性：
    - `dbValue`：数据库存储值，通常为枚举的name或其他唯一标识符
    - `label`：枚举的中文描述文本，用于前端展示
- 推荐使用Lombok的`@Getter`和`@AllArgsConstructor`简化代码
- 需要正确实现`getDbValue()`和`getLabel()`方法
- 提供必要的静态工具方法便于枚举值转换

### 代码示例
``` java
import com.open_care.core.IShowLabelEnum;
import lombok.Getter;
import lombok.AllArgsConstructor;

@Getter
@AllArgsConstructor
public enum VisitStatusEnum implements IShowLabelEnum {

    WAITING("WAITING", "候诊中"),
    CONSULTING("CONSULTING", "就诊中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELED("CANCELED", "已取消");

    private final String dbValue;
    private final String label;

    @Override
    public String getDbValue() {
        return this.dbValue;
    }

    @Override
    public String getLabel() {
        return this.label;
    }

    /**
     * 根据数据库值获取枚举实例
     */
    public static VisitStatusEnum getByDbValue(String dbValue) {
        if (dbValue == null) {
            return null;
        }

        for (VisitStatusEnum status : values()) {
            if (status.getDbValue().equals(dbValue)) {
                return status;
            }
        }
        return null;
    }
}
```
## 非空处理
- DTO和Entity中的必填字段应通过注解明确标记
- 枚举转换方法应妥善处理空值情况

## 版本管理
- 对于关键业务实体，考虑添加版本控制字段
