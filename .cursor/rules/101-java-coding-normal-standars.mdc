---
description: Java编码规范
globs: "**/*.java"
alwaysApply: true
---
# Java通用编码标准

## 代码长度
- 方法长度不应超过30行（不包括空格和注释）
- 超过30行的方法应提取为多个子方法

## 代码结构
- 提取代码块时，以完整功能块为单位
- 对于相似度高的代码，应抽象为通用方法
- 避免深层嵌套的条件语句，最多不超过2层，将内层嵌套抽成方法，或者优化结构， 减少嵌套
- 使用守卫语句(guard clauses)替代嵌套条件

## 常量使用
- 除非必要，所有魔法值均提取成静态变量，所有魔法值提取成内部静态属性，共享则声明为公共的，如果常量与当前类没有关联，则提取成 xxxConsts 类中的常量

## 方法调用
- 除非需要，不使用 map 存储数据作为接口参数或者返回值、而是 对象

## 命名与声明规范
### 类命名规范
- 不同服务间需要传递的类命名为 `xxxDTO`，放在 `dto` 目录下，根据用途加上子包
- 仅本项目内部使用的类命名为 `xxxBO`，放在 `bo` 目录下，根据用途加上子包
- 如果某个类仅被内部使用则声明为内部类，内部类超过30行（不含空格和注释）应声明为外部类
- 枚举类名应以`Enum`后缀结尾，表示其用途

## 方法命名规范
- 查询数据的方法 find 开头 比如 `findUserInfoByUserIds`
- 生成对象的方法 generate 开头比如 `generateUserByUserDTO`
- 尝试执行操作，并且方法不抛出异常或者已经处理异常 `tryGetUserInfos`如果执行失败返回默认值 比如 `null`

### 属性声明规范
- BO/DTO/实体类中的 Date 属性需加上 `@JsonFormat` 注解：
  ```java
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") // 仅保留年月日
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") // 保留时分秒
  ```
- 使用 lombok 注解简化代码：
  ```java
  @Data
  @NoArgsConstructor
  @AllArgsConstructor // 有属性时添加
  @SuperBuilder
  @EqualsAndHashCode(callSuper = true) // 有父类时添加
  ```
- 属性有默认值时，添加 `@Builder.Default` 注解

## 对象转换规范
- 使用 MapStruct 工具实现对象转换
- Mapper 接口放置在 `mapper` 包下，如无则创建
- 相同或类似的类转换应放在同一个 Mapper 接口中
- Mapper 类应忽略未映射的属性
- Mapper 类的 componentMode 设置为 spring
- 初始化完成后将实例设置到静态 INSTANCE 属性中，方便静态方法调用

## 工具类使用规范
- 字符串操作：isBlank isNotBlank isEmpty isNotEmpty subString 字符拼接format 优先使用 `StrUtil`（hutool）
- 对象操作：isNull isNotNUll equals 优先使用 `ObjectUtil`（hutool）
- 集合操作：isEmpty isNotEmpty 优先使用 `CollUtil`（hutool）
- Map操作：优先使用 `cn.hutool.core.map.MapUtil`（hutool）
- 反射操作：getFields getField 优先使用 `ReflectUtil`/`FieldUtil`（hutool）
- 文件操作：touch mkdir read write 优先使用 `FileUtil`（hutool）
- IO操作：优先使用 `IOUtil`（hutool）
- 不使用 Java 低版本的 `javax` 包，改用 `jakarta` 包

## 异常处理规范
- 使用自定义异常类表达业务异常
- 处理可能出现的空指针异常
- 异常信息应包含上下文信息，便于定位问题
- 避免捕获异常后不处理或仅打印堆栈
- 使用 try-with-resources 处理资源关闭

## 日志规范
- 使用 @Log4j2 作为日志门面，使用 lombok `@Log4j2` 注解引入 log 变量
- 日志级别合理使用：
  - ERROR：影响系统运行的错误
  - WARN：潜在问题
  - INFO：重要操作
  - DEBUG：调试信息
- 代码涉及到状态变更、或者比较容易出现错误，就上日志以供后续定位问题
- 日志信息应包含关键参数和上下文
- 敏感信息（密码、令牌等）不应记录到日志

## 导入规范
- 所有的导入都使用完整路径，不使用通配符 `*`
- 不导入静态常量，而是导入静态类，直接使用 `类.常量` 的方式
- 自动去掉不被使用的导入语句

## 异常处理
- 使用自定义异常类表达业务异常
- 异常信息应包含上下文信息，便于定位问题
- 避免捕获异常后不处理或仅打印堆栈
- 使用try-with-resources处理资源关闭

## 注释规范
- 类、公共方法应有Javadoc注释
- 复杂逻辑应有适当的行内注释
- 注释应解释"为什么"而非"是什么"
- 避免无意义或误导性的注释

## 类引入规范
- 所有的引入都是用完整路径，不能使用 *
- 不引入静态常量，而是引入静态类，直接使用 类.常量 的方式
- 如果某个类仅被内部使用则声明为内部类，内部类排除空格和注释后一旦超过30行，则声明为外部类（公共/私有/受保护/默认视情况而定）


## 耗时调用规范
- 如果一个耗时操作（比如数据库、IO操作）在循环中进行，如果可以，应该想办法将耗时操作放到循环之前统一进行，数据库查询可以先统一查询构造成map list context类等，save操作可以替换成 batchSave
