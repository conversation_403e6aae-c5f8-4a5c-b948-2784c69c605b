---
description: Java编码规范总纲
globs: "**/*.java"
alwaysApply: true
---
# Java编码规范总纲

本文档是Java编码规范的总纲，引用了以下具体规范文件：

## 引用规范

1. [Java通用编码标准](./.cursor/rules/101-java-coding-normal-standars.mdc) - 适用于所有Java项目的通用编码标准
2. [Open Care Java编码规范](./.cursor/rules/102-java-coding-open-care-standars.mdc) - 适用于Open Care项目的特定编码规范

## 规范优先级

1. 项目特定规范（如Open Care规范）优先于通用规范
2. 当特定规范未提及某方面要求时，应遵循通用规范

## 规范应用范围

- 所有新开发的Java代码
- 对现有代码的重构和修改
- 代码审查过程

## 规范执行

- 开发人员应在提交代码前确保符合规范要求
- 代码审查过程中应检查规范遵循情况
- CI/CD流程中应包含规范检查步骤

## 规范更新

- 规范文件可根据项目需求和最佳实践的演进进行更新
- 规范更新应通知所有开发人员并提供必要的培训
